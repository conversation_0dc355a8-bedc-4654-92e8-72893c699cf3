# IAM Service

## Configuring keycloak

1. Go to the keycloak admin console
2. Select the appropriate realm.
3. Go to the clients tab.
4. Select admin-cli client.
5. In settings tab, capability config section, enable Client authentication and authorization.
6. Ensure only service accounts roles is enabled in the authentication flow.
7. Click Save.
8. Go to service account roles tab.
9. <PERSON>lick assign role.
10. Change filter by realm-roles to filter by client.
11. Filter roles for **realm-management**.
12. Select **manage-users**.

## Setting environment variables for deployment

```yaml
QUARKUS_KEYCLOAK_ADMIN_CLIENT_SERVER_URL: "https://keycloak.thewalnut.ai"
QUARKUS_KEYCLOAK_ADMIN_CLIENT_REALM: "dev-vega"
QUARKUS_KEYCLOAK_ADMIN_CLIENT_CLIENT_ID: "admin-cli"
QUARKUS_KEYCLOAK_ADMIN_CLIENT_CLIENT_SECRET: "Client secret from admin-cli -> Credentials tab"
QUARKUS_KEYCLOAK_ADMIN_CLIENT_GRANT_TYPE: "CLIENT_CREDENTIALS "
QUARKUS_OIDC_AUTH_SERVER_URL: "https://auth.vegaspread.cloud/realms/dev-vega"
```

# AWS Configuration

## TLS Configuration

Set the following environment variables during build time:
QUARKUS_TLS__VEGA_TLS__TRUST_STORE_P12_PASSWORD: "your-truststore-password"
