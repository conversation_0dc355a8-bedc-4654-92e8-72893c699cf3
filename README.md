[![pipeline status](https://gitlab.com/vegaspread/java-backend/extraction/badges/develop/pipeline.svg)](https://gitlab.com/vegaspread/java-backend/extraction/-/commits/develop)
[![coverage report](https://gitlab.com/vegaspread/java-backend/extraction/badges/develop/coverage.svg)](https://gitlab.com/vegaspread/java-backend/extraction/-/commits/develop)

# AWS Configuration

## TLS Configuration

Set the following environment variables during build time:
QUARKUS_TLS__VEGA_TLS__TRUST_STORE_P12_PASSWORD: "your-truststore-password"
