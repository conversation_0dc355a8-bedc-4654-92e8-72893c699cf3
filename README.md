# workflow

[![pipeline status](https://gitlab.com/vegaspread/java-backend/workflow/badges/develop/pipeline.svg)](https://gitlab.com/vegaspread/java-backend/workflow/-/commits/develop)
[![coverage report](https://gitlab.com/vegaspread/java-backend/workflow/badges/develop/coverage.svg)](https://gitlab.com/vegaspread/java-backend/workflow/-/commits/develop)

This project uses Quarkus, the Supersonic Subatomic Java Framework.

If you want to learn more about Quarkus, please visit its website: https://quarkus.io/ .

## Running the application in dev mode

You can run your application in dev mode that enables live coding using:

```shell script
./mvnw compile quarkus:dev
```

> **_NOTE:_**  Quarkus now ships with a Dev UI, which is available in dev mode only at http://localhost:8080/q/dev/.

# CORS configurations for GCS Bucket

Change the origin and bucket name for the corresponding environment.

```json
[
  {
    "origin": [
      "*"
    ],
    "method": [
      "GET",
      "PUT",
      "HEAD"
    ],
    "responseHeader": [
      "Content-Type",
      "Content-Length",
      "Content-Disposition",
      "Content-Encoding",
      "Content-Range",
      "Content-MD5",
      "ETag",
      "x-goog-*"
    ],
    "maxAgeSeconds": 3600
  }
]
```

Save the json to cors.json and run the following command to update the CORS configuration for the bucket.

```shell script
gcloud storage buckets update gs://dev-vegaspread --cors-file=cors.json
```

Read back the CORS configuration for the bucket.

```shell script
gcloud storage buckets describe gs://dev-vegaspread --format="default(cors_config)"
```

# AWS Configuration

## TLS Configuration

Set the following environment variables during build time:
QUARKUS_TLS__VEGA_TLS__TRUST_STORE_P12_PASSWORD: "your-truststore-password"
