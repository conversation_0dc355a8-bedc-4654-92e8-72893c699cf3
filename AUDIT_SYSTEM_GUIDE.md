# Complete Entity Auditing Guide

This guide provides comprehensive documentation for implementing auditing functionality for any entity in the system,
including audit history tracking, querying, and rollback capabilities.

## Table of Contents

1. [Overview](#overview)
2. [Core Components](#core-components)
3. [Entity Setup](#entity-setup)
4. [DTOs and Request/Response Models](#dtos-and-requestresponse-models)
5. [Service Layer](#service-layer)
6. [REST API Layer](#rest-api-layer)
7. [Complete Example](#complete-example)
8. [Best Practices](#best-practices)

## Overview

The audit system provides:

- **Automatic audit tracking** using Hibernate Envers
- **Generic audit querying** with filtering, sorting, and pagination
- **Rollback functionality** to restore entities to previous states
- **TraceId correlation** for tracking changes across API requests
- **DTO-based responses** for consistent API design

## Core Components

### 1. Audit Infrastructure Classes

#### `GenericAuditService`

- **Location**: `com.walnut.vegaspread.common.service.audit.envers.GenericAuditService`
- **Purpose**: Provides generic audit querying and rollback functionality
- **Key Methods**:
    - `getPaginatedAuditsAsDto<E,D>()` - Get paginated audit history
    - `rollback<E,ID>()` - Rollback entity to specific traceId state

#### `GenericRollbackService`

- **Location**: `com.walnut.vegaspread.common.service.audit.envers.GenericRollbackService`
- **Purpose**: Handles core rollback logic
- **Features**: TraceId-based rollback, multiple entity changes, transaction management

#### `EntityRollbackStrategy<E, ID>`

- **Location**: `com.walnut.vegaspread.common.service.audit.envers.EntityRollbackStrategy`
- **Purpose**: Interface defining entity-specific rollback behavior
- **Key Methods**:
    - `copyAuditedFields()` - Define which fields to restore
    - `isRollbackAllowed()` - Business rule validation
    - `handleDeletionRollback()` - Custom deletion recovery logic

#### `MetadataRevEntity`

- **Location**: `com.walnut.vegaspread.common.model.audit.envers.MetadataRevEntity`
- **Purpose**: Custom revision entity that captures traceId and username
- **Fields**: `id`, `timestamp`, `username`, `traceId`

#### `MetadataRevListener`

- **Location**: `com.walnut.vegaspread.common.service.audit.envers.MetadataRevListener`
- **Purpose**: Automatically populates revision metadata from OpenTelemetry context

### 2. Utility Classes

#### `AuditFieldDetector`

- **Location**: `com.walnut.vegaspread.common.model.audit.envers.AuditFieldDetector`
- **Purpose**: Detects audited fields using JPA metamodel
- **Usage**: Validates filter fields and provides field metadata

#### `BaseEntityMapper<E, D>`

- **Location**: `com.walnut.vegaspread.common.model.audit.envers.BaseEntityMapper`
- **Purpose**: Interface for entity-to-DTO mapping in audit responses
- **Implementation**: Use MapStruct or manual mapping

## Entity Setup

### 1. Entity Annotations

```java

@Entity
@Table(name = "my_entity")
@Audited  // Enable Hibernate Envers auditing
@RevisionEntity(MetadataRevEntity.class)  // Use custom revision entity
public class MyEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name")
    private String name;  // This field will be audited

    @Column(name = "description")
    private String description;  // This field will be audited

    @NotAudited  // This field will NOT be audited
    @Column(name = "created_by")
    private String createdBy;

    @ManyToOne
    @JoinColumn(name = "category_id")
    private CategoryEntity category;  // Relationships are audited too

    // Getters and setters...
}
```

### 2. Repository Setup

```java
@ApplicationScoped
public class MyEntityRepository implements PanacheRepositoryBase<MyEntity, Long> {
    // Standard Panache repository - no special audit setup needed
}
```

## DTOs and Request/Response Models

### 1. Audit Request DTOs

#### `AuditRequestDto`

```java
public record AuditRequestDto(
        List<AuditFilterDto> filters,     // Optional filters
        List<AuditSortDto> sorts,         // Optional sorting
        int page,                         // Page number (1-based)
        int size                          // Page size
) {
}
```

#### `AuditFilterDto`

```java
public record AuditFilterDto(
        String fieldName,                 // Field to filter on
        Object value,                     // Filter value
        FilterOperation operation         // EQUALS, LIKE, GREATER_THAN, etc.
) {
    public enum FilterOperation {
        EQUALS, NOT_EQUALS, LIKE, NOT_LIKE,
        GREATER_THAN, LESS_THAN,
        GREATER_THAN_OR_EQUAL, LESS_THAN_OR_EQUAL,
        IN, NOT_IN, IS_NULL, IS_NOT_NULL
    }
}
```

#### `AuditSortDto`

```java
public record AuditSortDto(
        String field,                     // Field to sort by
        SortDirection direction           // ASC or DESC
) {
    public enum SortDirection {ASC, DESC}
}
```

### 2. Audit Response DTOs

#### `PaginatedAuditResponse<T>`

```java
public record PaginatedAuditResponse<T>(
    List<T> content,                  // Audit records
    int page,                         // Current page
    int size,                         // Page size
    long totalElements,               // Total records
    int totalPages                    // Total pages
) {}
```

#### `AuditRevisionDto<D>`

```java
public record AuditRevisionDto<D>(
        D entity,                         // Entity DTO
        Long revisionNumber,              // Revision number
        LocalDateTime revisionDate,       // When changed
        String revisionType,              // ADD, MOD, DEL
        String username,                  // Who changed it
        String traceId,                   // Request correlation ID
        Map<String, Object> changedFields // What changed
) {
}
```

## Service Layer

### 1. Entity-Specific Rollback Strategy

```java

@ApplicationScoped
public class MyEntityRollbackStrategy implements EntityRollbackStrategy<MyEntity, Long> {

    private final MyEntityRepository repository;

    public MyEntityRollbackStrategy(MyEntityRepository repository) {
        this.repository = repository;
    }

    @Override
    public Class<MyEntity> getEntityClass() {
        return MyEntity.class;
    }

    @Override
    public MyEntity findById(Long entityId) {
        return repository.findById(entityId);
    }

    @Override
    public void persist(MyEntity entity) {
        repository.persist(entity);
    }

    @Override
    public void delete(Long entityId) {
        MyEntity entity = repository.findById(entityId);
        if (entity != null) {
            repository.delete(entity);
        }
    }

    @Override
    public Long getEntityId(MyEntity entity) {
        return entity.getId();
    }

    @Override
    public MyEntity createNewEntity() {
        return new MyEntity();
    }

    @Override
    public void copyAuditedFields(MyEntity source, MyEntity target,
                                  RevisionType revisionType, boolean isEntityCreation) {
        // Copy only @Audited fields
        target.setName(source.getName());
        target.setDescription(source.getDescription());
        target.setCategory(source.getCategory());

        // Handle @NotAudited fields specially
        if (isEntityCreation) {
            // For entity creation, use values from audit history
            target.setCreatedBy(source.getCreatedBy());
        }
        // For updates, skip @NotAudited fields to preserve current values
    }

    @Override
    public boolean isRollbackAllowed(Long entityId, RevisionType revisionType) {
        // Add business rules here
        // Example: Prevent rollback of finalized records
        MyEntity entity = repository.findById(entityId);
        return entity == null || !"FINALIZED".equals(entity.getStatus());
    }
}
```

### 2. Entity-Specific Audit Service

```java

@ApplicationScoped
public class MyEntityAuditService {

    private final GenericAuditService genericAuditService;
    private final MyEntityRollbackStrategy rollbackStrategy;

    public MyEntityAuditService(GenericAuditService genericAuditService,
                                MyEntityRollbackStrategy rollbackStrategy) {
        this.genericAuditService = genericAuditService;
        this.rollbackStrategy = rollbackStrategy;
    }

    /**
     * Rollback entity to the state at the specified traceId
     */
    @Transactional
    public void rollback(String traceId, EntityManager entityManager) {
        genericAuditService.rollback(traceId, rollbackStrategy, entityManager);
    }

    /**
     * Get paginated audit history with DTO mapping
     */
    public PaginatedAuditResponse<AuditRevisionDto<MyEntityDto>> getPaginatedAuditsAsDto(
            AuditRequestDto request,
            BaseEntityMapper<MyEntity, MyEntityDto> mapper,
            EntityManager entityManager) {
        return genericAuditService.getPaginatedAuditsAsDto(
                MyEntity.class, request, mapper, entityManager);
    }

    /**
     * Convenience method to get audit history for a specific entity
     */
    public PaginatedAuditResponse<AuditRevisionDto<MyEntityDto>> getAuditForEntityId(
            Long entityId,
            BaseEntityMapper<MyEntity, MyEntityDto> mapper,
            EntityManager entityManager) {

        var filterDto = new AuditFilterDto(
                "id", entityId, AuditFilterDto.FilterOperation.EQUALS
        );

        var request = new AuditRequestDto(
                List.of(filterDto), null, 1, 100
        );

        return getPaginatedAuditsAsDto(request, mapper, entityManager);
    }
}
```

## REST API Layer

### 1. Audit Resource

```java

@Path("/my-entity/audit")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Authenticated
public class MyEntityAuditResource {

    private final MyEntityAuditService auditService;
    private final EntityManager entityManager;
    private final MyEntityMapper mapper;

    public MyEntityAuditResource(MyEntityAuditService auditService,
                                 EntityManager entityManager,
                                 MyEntityMapper mapper) {
        this.auditService = auditService;
        this.entityManager = entityManager;
        this.mapper = mapper;
    }

    /**
     * Get audit history for a specific entity
     */
    @GET
    @Path("/{entityId}")
    public PaginatedAuditResponse<AuditRevisionDto<MyEntityDto>> getAuditForEntity(
            @PathParam("entityId") Long entityId) {
        return auditService.getAuditForEntityId(entityId, mapper, entityManager);
    }

    /**
     * Get paginated audit history with filtering and sorting
     */
    @POST
    @Path("/list")
    public PaginatedAuditResponse<AuditRevisionDto<MyEntityDto>> listAll(
            @Valid AuditRequestDto request) {
        return auditService.getPaginatedAuditsAsDto(request, mapper, entityManager);
    }

    /**
     * Rollback entity to the state at the specified traceId
     */
    @PUT
    @Path("/rollback/{traceId}")
    public void rollback(@PathParam("traceId") String traceId) {
        auditService.rollback(traceId, entityManager);
    }
}
```

### 2. Example API Usage

#### Get Audit History

```bash
# Get audit history for entity ID 123
GET /my-entity/audit/123

# Get paginated audit history with filters
POST /my-entity/audit/list
{
  "filters": [
    {
      "fieldName": "name",
      "value": "John",
      "operation": "LIKE"
    }
  ],
  "sorts": [
    {
      "field": "revisionDate",
      "direction": "DESC"
    }
  ],
  "page": 1,
  "size": 20
}
```

#### Rollback Entity

```bash
# Rollback to the state at traceId "abc-123-def"
PUT /my-entity/audit/rollback/abc-123-def
```

## Complete Example

Let's see a complete implementation for a `ProductEntity`:

### 1. Entity

```java

@Entity
@Table(name = "products")
@Audited
@RevisionEntity(MetadataRevEntity.class)
public class ProductEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String name;
    private String description;
    private BigDecimal price;
    private boolean active;

    @NotAudited
    private String createdBy;

    @ManyToOne
    @JoinColumn(name = "category_id")
    private CategoryEntity category;

    // Getters and setters...
}
```

### 2. DTO

```java
public record ProductDto(
    Long id,
    String name,
    String description,
    BigDecimal price,
    boolean active,
    String categoryName
) {}
```

### 3. Mapper

```java

@Mapper(componentModel = "cdi")
public interface ProductMapper extends BaseEntityMapper<ProductEntity, ProductDto> {

    @Override
    @Mapping(target = "categoryName", source = "category.name")
    ProductDto toDto(ProductEntity entity);
}
```

### 4. Rollback Strategy

```java

@ApplicationScoped
public class ProductRollbackStrategy implements EntityRollbackStrategy<ProductEntity, Long> {

    private final ProductRepository repository;

    // Implementation as shown in previous examples...

    @Override
    public void copyAuditedFields(ProductEntity source, ProductEntity target,
                                  RevisionType revisionType, boolean isEntityCreation) {
        target.setName(source.getName());
        target.setDescription(source.getDescription());
        target.setPrice(source.getPrice());
        target.setActive(source.isActive());
        target.setCategory(source.getCategory());

        if (isEntityCreation) {
            target.setCreatedBy(source.getCreatedBy());
        }
    }
}
```

### 5. Service

```java

@ApplicationScoped
public class ProductAuditService {

    private final GenericAuditService genericAuditService;
    private final ProductRollbackStrategy rollbackStrategy;

    // Implementation as shown in previous examples...
}
```

### 6. Resource

```java
@Path("/products/audit")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Authenticated
public class ProductAuditResource {
    
    // Implementation as shown in previous examples...
}
```

## Best Practices

### 1. Entity Design

- ✅ Use `@Audited` on entities that need audit tracking
- ✅ Use `@NotAudited` on fields that shouldn't be tracked (like `createdBy`, `lastModified`)
- ✅ Consider audit table size - exclude large binary fields if not needed

### 2. Rollback Strategy

- ✅ Only copy `@Audited` fields in `copyAuditedFields()`
- ✅ Handle `@NotAudited` fields carefully - preserve current values for updates
- ✅ Implement business rules in `isRollbackAllowed()`
- ✅ Test rollback with ADD, MOD, and DEL operations

### 3. Performance

- ✅ Use pagination for audit queries
- ✅ Add database indexes on frequently filtered fields
- ✅ Consider audit data retention policies
- ✅ Use appropriate fetch strategies for relationships

### 4. Security

- ✅ Implement proper authorization for audit endpoints
- ✅ Validate rollback permissions based on user roles
- ✅ Log rollback operations for security auditing
- ✅ Consider sensitive data in audit logs

### 5. Testing

- ✅ Test audit tracking with entity changes
- ✅ Test rollback scenarios (single/multiple entities)
- ✅ Test `@NotAudited` field handling
- ✅ Test filtering and sorting functionality
- ✅ Test business rule validation

## Advanced Topics

### 1. Custom Revision Handling

#### Handling Complex Business Logic in Rollback

```java

@Override
public MyEntity handleDeletionRollback(Long entityId, MyEntity stateBeforeDeletion, AuditReader reader) {
    // Custom logic for deletion rollback
    MyEntity currentEntity = findById(entityId);

    if (currentEntity == null) {
        // Entity doesn't exist, recreate it
        MyEntity restoredEntity = createNewEntity();
        copyAuditedFields(stateBeforeDeletion, restoredEntity, RevisionType.ADD, true);

        // Custom business logic
        restoredEntity.setStatus("RESTORED");
        restoredEntity.setRestoredDate(LocalDateTime.now());

        persist(restoredEntity);
        return restoredEntity;
    } else {
        // Entity exists, restore to state before deletion
        copyAuditedFields(stateBeforeDeletion, currentEntity, RevisionType.MOD, false);
        persist(currentEntity);
        return currentEntity;
    }
}
```

### 2. Database Configuration

#### Hibernate Envers Configuration

```properties
# application.properties

# Envers configuration
quarkus.hibernate-orm.envers.audit-table-suffix=_AUD
quarkus.hibernate-orm.envers.revision-field-name=REV
quarkus.hibernate-orm.envers.revision-type-field-name=REVTYPE
quarkus.hibernate-orm.envers.store-data-at-delete=true
quarkus.hibernate-orm.envers.default-schema=audit_schema
```

#### Database Schema Example

```sql
-- Main entity table
CREATE TABLE products
(
    id BIGSERIAL PRIMARY KEY,
    name        VARCHAR(255) NOT NULL,
    description TEXT,
    price       DECIMAL(10, 2),
    active      BOOLEAN DEFAULT true,
    created_by  VARCHAR(100),
    category_id BIGINT REFERENCES categories (id)
);

-- Audit table (created automatically by Envers)
CREATE TABLE products_AUD
(
    id          BIGINT  NOT NULL,
    REV         INTEGER NOT NULL REFERENCES metadata_rev_entity (id),
    REVTYPE     SMALLINT,
    name        VARCHAR(255),
    description TEXT,
    price       DECIMAL(10, 2),
    active      BOOLEAN,
    category_id BIGINT,
    PRIMARY KEY (id, REV)
);

-- Custom revision entity table
CREATE TABLE metadata_rev_entity
(
    id        SERIAL PRIMARY KEY,
    timestamp BIGINT NOT NULL,
    username  VARCHAR(100),
    trace_id  VARCHAR(100)
);
```

### 3. Error Handling and Monitoring

#### Custom Exception Handling

```java

@ApplicationScoped
public class AuditExceptionHandler {

    public void handleRollbackException(String traceId, Exception e) {
        // Log the error
        logger.errorf("Rollback failed for traceId %s: %s", traceId, e.getMessage());

        // Send to monitoring system
        // metricsService.incrementRollbackFailures(traceId);

        // Notify administrators if critical
        if (isCriticalEntity(traceId)) {
            // notificationService.sendAlert("Critical rollback failure", e);
        }
    }
}
```

#### Metrics and Monitoring

```java

@ApplicationScoped
public class AuditMetricsService {

    @Counted(name = "audit_queries_total", description = "Total audit queries")
    public void recordAuditQuery(String entityType) {
        // Metrics automatically recorded by Micrometer
    }

    @Timed(name = "rollback_duration", description = "Rollback operation duration")
    public void recordRollbackDuration(String traceId) {
        // Timing automatically recorded
    }
}
```

### 4. Integration with OpenTelemetry

#### Tracing Audit Operations

```java

@ApplicationScoped
public class TracedAuditService {

    @WithSpan("audit.query")
    public PaginatedAuditResponse<AuditRevisionDto<MyEntityDto>> getAudits(
            @SpanAttribute("entity.type") String entityType,
            AuditRequestDto request) {

        Span.current().addEvent("Starting audit query");
        // ... audit logic
        Span.current().addEvent("Audit query completed");

        return result;
    }

    @WithSpan("audit.rollback")
    public void rollback(@SpanAttribute("trace.id") String traceId) {
        Span.current().addEvent("Starting rollback operation");
        // ... rollback logic
        Span.current().addEvent("Rollback completed successfully");
    }
}
```

### 5. Testing Framework

#### Integration Test Example

```java

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ProductAuditServiceTest {

    @Inject
    ProductAuditService auditService;

    @Inject
    EntityManager entityManager;

    @Test
    @Transactional
    void testRollbackModificationOperation() {
        // Given: Create initial entity
        ProductEntity product = createProduct("Initial Product", "Initial Description");
        Long productId = product.getId();

        // When: Modify entity with specific traceId
        String traceId = "test-trace-123";
        setTraceIdInContext(traceId);

        product.setName("Modified Product");
        product.setDescription("Modified Description");
        entityManager.persist(product);
        entityManager.flush();

        // Make another change after target state
        product.setName("Further Modified");
        entityManager.persist(product);
        entityManager.flush();

        // Then: Rollback to target traceId state
        auditService.rollback(traceId, entityManager);
        entityManager.clear();

        // Verify: Entity restored to target state
        ProductEntity rolledBackProduct = entityManager.find(ProductEntity.class, productId);
        assertEquals("Modified Product", rolledBackProduct.getName());
        assertEquals("Modified Description", rolledBackProduct.getDescription());
    }

    private void setTraceIdInContext(String traceId) {
        // Mock OpenTelemetry context for testing
        // Implementation depends on your testing setup
    }
}
```

### 6. Migration Guide

#### Migrating Existing Entities to Audit System

1. **Add Audit Annotations**

```java
// Before
@Entity
public class ExistingEntity {
    // fields...
}

// After
@Entity
@Audited
@RevisionEntity(MetadataRevEntity.class)
public class ExistingEntity {
    // fields...

    @NotAudited  // Add to fields that shouldn't be audited
    private String internalField;
}
```

2. **Create Database Migration**

```sql
-- Create audit table
CREATE TABLE existing_entity_AUD (
    id BIGINT NOT NULL,
    REV INTEGER NOT NULL REFERENCES metadata_rev_entity(id),
    REVTYPE SMALLINT,
    -- Add all audited fields here
    name VARCHAR(255),
    description TEXT,
    PRIMARY KEY (id, REV)
);
```

3. **Implement Rollback Strategy**

```java

@ApplicationScoped
public class ExistingEntityRollbackStrategy implements EntityRollbackStrategy<ExistingEntity, Long> {
    // Implement all required methods
}
```

4. **Update Service Layer**

```java
@ApplicationScoped
public class ExistingEntityAuditService {
    // Add audit service methods
}
```

5. **Add REST Endpoints**

```java

@Path("/existing-entity/audit")
public class ExistingEntityAuditResource {
    // Add audit endpoints
}
```

## Troubleshooting

### Common Issues and Solutions

1. **Audit Tables Not Created**
    - Ensure `@Audited` annotation is present
    - Check Hibernate Envers configuration
    - Verify database schema permissions

2. **TraceId Not Captured**
    - Verify OpenTelemetry is properly configured
    - Check `MetadataRevListener` is registered
    - Ensure spans are active during entity changes

3. **Rollback Fails with @NotAudited Fields**
    - Review `copyAuditedFields()` implementation
    - Ensure @NotAudited fields are handled correctly
    - Test with entity creation vs. update scenarios

4. **Performance Issues with Large Audit Tables**
    - Add indexes on frequently queried fields
    - Implement audit data archiving
    - Consider pagination limits
    - Use database partitioning for very large tables

5. **Memory Issues with Large Result Sets**
    - Always use pagination
    - Implement streaming for large exports
    - Consider DTO projection instead of full entities

This comprehensive guide provides everything needed to implement full auditing functionality for any entity in your
system. The generic infrastructure handles the complex logic while allowing for entity-specific customization through
the strategy pattern.
