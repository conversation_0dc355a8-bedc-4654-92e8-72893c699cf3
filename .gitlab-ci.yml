stages:
  - test

cache:
  paths:
    - .m2/repository

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository"

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "web"
    - if: >-
        $CI_PIPELINE_SOURCE == "merge_request_event" && 
        ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "stg") && 
        $CI_MERGE_REQUEST_TITLE !~ /^Draft:/

test-with-coverage:
  stage: test
  image: maven:3.9-amazoncorretto-17-debian
  before_script:
    - apt update && apt install -y curl gawk
    - curl -O https://dl.google.com/dl/cloudsdk/channels/rapid/downloads/google-cloud-cli-linux-x86_64.tar.gz
    - tar -xf google-cloud-cli-linux-x86_64.tar.gz -C /usr/local
    - /usr/local/google-cloud-sdk/install.sh --quiet
    - ln -s /usr/local/google-cloud-sdk/bin/gcloud /usr/local/bin/
    - ln -s /usr/local/google-cloud-sdk/bin/gsutil /usr/local/bin/
    - echo -n ${GCP_SERVICE_KEY_BASE64} | base64 -d > /tmp/gcp-key.json
    - export GOOGLE_APPLICATION_CREDENTIALS=/tmp/gcp-key.json
  script:
    - mvn clean verify
    - chmod +x ./jacoco-summary.sh
    - ./jacoco-summary.sh target/jacoco-report/jacoco.csv
  coverage: '/Instructions.*?([0-9.]+)%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: jacoco
        path: target/jacoco-report/jacoco.xml
    expire_in: 1 week
