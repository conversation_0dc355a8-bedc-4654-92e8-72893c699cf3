BUILD_NUMBER=local

ENV=dev
SERVICE_NAME=iam
ENV_UPPER=$(shell echo ${ENV} | tr '[:lower:]' '[:upper:]')
GCLOUD_RUN_CPU=1
GCLOUD_RUN_MEM=512Mi
GCLOUD_RUN_CONCURRENCY=80
GCLOUD_RUN_MAX_INSTANCES=1
BUILD_DATE=$(shell date "+%d-%m-%y")
BUILD_ID=${ENV}-${BUILD_DATE}-${BUILD_NUMBER}
CLOUD_SQL_INSTANCE=vegaspread-7586a:asia-southeast1:vega-db
GCLOUD_RUN_MAX_INSTANCES=1
GCR_REPO=asia-southeast1-docker.pkg.dev/vegaspread-7586a/java

KEYCLOAK_REALM=dev-vega
KEYCLOAK_BASE_URL=https://auth.vegaspread.cloud
KEYCLOAK_REALM_URL=${KEY<PERSON>OAK_BASE_URL}/realms/${KE<PERSON><PERSON><PERSON><PERSON>_REALM}
ADMIN_CLIENT_SECRET=${ENV_UPPER}_ADMIN_SECRET:latest

AWS_ACCOUNT_ID = $(shell aws sts get-caller-identity --query Account --output text)
LAMBDA_S3_BUCKET_NAME = dev-vega-build-artifacts-${AWS_ACCOUNT_ID}
TRUSTSTORE_PASSWORD=changeit

deploy:
	gcloud artifacts docker images delete ${GCR_REPO}/${ENV}-${SERVICE_NAME} --quiet || true
	docker tag thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} ${GCR_REPO}/${ENV}-${SERVICE_NAME}
	docker push ${GCR_REPO}/${ENV}-${SERVICE_NAME}

	gcloud run deploy ${ENV}-vega-${SERVICE_NAME} \
		--image ${GCR_REPO}/${ENV}-${SERVICE_NAME} \
		--allow-unauthenticated --port=8080 --cpu=${GCLOUD_RUN_CPU} --memory=${GCLOUD_RUN_MEM} \
		--max-instances=${GCLOUD_RUN_MAX_INSTANCES} --concurrency ${GCLOUD_RUN_CONCURRENCY} \
		--region=asia-southeast1 --cpu-boost --cpu-throttling \
		--set-env-vars=VEGA_ENV=${ENV} \
		--set-env-vars=QUARKUS_KEYCLOAK_ADMIN_CLIENT_REALM=${KEYCLOAK_REALM} \
		--set-env-vars=QUARKUS_KEYCLOAK_API_KEY_AUTH_REALM=${KEYCLOAK_REALM} \
		--set-env-vars=QUARKUS_OIDC_AUTH_SERVER_URL=${KEYCLOAK_REALM_URL} \
		--set-env-vars=KEYCLOAK_BASE_URL=${KEYCLOAK_BASE_URL} \
		--update-secrets=QUARKUS_KEYCLOAK_ADMIN_CLIENT_CLIENT_SECRET=${ADMIN_CLIENT_SECRET} \
		--project=vegaspread-7586a --service-account=${ENV}-<EMAIL>

deploy-native:
	QUARKUS_SMALLRYE_OPENAPI_OAUTH2_IMPLICIT_AUTHORIZATION_URL=${KEYCLOAK_REALM_URL}/protocol/openid-connect/auth mvn \
		clean package -Pnative,gcp
	docker build -f src/main/docker/Dockerfile.native-micro -t thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} .
	make deploy


deploy-ezee: BUILD_ID=${ENV}-ezee-${BUILD_DATE}-${BUILD_NUMBER}
deploy-ezee: EZEE_ENV=dev-ezee
deploy-ezee: KEYCLOAK_REALM=ezee
deploy-ezee: KEYCLOAK_REALM_URL=https://demoauth.ezeefin.net.in/realms/ezee
deploy-ezee: KEYCLOAK_BASE_URL=https://demoauth.ezeefin.net.in
deploy-ezee: ADMIN_CLIENT_SECRET=DEV_EZEE_ADMIN_SECRET:latest
deploy-ezee: KEYCLOAK_CLIENT_ID=walnut-api-client
deploy-ezee: AUTH_BASE_URL=https://marketplace-dev-jiofinance.ezeefin.net.in/ezeeAPI/v2
deploy-ezee:
	mvn clean package -Pnative,ezee
	docker build -f src/main/docker/Dockerfile.native-micro -t thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} .
	gcloud artifacts docker images delete ${GCR_REPO}/${EZEE_ENV}-${SERVICE_NAME} --quiet || true

	gcloud artifacts docker images delete ${GCR_REPO}/${EZEE_ENV}-${SERVICE_NAME} --quiet || true
	docker tag thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} ${GCR_REPO}/${EZEE_ENV}-${SERVICE_NAME}
	docker push ${GCR_REPO}/${EZEE_ENV}-${SERVICE_NAME}

	gcloud run deploy ${EZEE_ENV}-vega-${SERVICE_NAME} \
		--image ${GCR_REPO}/${EZEE_ENV}-${SERVICE_NAME} \
		--allow-unauthenticated --port=8080 --cpu=${GCLOUD_RUN_CPU} --memory=${GCLOUD_RUN_MEM} \
		--max-instances=${GCLOUD_RUN_MAX_INSTANCES} --concurrency ${GCLOUD_RUN_CONCURRENCY} \
		--region=asia-southeast1 --cpu-boost --cpu-throttling \
		--set-env-vars=VEGA_ENV=${EZEE_ENV} \
		--set-env-vars=QUARKUS_KEYCLOAK_ADMIN_CLIENT_SERVER_URL=${KEYCLOAK_BASE_URL} \
		--set-env-vars=QUARKUS_KEYCLOAK_ADMIN_CLIENT_REALM=${KEYCLOAK_REALM} \
		--set-env-vars=QUARKUS_KEYCLOAK_ADMIN_CLIENT_CLIENT_ID=${KEYCLOAK_CLIENT_ID} \
		--set-env-vars=QUARKUS_KEYCLOAK_ADMIN_CLIENT_GRANT_TYPE=CLIENT_CREDENTIALS \
		--set-env-vars=QUARKUS_KEYCLOAK_API_KEY_AUTH_REALM=${KEYCLOAK_REALM} \
		--set-env-vars=QUARKUS_OIDC_AUTH_SERVER_URL=${KEYCLOAK_REALM_URL} \
		--set-env-vars=KEYCLOAK_BASE_URL=${KEYCLOAK_BASE_URL} \
		--set-env-vars=QUARKUS_REST_CLIENT_EZEE_AUTH_CLIENT_URL=${AUTH_BASE_URL} \
		--set-env-vars VEGA_CLOUD_PROVIDER=gcp \
		--update-secrets=QUARKUS_KEYCLOAK_ADMIN_CLIENT_CLIENT_SECRET=${ADMIN_CLIENT_SECRET} \
		--project=vegaspread-7586a --service-account=${ENV}-<EMAIL>

deploy-jvm:
	QUARKUS_SMALLRYE_OPENAPI_OAUTH2_IMPLICIT_AUTHORIZATION_URL=${KEYCLOAK_REALM_URL}/protocol/openid-connect/auth mvn \
	clean package -Pjvm,gcp
	docker build -f src/main/docker/Dockerfile.jvm -t thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} .
	make deploy

deploy-native-aws:
	QUARKUS_TLS__VEGA_TLS__TRUST_STORE_P12_PASSWORD=${TRUSTSTORE_PASSWORD} mvn clean package -Pnative,aws
	@echo "Uploading zip file to S3..."
	aws s3 cp target/function.zip s3://${LAMBDA_S3_BUCKET_NAME}/${SERVICE_NAME}-lambda.zip
	@echo "Updating Lambda function..."
	aws lambda update-function-code \
		--function-name ${ENV}-${SERVICE_NAME}-lambda \
		--s3-bucket ${LAMBDA_S3_BUCKET_NAME} \
		--s3-key ${SERVICE_NAME}-lambda.zip > /dev/null 2>&1
	@echo "Waiting for Lambda update to complete..."
	aws lambda wait function-updated --function-name ${ENV}-${SERVICE_NAME}-lambda
	@echo "Lambda function updated successfully."

verify-lambda:
	@echo "Verifying Lambda function update..."
	@echo "Last update time (local):"
	@aws lambda get-function \
		--function-name ${ENV}-${SERVICE_NAME}-lambda \
		--query 'Configuration.LastModified' \
		--output text | xargs -I{} date -d {} "+%Y-%m-%d %H:%M:%S local time"
	@echo "Revision ID:"
	@aws lambda get-function \
		--function-name ${ENV}-${SERVICE_NAME}-lambda \
		--query 'Configuration.RevisionId' \
		--output text
