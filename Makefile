BUILD_NUMBER=local

ENV=dev
SERVICE_NAME=workflow
ENV_UPPER=$(shell echo ${ENV} | tr '[:lower:]' '[:upper:]')
GCLOUD_RUN_CPU=1
GCLOUD_RUN_MEM=768Mi
GCLOUD_RUN_CONCURRENCY=80
GCLOUD_RUN_MAX_INSTANCES=1
BUILD_DATE=$(shell date "+%d-%m-%y")
BUILD_ID=${ENV}-${BUILD_DATE}-${BUILD_NUMBER}
CLOUD_SQL_INSTANCE=vegaspread-7586a:asia-southeast1:vega-db
GCLOUD_RUN_MAX_INSTANCES=1
GCR_REPO=asia-southeast1-docker.pkg.dev/vegaspread-7586a/java

KEYCLOAK_REALM=dev-vega
QUARKUS_REST_CLIENT_RUNPOD_URL=https://api.runpod.ai/v2/7a31shtmkg3sa0
KEYCLOAK_BASE_URL=https://auth.vegaspread.cloud
KEYCLOAK_REALM_URL=${KEYCLOAK_BASE_URL}/realms/${KEYCLOAK_REALM}
WISE_API_KEY=${ENV_UPPER}_WISE_API_KEY:latest

AWS_ACCOUNT_ID = $(shell aws sts get-caller-identity --query Account --output text)
LAMBDA_S3_BUCKET_NAME = dev-vega-build-artifacts-${AWS_ACCOUNT_ID}
LAMBDA_S3_KEY = lambda/${SERVICE_NAME}-lambda.zip

deploy:
	gcloud artifacts docker images delete ${GCR_REPO}/${ENV}-${SERVICE_NAME} --quiet || true
	docker tag thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} ${GCR_REPO}/${ENV}-${SERVICE_NAME}
	docker push ${GCR_REPO}/${ENV}-${SERVICE_NAME}

	gcloud run deploy ${ENV}-vega-${SERVICE_NAME} \
		--image ${GCR_REPO}/${ENV}-${SERVICE_NAME} \
		--allow-unauthenticated --port=8080 --cpu=${GCLOUD_RUN_CPU} --memory=${GCLOUD_RUN_MEM} \
		--max-instances=${GCLOUD_RUN_MAX_INSTANCES} --concurrency ${GCLOUD_RUN_CONCURRENCY} \
		--region=asia-southeast1 --cpu-boost --cpu-throttling \
		--set-env-vars VEGA_ENV=${ENV} \
		--set-env-vars CLOUD_SQL_INSTANCE=${CLOUD_SQL_INSTANCE} \
		--set-env-vars QUARKUS_OIDC_AUTH_SERVER_URL=${KEYCLOAK_REALM_URL} \
		--set-env-vars QUARKUS_REST_CLIENT_RUNPOD_URL=${QUARKUS_REST_CLIENT_RUNPOD_URL} \
		--set-env-vars BUCKET_NAME=dev-vegaspread \
		--set-env-vars VEGA_API_CLIENT_NAME=dev-api-client \
		--set-env-vars INFINI_CALLBACK_USERNAME=service-account-infinii-dev-client \
		--set-env-vars INFINI_CALLBACK_METHOD=POST \
		--set-env-vars INFINI_CALLBACK_URL_TEMPLATE=https://dev.gw.infinisystem.com/service/ocr/api/v1/public/vega/data-callback \
		--set-env-vars QUARKUS_LOG_CATEGORY__COM_WALNUT_VEGASPREAD_WORKFLOW__LEVEL=DEBUG \
		--update-secrets=RUNPOD_API_KEY=RUNPOD_API_KEY:latest \
		--update-secrets=WISE_CLIENT_SECRET=${WISE_API_KEY} \
		--update-secrets=QUARKUS_MAILER_PASSWORD=${ENV_UPPER}_SMTP_PASSWORD:latest \
		--update-secrets=INFINI_CALLBACK_HEADER_X_API_KEY=${ENV_UPPER}_INFINI_CALLBACK_API_KEY:latest \
		--project=vegaspread-7586a --service-account=${ENV}-<EMAIL>

deploy-native:
	QUARKUS_SMALLRYE_OPENAPI_OAUTH2_IMPLICIT_AUTHORIZATION_URL=${KEYCLOAK_REALM_URL}/protocol/openid-connect/auth mvn \
		clean package -Pnative,aws-compile-dependencies,gcp
	docker build -f src/main/docker/Dockerfile.native-micro -t thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} .
	make deploy

deploy-ezee: EZEE_ENV=dev-ezee
deploy-ezee: KEYCLOAK_REALM_URL=https://demoauth.ezeefin.net.in/realms/ezee
deploy-ezee: WISE_API_KEY=DEV_EZEE_ADMIN_SECRET:latest
deploy-ezee: INTROSPECTION_URL=https://dev-ezee-vega-iam-agxygoruoq-as.a.run.app/vegaspread/api/v1/iam/token/introspect
deploy-ezee: OIDC_CLIENT_ID=ezee
deploy-ezee:
	gcloud artifacts docker images delete ${GCR_REPO}/${EZEE_ENV}-${SERVICE_NAME} --quiet || true

	docker pull ${GCR_REPO}/${ENV}-${SERVICE_NAME}
	docker tag ${GCR_REPO}/${ENV}-${SERVICE_NAME} ${GCR_REPO}/${EZEE_ENV}-${SERVICE_NAME}:latest
	docker push ${GCR_REPO}/${EZEE_ENV}-${SERVICE_NAME}:latest

	gcloud run deploy ${EZEE_ENV}-vega-${SERVICE_NAME} \
		--image ${GCR_REPO}/${EZEE_ENV}-${SERVICE_NAME} \
		--allow-unauthenticated --port=8080 --cpu=${GCLOUD_RUN_CPU} --memory=${GCLOUD_RUN_MEM} \
		--max-instances=${GCLOUD_RUN_MAX_INSTANCES} --concurrency ${GCLOUD_RUN_CONCURRENCY} \
		--region=asia-southeast1 --cpu-boost --cpu-throttling \
		--set-env-vars VEGA_ENV=${EZEE_ENV} \
		--set-env-vars CLOUD_SQL_INSTANCE=${CLOUD_SQL_INSTANCE} \
		--set-env-vars QUARKUS_DATASOURCE_USERNAME=${ENV}-vegaspread \
		--set-env-vars QUARKUS_DATASOURCE_JDBC_URL=jdbc:mysql:///${ENV}_vega_${SERVICE_NAME} \
		--set-env-vars QUARKUS_OIDC_AUTH_SERVER_URL=${KEYCLOAK_REALM_URL} \
		--set-env-vars QUARKUS_REST_CLIENT_RUNPOD_URL=${QUARKUS_REST_CLIENT_RUNPOD_URL} \
		--set-env-vars QUARKUS_OIDC_DISCOVERY_ENABLED=false \
		--set-env-vars QUARKUS_OIDC_INTROSPECTION_PATH=${INTROSPECTION_URL} \
		--set-env-vars QUARKUS_OIDC_CLIENT_ID=${OIDC_CLIENT_ID} \
		--set-env-vars QUARKUS_OIDC_TOKEN_REQUIRE_JWT_INTROSPECTION_ONLY=true \
		--set-env-vars BUCKET_NAME=dev-vegaspread \
		--set-env-vars VEGA_API_CLIENT_NAME=walnut-api-client \
		--set-env-vars EZEE_CALLBACK_USERNAME=ALL_USERS \
		--set-env-vars EZEE_CALLBACK_METHOD=POST \
		--set-env-vars EZEE_CALLBACK_URL_TEMPLATE=https://demolend.ezeefin.net.in/centralAPI/v1/walnut/workflow/callback/{docId} \
		--set-env-vars QUARKUS_LOG_CATEGORY__COM_WALNUT_VEGASPREAD_WORKFLOW__LEVEL=DEBUG \
		--update-secrets=RUNPOD_API_KEY=RUNPOD_API_KEY:latest \
		--update-secrets=WISE_CLIENT_SECRET=${WISE_API_KEY} \
		--update-secrets=EZEE_CALLBACK_HEADER_X_API_KEY=${ENV_UPPER}_EZEE_CALLBACK_API_KEY:latest \
		--update-secrets=QUARKUS_MAILER_PASSWORD=${ENV_UPPER}_SMTP_PASSWORD:latest \
		--project=vegaspread-7586a --service-account=${ENV}-<EMAIL>

deploy-jvm:
	QUARKUS_SMALLRYE_OPENAPI_OAUTH2_IMPLICIT_AUTHORIZATION_URL=${KEYCLOAK_REALM_URL}/protocol/openid-connect/auth mvn \
		clean package -Pjvm-gcp,aws-compile-dependencies
	docker build -f src/main/docker/Dockerfile.jvm -t thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} .
	make deploy

deploy-native-aws:
	mvn clean package -Pnative,aws,gcp-compile-dependencies
	@echo "Uploading zip file to S3..."
	aws s3 cp target/function.zip s3://${LAMBDA_S3_BUCKET_NAME}/${LAMBDA_S3_KEY}
	@echo "Updating Lambda function..."
	aws lambda update-function-code \
		--function-name ${ENV}-${SERVICE_NAME}-lambda \
		--s3-bucket ${LAMBDA_S3_BUCKET_NAME} \
		--s3-key ${LAMBDA_S3_KEY} > /dev/null
	@echo "Waiting for Lambda update to complete..."
	aws lambda wait function-updated --function-name ${ENV}-${SERVICE_NAME}-lambda
	@echo "Lambda function updated successfully."

verify-lambda:
	@echo "Verifying Lambda function update..."
	@echo "Last update time (local):"
	@aws lambda get-function \
		--function-name ${ENV}-${SERVICE_NAME}-lambda \
		--query 'Configuration.LastModified' \
		--output text | xargs -I{} date -d {} "+%Y-%m-%d %H:%M:%S local time"
	@echo "Revision ID:"
	@aws lambda get-function \
		--function-name ${ENV}-${SERVICE_NAME}-lambda \
		--query 'Configuration.RevisionId' \
		--output text
