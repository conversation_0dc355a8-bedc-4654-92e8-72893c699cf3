<html>
<head>
    <style>
        body {
            font-family: Arial, sans-serif;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #fafafa;
        }

        h2 {
            color: #2c3e50;
        }

        .container {
            max-width: 800px;
            margin: auto;
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
        }

        .document-link {
            margin: 15px 0;
        }

        .document-link a {
            color: #1a73e8;
            text-decoration: none;
        }

        .document-link a:hover {
            text-decoration: underline;
        }

        table {
            border-collapse: collapse;
            width: 100%;
            margin-top: 20px;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 10px;
            font-size: 14px;
        }

        th {
            background-color: #f2f2f2;
            color: #555;
        }

        .summary {
            margin-top: 20px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<div class="container">
    <h2>Document Summary</h2>
    <div class="document-link">
        <p>
            <strong>Document ID:</strong> {docId}
            <br/>
            <a href="{documentUrl}" target="_blank">View Document</a>
        </p>
    </div>

    <table>
        <thead>
        <tr>
            <th>Name</th>
            <th>Start Time</th>
            <th>End Time</th>
            <th>Duration</th>
            <th>Success</th>
        </tr>
        </thead>
        <tbody>
        {#for job in jobs}
            <tr>
                <td>{job.stage}</td>
                <td>{job.startTime}</td>
                <td>{job.endTime}</td>
                <td>{job.durationMinutes}m {job.durationSeconds}s</td>
                <td>{job.isSuccess ? 'Success' : 'Failed'}</td>
            </tr>
        {/for}
        </tbody>
    </table>

    <p class="summary">
        <strong>Total Duration:</strong> {totalMinutes} minutes {totalSeconds} seconds
    </p>
    <div class="footer">
        This is an automated email. Please do not reply.
    </div>
</div>
</body>
</html>
