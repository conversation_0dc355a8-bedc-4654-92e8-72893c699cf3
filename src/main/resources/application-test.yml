quarkus:
  datasource:
    db-kind: h2
    jdbc:
      url: jdbc:h2:mem:test;DB_CLOSE_DELAY=-1;MODE=MySQL
      driver: org.h2.Driver
      transactions: enabled
    username: test
    password: test

  flyway:
    migrate-at-start: true

  keycloak:
    devservices:
      enabled: false

  oidc:
    auth-server-url: http://localhost:8180/realms/myrealm
    client-id: my-client

  rest-client:
    iam:
      url: http://localhost:8084/test/api/v1/iam
  arc:
    exclude-types: com.walnut.vegaspread.common.security.ApiKeyInterceptor

vega:
  env: test
  cloud:
    provider: gcp
