quarkus:
  datasource:
    jdbc:
      additional-jdbc-properties:
        cloudSqlInstance: ${CLOUD_SQL_INSTANCE}
        socketFactory: com.google.cloud.sql.mysql.SocketFactory
        enableIamAuth: true
      url: jdbc:mysql:///${vega.env}_vega_workflow
    username: ${vega.env}-vegaspread

  google:
    cloud:
      project-id: vegaspread-7586a

vega:
  cloud:
    provider: gcp
    # The host url is used for the base url for callback url creation and for service url template creation for processing.
    host-url: https://${vega.env}-api-gateway-cpwajeq4.an.gateway.dev
    bucket-name: ${BUCKET_NAME}
