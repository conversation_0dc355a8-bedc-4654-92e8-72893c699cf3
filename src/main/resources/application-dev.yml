quarkus:
  http:
    port: 8084

  security:
    auth:
      enabled-in-dev-mode: false

  keycloak:
    admin-client:
      server-url: https://auth.vegaspread.cloud
      realm: dev-vega
      client-id: admin-cli
      client-secret: SECRET
      grant-type: CLIENT_CREDENTIALS
    api-key-auth:
      realm: dev-vega

  oidc:
    auth-server-url: https://auth.vegaspread.cloud/realms/dev-vega

  smallrye-openapi:
    oauth2-implicit-authorization-url: https://auth.vegaspread.cloud/realms/dev-vega/protocol/openid-connect/auth


vega:
  cloud:
    provider: gcp
  env: local
