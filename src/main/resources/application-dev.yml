quarkus:
  datasource:
    db-kind: mysql
    username: root
    password: root
    jdbc:
      url: *******************************************
  flyway:
    locations: db/migration/mysql
    migrate-at-start: true

  oidc:
    auth-server-url: https://auth.vegaspread.cloud/realms/dev-vega

  smallrye-openapi:
    oauth2-implicit-authorization-url: https://auth.vegaspread.cloud/realms/dev-vega/protocol/openid-connect/auth

  http:
    port: 8085

vega:
  cloud:
    provider: gcp
  env: dev
