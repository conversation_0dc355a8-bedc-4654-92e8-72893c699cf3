quarkus:
  datasource:
    username: root
    password: root
    jdbc:
      url: *********************************************

  live-reload:
    enabled: true

  oidc:
    auth-server-url: https://auth.vegaspread.cloud/realms/dev-vega

  smallrye-openapi:
    oauth2-implicit-authorization-url: https://auth.vegaspread.cloud/realms/dev-vega/protocol/openid-connect/auth

  rest-client:
    runpod:
      url: https://api.runpod.ai/v2/7a31shtmkg3sa0

  mailer:
    mock: false

wise:
  client:
    secret: client-secret

vega:
  env: dev
  api-client-name: dev-api-client
  frontend-host-url: https://dev.vegaspread.cloud
  cloud:
    provider: gcp
    host-url: http://localhost:8080
    bucket-name: dev-vegaspread
  json-upload-paths: json_uploads