quarkus:
  datasource:
    credentials-provider: aws-iam-provider
    username: ${AURORA_INSTANCE_USERNAME}
    jdbc:
      url: jdbc:mysql://${AURORA_INSTANCE_HOST}:3306/${AURORA_INSTANCE_DATABASE}
      additional-jdbc-properties:
        sslMode=VERIFY_CA

  oidc:
    tls:
      tls-configuration-name: vega-tls

  tls:
    vega-tls:
      trust-store:
        p12:
          path: ssl/rcbc-truststore.p12

  rest-client:
    tls:
      tls-configuration-name: vega-tls

  native:
    resources:
      includes: ssl/rcbc-truststore.p12

vega:
  cloud:
    provider: aws
    api-gateway-url: ${API_GATEWAY_URL}
    region: ${AURORA_INSTANCE_REGION:ap-southeast-1}
