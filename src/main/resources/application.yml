quarkus:
  smallrye-openapi:
    security-scheme: oauth2-implicit

  swagger-ui:
    always-include: true
    oauth-client-id: workflow-swagger-ui

  flyway:
    migrate-at-start: true

  hibernate-orm:
    physical-naming-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
    log:
      sql: false
  hibernate-envers:
    audit-strategy: org.hibernate.envers.strategy.ValidityAuditStrategy
    audit-strategy-validity-store-revend-timestamp: true
    audit-strategy-validity-revend-timestamp-field-name: revend_time

  otel:
    traces:
      exporter: none
    metrics:
      exporter: none
    logs:
      exporter: none
    enabled: true

  http:
    root-path: /vegaspread/api/v1/coa
    proxy:
      proxy-address-forwarding: true
      enable-forwarded-host: true
      enable-forwarded-prefix: true
    cors:
      enabled: true
      origins: "*"
    access-log:
      enabled: true

  index-dependency:
    common:
      group-id: com.walnut.vegaspread
      artifact-id: common

org:
  eclipse:
    microprofile:
      rest:
        client:
          propagateHeaders: Authorization,X-CLIENT-ID,X-API-KEY

vega:
  env: ${VEGA_ENV}
