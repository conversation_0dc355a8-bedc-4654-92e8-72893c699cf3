CREATE TABLE `coa_item_audit`
(
    `id`         INT                                   NOT NULL AUTO_INCREMENT,
    `coa_id`     INT                               NOT NULL,
    `col_name`   VARCHAR(255)                          NOT NULL,
    `prev_value` VARCHAR(255),
    `new_value`  VARCHAR(255),
    `action`     ENUM ('CREATED', 'UPDATED','DELETED') NOT NULL,
    `audit_time` DATETIME(6)                           NOT NULL,
    `audited_by` VARCHAR(255)                          NOT NULL,
    PRIMARY KEY (`id`)
);

CREATE TABLE `extracted_table_row_audit`
(
    `id`         INT                                   NOT NULL AUTO_INCREMENT,
    `table_id`   INT,
    `row_id`     TINYINT,
    `col_name`   VARCHAR(255)                          NOT NULL,
    `prev_value` VARCHAR(255),
    `new_value`  VARCHAR(255),
    `action`     ENUM ('CREATED', 'UPDATED','DELETED') NOT NULL,
    `audit_time` DATETIME(6)                           NOT NULL,
    `audited_by` VA<PERSON>HA<PERSON>(255)                          NOT NULL,
    <PERSON>IMAR<PERSON> KEY (`id`)
);