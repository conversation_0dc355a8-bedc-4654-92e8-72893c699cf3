CREATE TABLE `coa_map`
(
    `id`           INT      NOT NULL AUTO_INCREMENT,
    `spread_id`    INT     NOT NULL,
    `block_id`     INT     NOT NULL,
    `row_id`       TINYINT   NOT NULL,
    `new_coa_id`   INT      NOT NULL,
    `prev_coa_id`  INT      NOT NULL,
    `created_time` DATETIME(6)  NOT NULL,
    `action`       VARCHAR(255) NOT NULL,
    `created_by`   VARCHAR(255) NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

CREATE TABLE `table_tag`
(
    `id`           INT      NOT NULL AUTO_INCREMENT,
    `spread_id`    INT      NOT NULL,
    `block_id`     INT      NOT NULL,
    `new_tag`      VARCHAR(255) NOT NULL,
    `prev_tag`     VARCHAR(255) NOT NULL,
    `created_time` DATETIME(6)  NOT NULL,
    `action`       VARCHAR(255) NOT NULL,
    `created_by`   VARCHAR(255) NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

CREATE TABLE `fy_headers`
(
    `id`              INT      NOT NULL AUTO_INCREMENT,
    `spread_id`       INT      NOT NULL,
    `block_id`        INT      NOT NULL,
    `new_fy_headers`  VARCHAR(255) NOT NULL,
    `prev_fy_headers` VARCHAR(255) NOT NULL,
    `created_time`    DATETIME(6)  NOT NULL,
    `action`          VARCHAR(255) NOT NULL,
    `created_by`      VARCHAR(255) NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

CREATE TABLE `coa_item`
(
    `id`           INT      NOT NULL AUTO_INCREMENT,
    `coa_id`       INT      NOT NULL,
    `coa_text`     VARCHAR(255) NOT NULL,
    `client_name`  VARCHAR(255) NOT NULL,
    `created_time` DATETIME(6)  NOT NULL,
    `action`       VARCHAR(255) NOT NULL,
    `created_by`   VARCHAR(255) NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;