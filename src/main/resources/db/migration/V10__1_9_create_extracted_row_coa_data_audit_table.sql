CREATE TABLE `extracted_row_coa_data_audit`
(
    `id`         INT                                   NOT NULL AUTO_INCREMENT,
    `table_id`   INT                               NOT NULL,
    `row_id`     SMALLINT                           NOT NULL,
    `col_name`   VARCHAR(255)                          NOT NULL,
    `prev_value` VARCHAR(255),
    `new_value`  VARCHAR(255),
    `action`     ENUM ('CREATED', 'UPDATED','DELETED') NOT NULL,
    `audit_time` DATETIME(6)                           NOT NULL,
    `audited_by` VARCHAR(255)                          NOT NULL,
    PRIMARY KEY (`id`)
);


INSERT INTO `extracted_row_coa_data_audit`(`table_id`, `row_id`, `col_name`, `prev_value`, `new_value`, `action`, `audit_time`, `audited_by`)
    (SELECT `table_id`, `row_id`, `col_name`, `prev_value`, `new_value`, `action`, `audit_time`, `audited_by`
                FROM `extracted_table_row_audit`
                WHERE `col_name` = 'coa_id'
    );

DELETE FROM `extracted_table_row_audit` WHERE `col_name`= 'coa_id';

CREATE OR REPLACE VIEW v_spread_audited_block_mapping AS
WITH block_audits AS (SELECT etra.table_id AS audit_block_id
                      FROM extracted_table_row_audit etra
                      UNION
                      SELECT lba.block_id
                      FROM layout_block_audit lba
                      UNION
                      SELECT ercda.table_id
                      FROM extracted_row_coa_data_audit ercda)
SELECT ROW_NUMBER() OVER () AS id,
       doc.spread_id,
       lb.block_id          AS block_id
FROM ${workflow}.document doc
         JOIN
     ${extraction}.layout_block lb ON doc.doc_id = lb.doc_id
WHERE lb.block_id IN (SELECT audit_block_id
                      FROM block_audits)
ORDER BY doc.spread_id, lb.block_id;