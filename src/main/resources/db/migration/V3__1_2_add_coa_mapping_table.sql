CREATE TABLE coa_mapping
(
    `table_id`    INT          NOT NULL,
    `row_id`      TINYINT      NOT NULL,
    `doc_id`      BINARY(16)   NOT NULL,
    `table_type`  VARCHAR(255) NOT NULL,
    `row_parent`  VARCHAR(255) NOT NULL,
    `text`        VARCHAR(255) NOT NULL,
    `fs_header`   VARCHAR(255) NOT NULL,
    `fs_text`     VARCHAR(255) NOT NULL,
    `coa_id`      INT          NOT NULL,
    `is_approved` BOOLEAN      NOT NULL,
    PRIMARY KEY (`table_id`, `row_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;
