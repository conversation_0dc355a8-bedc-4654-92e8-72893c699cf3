-- Create revision entity sequence
CREATE TABLE metadata_rev_entity_seq (
    next_val BIGINT
) ENGINE=InnoDB;

INSERT INTO metadata_rev_entity_seq VALUES (1);

-- Create revision entity metadata table
CREATE TABLE metadata_rev_entity (
    id INT NOT NULL,
    TIMESTAMP BIGINT NOT NULL,
    trace_id VARCHAR(255),
    username VARCHAR(255),
    PRIMARY KEY (id)
) ENGINE=InnoDB;

-- Create coa_list_aud table with all required columns and inline FKs
CREATE TABLE coa_list_aud (
    coa_id INT NOT NULL,
    rev INT NOT NULL,
    revtype TINYINT,
    revend INT,
    revend_time DATETIME(6),
    coa_text VARCHAR(255),
    coa_text_mod BIT,
    is_active BIT,
    is_active_mod BIT,
    coa_description VARCHAR(255),
    coa_description_mod BIT,
    sign B<PERSON>,
    sign_mod BIT,
    lvl1_category_id INT,
    lvl1category_mod BIT,
    PRIMARY KEY (rev, coa_id),
    CONSTRAINT coa_list_aud_rev_fk FOREIGN KEY (rev) REFERENCES metadata_rev_entity (id),
    CONSTRAINT coa_list_aud_revend_fk FOREIGN KEY (revend) REFERENCES metadata_rev_entity (id)
) ENGINE=InnoDB;

-- Create lvl1_category_aud table with inline FKs
CREATE TABLE lvl1_category_aud (
    id INT NOT NULL,
    rev INT NOT NULL,
    revtype TINYINT,
    revend INT,
    revend_time DATETIME(6),
    category VARCHAR(255),
    category_mod BIT,
    last_modified_by VARCHAR(255),
    last_modified_by_mod BIT,
    PRIMARY KEY (rev, id),
    CONSTRAINT FKgw10yswklobuw1v2t0kqj24y1 FOREIGN KEY (rev) REFERENCES metadata_rev_entity (id),
    CONSTRAINT FKms1ftivsssmlnny1my19shupf FOREIGN KEY (revend) REFERENCES metadata_rev_entity (id)
) ENGINE=InnoDB;
