CREATE TABLE `table_tag`
(
    `id`           INT          NOT NULL AUTO_INCREMENT,
    `tag`          VA<PERSON>HAR(255) NOT NULL,
    `created_by`   VA<PERSON>HA<PERSON>(255) NOT NULL,
    `created_time` DATETIME(6)  NOT NULL,
    `last_modified_by`   VARCHAR(255) NOT NULL,
    `last_modified_time` DATETIME(6)  NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_table_tag_tag` (`tag`)
) ENGINE = InnoDB
   DEFAULT CHARSET = utf8mb4
   COLLATE = utf8mb4_general_ci;

INSERT INTO table_tag (tag, created_by, created_time, last_modified_by, last_modified_time)
VALUES ('NA', 'developer', NOW(), 'developer', NOW());