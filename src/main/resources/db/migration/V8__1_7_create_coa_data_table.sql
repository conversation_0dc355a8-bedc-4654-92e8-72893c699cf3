CREATE TABLE IF NOT EXISTS `coa_data`
(
    `id`        INT     NOT NULL AUTO_INCREMENT,
    `use_coa`   BOOLEAN NOT NULL,
    `coa_id`    INT     NOT NULL,
    `coa_score` TINYINT DEFAULT 0,
    PRIMARY KEY (id)
);
CREATE TABLE IF NOT EXISTS `extracted_row_coa_data`
(
    `table_id`          INT      NOT NULL,
    `row_id`            SMALLINT NOT NULL,
    `coa_data_id`       INT      NOT NULL,
    `explainability_id` INT,
    PRIMARY KEY (table_id, row_id, coa_data_id)
);

INSERT INTO coa_data(coa_id, coa_score, use_coa)
    (SELECT extracted_table_row.coa_id, extracted_table_row.coa_score, extracted_table_row.use_coa
     FROM extracted_table_row
     WHERE coa_id IS NOT NULL
     GROUP BY extracted_table_row.use_coa, extracted_table_row.coa_score, extracted_table_row.coa_id,
              extracted_table_row.coa_score, extracted_table_row.use_coa);

SET @increment_value = 0;
INSERT INTO extracted_row_coa_data (table_id, row_id, coa_data_id, explainability_id)
SELECT extracted_table_row.table_id,
       extracted_table_row.row_id,
       coa_data.id as coa_data_id,
       null
FROM extracted_table_row
         JOIN
     coa_data ON extracted_table_row.coa_id = coa_data.coa_id AND extracted_table_row.coa_score = coa_data.coa_score AND
                 extracted_table_row.use_coa = coa_data.use_coa
WHERE extracted_table_row.coa_id IS NOT NULL
ORDER BY extracted_table_row.table_id, extracted_table_row.row_id;

ALTER TABLE extracted_row_coa_data
    ADD CONSTRAINT fk_extracted_row
        FOREIGN KEY (row_id, table_id)
            REFERENCES extracted_table_row (row_id, table_id)
            ON DELETE CASCADE;

ALTER TABLE extracted_row_coa_data
    ADD CONSTRAINT fk_coa_data
        FOREIGN KEY (coa_data_id)
            REFERENCES coa_data (id)
            ON DELETE CASCADE;

ALTER TABLE extracted_row_coa_data
    ADD CONSTRAINT fk_explainability
        FOREIGN KEY (explainability_id)
            REFERENCES coa_mapping (id)
            ON DELETE CASCADE;

ALTER TABLE extracted_table_row
    DROP COLUMN use_coa;
ALTER TABLE extracted_table_row
    DROP COLUMN coa_id;
ALTER TABLE extracted_table_row
    DROP COLUMN coa_score;
