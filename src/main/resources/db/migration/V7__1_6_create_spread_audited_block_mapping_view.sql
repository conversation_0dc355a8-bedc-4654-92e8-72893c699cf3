CREATE OR REPLACE VIEW v_spread_audited_block_mapping AS
WITH block_audits AS (SELECT etra.table_id AS audit_block_id
                      FROM extracted_table_row_audit etra
                      UNION
                      SELECT lba.block_id
                      FROM layout_block_audit lba)
SELECT ROW_NUMBER() OVER () AS id,
       doc.spread_id,
       lb.block_id          AS block_id
FROM ${workflow}.document doc
         JOIN
     ${extraction}.layout_block lb ON doc.doc_id = lb.doc_id
WHERE lb.block_id IN (SELECT audit_block_id
                      FROM block_audits)
ORDER BY doc.spread_id, lb.block_id;