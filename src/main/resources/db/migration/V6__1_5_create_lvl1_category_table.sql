CREATE TABLE `lvl1_category`
(
    `id`                 INT          NOT NULL AUTO_INCREMENT,
    `category`           VARCHAR(255) NOT NULL,
    `created_by`         VARCHAR(255) NOT NULL,
    `created_time`       DATETIME(6)  NOT NULL,
    `last_modified_by`   VARCHAR(255) NOT NULL,
    `last_modified_time` DATETIME(6)  NOT NULL,

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_lvl1_category_category` (`category`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

  INSERT INTO lvl1_category (category, created_by, created_time, last_modified_by, last_modified_time)
  VALUES ('NA', 'developer', NOW(), 'developer', NOW());

  INSERT INTO lvl1_category (category, created_by, created_time, last_modified_by, last_modified_time)
  SELECT DISTINCT lvl1_category,'developer', NOW(),'developer', NOW()
  FROM coa_list
  WHERE lvl1_category != ''
  ORDER BY lvl1_category;