CREATE TABLE `subtotal_mapping`
(
    `doc_id`        BINARY(16)   NOT NULL,
    `table_id`      INT          NOT NULL,
    `row_id`        SMALLINT      NOT NULL,
    `subtotal_id` INT NOT NULL,
    PRIMARY KEY (`table_id`, `row_id`),
    UNIQUE (`doc_id`, `subtotal_id`),
    CONSTRAINT fk_subtotal_mapping_extracted_table_row FOREIGN KEY (table_id,row_id) REFERENCES extracted_table_row (table_id,row_id),
    CONSTRAINT fk_subtotal_id FOREIGN KEY (subtotal_id) REFERENCES subtotal(id) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;