CREATE TABLE `review`
(
    `id`            INT         NOT NULL AUTO_INCREMENT,
    `doc_id`        BINARY(16)  NOT NULL,
    `reviewer`      VARCHAR(255)                                                                      DEFAULT NULL,
    `review_level`  INT                                                                               DEFAULT NULL,
    `status`        ENUM ('PENDING', 'SENT_FOR_APPROVAL','APPROVED','CHANGES_REQUESTED','REASSIGNED') DEFAULT NULL,
    `reviewed_time` DATETIME(6) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `fk_review_document_id` (`doc_id`),
    CONSTRAINT `fk_review_document_id` FOREIGN KEY (`doc_id`) REFERENCES `document` (`doc_id`) ON DELETE CASCADE
);

ALTER TABLE `document`
    MODIFY COLUMN
        `status` ENUM ('COMPLETED','CREATED','DRAFT','PROCESSING',
        'TO_REVIEW','UNDER_REVIEW_CLIENT','UNDER_REVIEW_WALNUT', 'UNDER_REVIEW_LVL_1',
        'UNDER_REVIEW_LVL_2','FAILED') NOT NULL;
