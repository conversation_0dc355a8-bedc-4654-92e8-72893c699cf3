-- Add foreign key column to coa list table
ALTER TABLE coa_list ADD COLUMN lvl1_category_id INT;

-- Backfill lvl1_category_id using a join (map '' to 'NA')
UPDATE coa_list
SET lvl1_category_id = (
    SELECT id FROM lvl1_category WHERE category =
        CASE WHEN coa_list.lvl1_category = '' THEN 'NA' ELSE coa_list.lvl1_category END
);

-- Drop the old lvl1_category column
ALTER TABLE coa_list DROP COLUMN lvl1_category;

-- Add NOT NULL and foreign key constraint
ALTER TABLE coa_list MODIFY COLUMN lvl1_category_id INT NOT NULL;
ALTER TABLE coa_list ADD CONSTRAINT fk_lvl1_category FOREIGN KEY (lvl1_category_id) REFERENCES lvl1_category(id);
