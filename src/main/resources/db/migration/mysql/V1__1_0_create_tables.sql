CREATE TABLE IF NOT EXISTS table_tag (
    block_id INT,
    doc_id BINARY(16) NOT NULL,
    last_modified_time DATETIME(6)  NOT NULL,
    tag VARCHAR(255) NOT NULL,
    embedding vector(768) USING VARBINARY NOT NULL,
    PRIMARY KEY (block_id)
);

CREATE TABLE IF NOT EXISTS fs_tag(
    block_id INT,
    doc_id BINARY(16) NOT NULL,
    last_modified_time DATETIME(6)  NOT NULL,
    embedding VECTOR(768) USING VARBINARY NOT NULL,
    tag VARCHAR(255) NOT NULL,
    entity_id INT NOT NULL,
    entity_name VARCHAR(255) NOT NULL,
    spread_level VARCHAR(255) NOT NULL,
    PRIMARY KEY (block_id)
);