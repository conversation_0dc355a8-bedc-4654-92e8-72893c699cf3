-- Must drop vector index before altering the table
ALTER TABLE fs_tag
    DROP INDEX vector_index;

-- Add tag id column for fs tag table
ALTER TABLE fs_tag
    ADD COLUMN tag_id INT NOT NULL DEFAULT 1;
ALTER TABLE fs_tag
    MODIFY COLUMN tag VARCHAR(255) DEFAULT NULL;

CREATE
VECTOR INDEX vector_index
ON fs_tag(embedding)
USING SCANN QUANTIZER = SQ8 DISTANCE_MEASURE = COSINE;

-- Must drop vector index before altering the table
ALTER TABLE table_tag
    DROP INDEX vector_index;

-- Add tag id column for table tag table
ALTER TABLE table_tag
    ADD COLUMN tag_id INT NOT NULL DEFAULT 1;

ALTER TABLE table_tag MODIFY COLUMN tag VARCHAR(255) DEFAULT NULL;

-- Drop the tag index
ALTER TABLE table_tag
    DROP INDEX tag_index;

CREATE
VECTOR INDEX vector_index
ON table_tag(embedding)
USING SCANN QUANTIZER = SQ8 DISTANCE_MEASURE = COSINE;
