ALTER TABLE coa_task
      ADD COLUMN entity_name_id  INT NOT NULL DEFAULT 1;

ALTER TABLE coa_task
      ADD COLUMN industry_id  INT NOT NULL DEFAULT 1;

ALTER TABLE coa_task
      ADD COLUMN region_id  INT NOT NULL DEFAULT 1;

ALTER TABLE coa_task DROP COLUMN spread_id;
ALTER TABLE coa_task DROP COLUMN block_id;
ALTER TABLE coa_task DROP COLUMN row_id;
ALTER TABLE coa_task DROP COLUMN row_parent;
ALTER TABLE coa_task DROP COLUMN fs_header;
ALTER TABLE coa_task DROP COLUMN instances;
ALTER TABLE coa_task DROP COLUMN status;
ALTER TABLE coa_task DROP COLUMN created_by;
ALTER TABLE coa_task DROP COLUMN created_time;
ALTER TABLE coa_task DROP COLUMN reviewed_by;
ALTER TABLE coa_task DROP COLUMN reviewed_time;
