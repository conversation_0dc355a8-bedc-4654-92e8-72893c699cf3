package com.walnut.vegaspread.audit.entity.workflow;

import com.walnut.vegaspread.audit.entity.common.BaseAuditEntity;
import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import com.walnut.vegaspread.common.model.audit.workflow.ReviewAuditDto;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.ws.rs.core.Response;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

@Accessors(chain = true)
@Entity
@Table(name = ReviewAuditEntity.REVIEW_AUDIT_TABLE_NAME)
@Getter
@Setter
public class ReviewAuditEntity extends BaseAuditEntity {

    public static final String REVIEW_AUDIT_TABLE_NAME = "review_audit";
    public static final String REVIEW_ID_COL_NAME = "review_id";

    @Column(name = REVIEW_ID_COL_NAME, nullable = false)
    private Integer reviewId;

    public static List<ReviewAuditDto.Response> toDtoList(List<ReviewAuditEntity> reviewAuditEntities) {
        return reviewAuditEntities.stream()
                .map(ReviewAuditEntity::toDto)
                .toList();
    }

    @Override
    public ReviewAuditEntity setAction(AuditStatus action) {
        if (action.equals(AuditStatus.DELETED)) {
            ResponseException.throwResponseException(Response.Status.BAD_REQUEST,
                    "Invalid action for review audit entity: " + AuditStatus.DELETED);
        }
        super.setAction(action);
        return this;
    }

    public ReviewAuditDto.Response toDto() {
        return new ReviewAuditDto.Response(
                this.getId(),
                this.getReviewId(),
                this.getColName(),
                this.getPrevValue() == null ? "NA" : this.getPrevValue(),
                this.getNewValue() == null ? "NA" : this.getNewValue(),
                this.getAction(),
                this.getAuditTime(),
                this.getAuditedBy(),
                this.getAuditedBYFullName() == null ? "NA" : this.getAuditedBYFullName()
        );
    }
}
