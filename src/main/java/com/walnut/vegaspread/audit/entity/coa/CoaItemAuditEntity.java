package com.walnut.vegaspread.audit.entity.coa;

import com.walnut.vegaspread.audit.entity.common.BaseAuditEntity;
import com.walnut.vegaspread.common.model.audit.coa.CoaItemAuditDto;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

@Accessors(chain = true)
@Entity
@Table(name = CoaItemAuditEntity.TABLE_NAME)
@Getter
@Setter
public class CoaItemAuditEntity extends BaseAuditEntity {

    public static final String TABLE_NAME = "coa_item_audit";

    public static final String COA_ID_COL_NAME = "coa_id";

    @Column(name = COA_ID_COL_NAME, nullable = false)
    private Integer coaId;

    public static List<CoaItemAuditDto.Response> toDtoList(List<CoaItemAuditEntity> coaItemAuditEntities) {
        return coaItemAuditEntities.stream()
                .map(CoaItemAuditEntity::toDto)
                .toList();
    }

    public CoaItemAuditDto.Response toDto() {
        return new CoaItemAuditDto.Response(
                this.getId(),
                this.getCoaId(),
                this.getColName(),
                this.getPrevValue() == null ? "NA" : this.getPrevValue(),
                this.getNewValue() == null ? "NA" : this.getNewValue(),
                this.getAction(),
                this.getAuditTime(),
                this.getAuditedBy(),
                this.getAuditedBYFullName() == null ? "NA" : this.getAuditedBYFullName()
        );
    }
}
