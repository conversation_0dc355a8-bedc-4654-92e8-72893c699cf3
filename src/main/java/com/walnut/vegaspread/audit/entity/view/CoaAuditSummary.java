package com.walnut.vegaspread.audit.entity.view;

import com.walnut.vegaspread.audit.model.view.CoaAuditSummaryAuditType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import org.hibernate.annotations.Immutable;

import java.util.UUID;

@Entity
@Immutable
@Table(name = CoaAuditSummary.TABLE_NAME)
@Getter
public class CoaAuditSummary {
    public static final String TABLE_NAME = "v_coa_audit_summary";
    public static final String DOCUMENT_ID_COL_NAME = "doc_id";
    public static final String AUDIT_TYPE_COL_NAME = "audit_type";
    public static final String CATEGORY_COL_NAME = "category";
    public static final String ID_COL_NAME = "id";
    public static final String PREV_COA_ID_COL_NAME = "prev_coa_id";
    public static final String NEW_COA_ID_COL_NAME = "new_coa_id";

    @Id
    @Column(name = ID_COL_NAME, nullable = false)
    private Long id;

    @Column(name = CATEGORY_COL_NAME, nullable = false)
    private String category;

    @Column(name = PREV_COA_ID_COL_NAME, nullable = false)
    private Long prevCoaId;

    @Column(name = NEW_COA_ID_COL_NAME, nullable = false)
    private Long newCoaId;

    @Column(name = DOCUMENT_ID_COL_NAME, nullable = false)
    private UUID docId;

    @Enumerated(EnumType.STRING)
    @Column(name = AUDIT_TYPE_COL_NAME, nullable = false)
    private CoaAuditSummaryAuditType auditType;
}
