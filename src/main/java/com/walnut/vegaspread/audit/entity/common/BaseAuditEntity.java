package com.walnut.vegaspread.audit.entity.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Accessors(chain = true)
@MappedSuperclass
@Getter
@Setter
public class BaseAuditEntity {
    public static final String AUDIT_ID_COL_NAME = "id";
    public static final String COLUMN_NAME_COL_NAME = "col_name";
    public static final String PREVIOUS_VALUE_COL_NAME = "prev_value";
    public static final String NEW_VALUE_COL_NAME = "new_value";
    public static final String ACTION_COL_NAME = "action";
    public static final String AUDIT_TIME_COL_NAME = "audit_time";
    public static final String AUDITED_BY_COL_NAME = "audited_by";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = AUDIT_ID_COL_NAME, nullable = false)
    private Integer id;

    @Column(name = COLUMN_NAME_COL_NAME, nullable = false)
    private String colName;

    @Column(name = PREVIOUS_VALUE_COL_NAME)
    private String prevValue;

    @Column(name = NEW_VALUE_COL_NAME)
    private String newValue;

    @Enumerated(EnumType.STRING)
    @Column(name = ACTION_COL_NAME, nullable = false)
    private AuditStatus action;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Column(name = AUDIT_TIME_COL_NAME, nullable = false)
    private LocalDateTime auditTime;

    @Column(name = AUDITED_BY_COL_NAME, nullable = false)
    private String auditedBy;

    @Transient
    private String auditedBYFullName;

}