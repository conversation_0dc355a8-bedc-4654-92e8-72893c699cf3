package com.walnut.vegaspread.audit.entity.view;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import org.hibernate.annotations.Immutable;

@Entity
@Immutable
@Table(name = SpreadAuditedBlockMapping.TABLE_NAME)
@Getter
public class SpreadAuditedBlockMapping {
    public static final String TABLE_NAME = "v_spread_audited_block_mapping";
    public static final String ID_COL_NAME = "id";
    public static final String SPREAD_ID_COL_NAME = "spread_id";
    public static final String BLOCK_ID_COL_NAME = "block_id";

    @Id
    @Column(name = ID_COL_NAME, nullable = false)
    private Long id;

    @Column(name = SPREAD_ID_COL_NAME, nullable = false)
    private Integer spreadId;

    @Column(name = BLOCK_ID_COL_NAME, nullable = false)
    private Integer blockId;
}
