package com.walnut.vegaspread.audit.entity.view;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.UUID;

@Accessors(chain = true)
@Entity
@Table(name = AuditMetricsEntity.TABLE_NAME)
@Getter
@Setter
@NoArgsConstructor
public class AuditMetricsEntity {
    public static final String TABLE_NAME = "audit_metrics";
    private static final String ID_COL_NAME = "id";
    private static final String DOC_ID_COL_NAME = "doc_id";
    private static final String CATEGORY_COL_NAME = "category";
    private static final String ACCURACY_COL_NAME = "accuracy";
    private static final String COMPLETENESS_COL_NAME = "completeness";
    private static final String CREATED_TIME_COL_NAME = "created_time";
    private static final String CREATED_BY_COL_NAME = "created_by";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = ID_COL_NAME, nullable = false)
    private Long id;

    @Column(name = DOC_ID_COL_NAME, nullable = false)
    private UUID docId;

    @Column(name = CATEGORY_COL_NAME, nullable = false)
    private String category;

    @Column(name = ACCURACY_COL_NAME, nullable = false)
    private Integer accuracy;

    @Column(name = COMPLETENESS_COL_NAME, nullable = false)
    private Integer completeness;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Column(name = CREATED_TIME_COL_NAME, nullable = false)
    private LocalDateTime createdTime;

    @Column(name = CREATED_BY_COL_NAME, nullable = false)
    private String createdBy;

    @Transient
    private String createdByFullName;
}
