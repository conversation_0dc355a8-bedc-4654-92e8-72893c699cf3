package com.walnut.vegaspread.audit.entity.extraction;

import com.walnut.vegaspread.audit.entity.common.BaseAuditEntity;
import com.walnut.vegaspread.common.model.audit.extraction.SubtotalAuditDto;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Entity
@Table(name = SubtotalAuditEntity.TABLE_NAME)
public class SubtotalAuditEntity extends BaseAuditEntity {
    public static final String TABLE_NAME = "subtotal_audit";

    public static final String SUBTOTAL_ID_COL_NAME = "subtotal_id";
    @NotNull
    @Column(name = SUBTOTAL_ID_COL_NAME, nullable = false)
    private Integer subtotalId;

    public static List<SubtotalAuditDto.Response> toDtoList(
            List<SubtotalAuditEntity> subtotalAuditEntities) {
        return subtotalAuditEntities.stream()
                .map(SubtotalAuditEntity::toDto)
                .toList();
    }

    public SubtotalAuditDto.Response toDto() {
        return new SubtotalAuditDto.Response(
                this.getId(),
                this.getSubtotalId(),
                this.getColName(),
                this.getPrevValue(),
                this.getNewValue(),
                this.getAction(),
                this.getAuditTime(),
                this.getAuditedBy(),
                this.getAuditedBYFullName()
        );
    }
}