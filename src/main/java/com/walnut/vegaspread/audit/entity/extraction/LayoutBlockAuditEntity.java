package com.walnut.vegaspread.audit.entity.extraction;

import com.walnut.vegaspread.audit.entity.common.BaseAuditEntity;
import com.walnut.vegaspread.common.model.audit.extraction.LayoutBlockAuditDto;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

@Accessors(chain = true)
@Entity
@Table(name = LayoutBlockAuditEntity.TABLE_NAME)
@Getter
@Setter
public class LayoutBlockAuditEntity extends BaseAuditEntity {
    public static final String TABLE_NAME = "layout_block_audit";
    public static final String BLOCK_ID_COL_NAME = "block_id";

    @Column(name = BLOCK_ID_COL_NAME, nullable = false)
    private Integer blockId;

    public static List<LayoutBlockAuditDto.Response> toDtoList(List<LayoutBlockAuditEntity> layoutBlockAuditEntities) {
        return layoutBlockAuditEntities.stream()
                .map(LayoutBlockAuditEntity::toDto)
                .toList();
    }

    public LayoutBlockAuditDto.Response toDto() {
        return new LayoutBlockAuditDto.Response(
                this.getId(),
                this.getBlockId(),
                this.getColName(),
                this.getPrevValue() == null ? "NA" : this.getPrevValue(),
                this.getNewValue() == null ? "NA" : this.getNewValue(),
                this.getAction(),
                this.getAuditTime(),
                this.getAuditedBy(),
                this.getAuditedBYFullName() == null ? "NA" : this.getAuditedBYFullName()
        );
    }
}
