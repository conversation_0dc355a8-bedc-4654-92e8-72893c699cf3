package com.walnut.vegaspread.audit.entity.workflow;

import com.walnut.vegaspread.audit.entity.common.BaseAuditEntity;
import com.walnut.vegaspread.common.model.audit.workflow.DocumentAuditDto;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.UUID;

@Accessors(chain = true)
@Entity
@Table(name = DocumentAuditEntity.DOCUMENT_AUDIT_TABLE_NAME)
@Getter
@Setter
public class DocumentAuditEntity extends BaseAuditEntity {
    public static final String DOCUMENT_AUDIT_TABLE_NAME = "document_audit";
    public static final String DOCUMENT_ID_COL_NAME = "doc_id";

    @Column(name = DOCUMENT_ID_COL_NAME, nullable = false)
    private UUID docId;

    public static List<DocumentAuditDto.Response> toDtoList(List<DocumentAuditEntity> documentAuditEntities) {
        return documentAuditEntities.stream()
                .map(DocumentAuditEntity::toDto)
                .toList();
    }

    public DocumentAuditDto.Response toDto() {
        return new DocumentAuditDto.Response(
                this.getId(),
                this.getDocId(),
                this.getColName(),
                this.getPrevValue() == null ? "NA" : this.getPrevValue(),
                this.getNewValue() == null ? "NA" : this.getNewValue(),
                this.getAction(),
                this.getAuditTime(),
                this.getAuditedBy(),
                this.getAuditedBYFullName() == null ? "NA" : this.getAuditedBYFullName()
        );
    }
}
