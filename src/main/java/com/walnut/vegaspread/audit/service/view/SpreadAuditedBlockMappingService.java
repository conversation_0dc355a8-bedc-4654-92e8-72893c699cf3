package com.walnut.vegaspread.audit.service.view;

import com.walnut.vegaspread.audit.repository.view.SpreadAuditedBlockMappingRepository;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

@ApplicationScoped
public class SpreadAuditedBlockMappingService {

    private final SpreadAuditedBlockMappingRepository spreadAuditedBlockMappingRepository;

    public SpreadAuditedBlockMappingService(SpreadAuditedBlockMappingRepository spreadAuditedBlockMappingRepository) {
        this.spreadAuditedBlockMappingRepository = spreadAuditedBlockMappingRepository;
    }

    public List<Integer> getAuditedBlockIdsForSpread(Integer spreadId) {
        return spreadAuditedBlockMappingRepository.getAuditedBlockIdsForSpread(spreadId);
    }
}
