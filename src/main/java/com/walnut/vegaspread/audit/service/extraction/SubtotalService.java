package com.walnut.vegaspread.audit.service.extraction;

import com.walnut.vegaspread.audit.entity.extraction.SubtotalAuditEntity;
import com.walnut.vegaspread.audit.repository.extraction.SubtotalAuditRepository;
import com.walnut.vegaspread.audit.service.common.ExchangeService;
import com.walnut.vegaspread.common.model.audit.extraction.SubtotalAuditDto;
import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import org.eclipse.microprofile.jwt.JsonWebToken;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.walnut.vegaspread.common.utils.Jwt.getUsername;

@ApplicationScoped
public class SubtotalService {
    private final JsonWebToken accessToken;
    private final SubtotalAuditRepository subtotalAuditRepository;
    private final ExchangeService exchangeService;

    public SubtotalService(JsonWebToken accessToken,
                           SubtotalAuditRepository subtotalAuditRepository,
                           ExchangeService exchangeService) {

        this.accessToken = accessToken;
        this.subtotalAuditRepository = subtotalAuditRepository;
        this.exchangeService = exchangeService;
    }

    @Transactional
    public List<SubtotalAuditDto.Response> auditForCreate(
            List<SubtotalAuditDto.Create> subtotalAuditCreateDtos) {
        List<SubtotalAuditEntity> subtotalAuditEntities = new ArrayList<>();
        if (subtotalAuditCreateDtos == null || subtotalAuditCreateDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String username = getUsername(accessToken);
            String fullName = exchangeService.getNameFromUsername(username);
            for (SubtotalAuditDto.Create subtotalAuditCreateDto : subtotalAuditCreateDtos) {
                SubtotalAuditEntity subtotalAuditEntity = new SubtotalAuditEntity();
                subtotalAuditEntity.setSubtotalId(subtotalAuditCreateDto.subtotalId());
                subtotalAuditEntity.setColName(subtotalAuditCreateDto.colName());
                subtotalAuditEntity.setPrevValue(null);
                subtotalAuditEntity.setNewValue(subtotalAuditCreateDto.newValue());
                subtotalAuditEntity.setAuditTime(LocalDateTime.now());
                subtotalAuditEntity.setAuditedBy(username);
                subtotalAuditEntity.setAuditedBYFullName(fullName);
                subtotalAuditEntity.setAction(AuditStatus.CREATED);
                subtotalAuditEntities.add(subtotalAuditEntity);
            }
            subtotalAuditRepository.persist(subtotalAuditEntities);
            return SubtotalAuditEntity.toDtoList(subtotalAuditEntities);
        }
    }

    @Transactional
    public List<SubtotalAuditDto.Response> auditForUpdate(
            List<SubtotalAuditDto.Update> subtotalAuditUpdateDtos,
            String username) {
        List<SubtotalAuditEntity> subtotalAuditEntities = new ArrayList<>();
        if (subtotalAuditUpdateDtos == null || subtotalAuditUpdateDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String fullName = username;
            if (username == null) {
                username = getUsername(accessToken);
                fullName = exchangeService.getNameFromUsername(username);
            }
            for (SubtotalAuditDto.Update subtotalAuditUpdateDto : subtotalAuditUpdateDtos) {
                SubtotalAuditEntity subtotalAuditEntity = new SubtotalAuditEntity();
                subtotalAuditEntity.setSubtotalId(subtotalAuditUpdateDto.subtotalId());
                subtotalAuditEntity.setColName(subtotalAuditUpdateDto.colName());
                subtotalAuditEntity.setPrevValue(subtotalAuditUpdateDto.prevValue());
                subtotalAuditEntity.setNewValue(subtotalAuditUpdateDto.newValue());
                subtotalAuditEntity.setAuditTime(LocalDateTime.now());
                subtotalAuditEntity.setAuditedBy(username);
                subtotalAuditEntity.setAuditedBYFullName(fullName);
                subtotalAuditEntity.setAction(AuditStatus.UPDATED);
                subtotalAuditEntities.add(subtotalAuditEntity);
            }
            subtotalAuditRepository.persist(subtotalAuditEntities);
            return SubtotalAuditEntity.toDtoList(subtotalAuditEntities);
        }
    }

    @Transactional
    public List<SubtotalAuditDto.Response> auditForDelete(
            List<SubtotalAuditDto.Delete> subtotalAuditDeleteDtos, String username) {
        List<SubtotalAuditEntity> subtotalAuditEntities = new ArrayList<>();
        if (subtotalAuditDeleteDtos == null || subtotalAuditDeleteDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String fullName = username;
            if (username == null) {
                username = getUsername(accessToken);
                fullName = exchangeService.getNameFromUsername(username);
            }
            for (SubtotalAuditDto.Delete subtotalAuditDeleteDto : subtotalAuditDeleteDtos) {
                SubtotalAuditEntity subtotalAuditEntity = new SubtotalAuditEntity();
                subtotalAuditEntity.setSubtotalId(subtotalAuditDeleteDto.subtotalId());
                subtotalAuditEntity.setColName(subtotalAuditDeleteDto.colName());
                subtotalAuditEntity.setPrevValue(subtotalAuditDeleteDto.prevValue());
                subtotalAuditEntity.setNewValue(null);
                subtotalAuditEntity.setAuditTime(LocalDateTime.now());
                subtotalAuditEntity.setAuditedBy(username);
                subtotalAuditEntity.setAuditedBYFullName(fullName);
                subtotalAuditEntity.setAction(AuditStatus.DELETED);
                subtotalAuditEntities.add(subtotalAuditEntity);
            }
            subtotalAuditRepository.persist(subtotalAuditEntities);
            return SubtotalAuditEntity.toDtoList(subtotalAuditEntities);
        }
    }
}
