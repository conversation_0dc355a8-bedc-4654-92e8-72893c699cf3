package com.walnut.vegaspread.audit.service.workflow;

import com.walnut.vegaspread.audit.entity.workflow.ReviewAuditEntity;
import com.walnut.vegaspread.audit.repository.workflow.ReviewAuditRepository;
import com.walnut.vegaspread.audit.service.common.ExchangeService;
import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import com.walnut.vegaspread.common.model.audit.workflow.ReviewAuditDto;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.PathParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.eclipse.microprofile.jwt.JsonWebToken;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.walnut.vegaspread.common.utils.Jwt.getUsername;

@ApplicationScoped
public class ReviewAuditService {
    private static final Logger logger = LogManager.getLogger(ReviewAuditService.class);
    private final JsonWebToken accessToken;
    private final ReviewAuditRepository reviewAuditRepository;
    private final ExchangeService exchangeService;

    public ReviewAuditService(JsonWebToken accessToken, ReviewAuditRepository reviewAuditRepository,
                              ExchangeService exchangeService) {
        this.accessToken = accessToken;
        this.reviewAuditRepository = reviewAuditRepository;
        this.exchangeService = exchangeService;
    }

    @Transactional
    public List<ReviewAuditDto.Response> auditForCreate(List<ReviewAuditDto.Create> reviewAuditCreateDtos) {
        List<ReviewAuditEntity> reviewAuditEntities = new ArrayList<>();
        if (reviewAuditCreateDtos == null || reviewAuditCreateDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String username = getUsername(accessToken);
            String fullName = exchangeService.getNameFromUsername(username);
            for (ReviewAuditDto.Create reviewAuditCreateDto : reviewAuditCreateDtos) {
                ReviewAuditEntity reviewAuditEntity = new ReviewAuditEntity();
                reviewAuditEntity.setReviewId(reviewAuditCreateDto.reviewId());
                reviewAuditEntity.setColName(reviewAuditCreateDto.colName());
                reviewAuditEntity.setPrevValue(null);
                reviewAuditEntity.setNewValue(reviewAuditCreateDto.newValue());
                reviewAuditEntity.setAuditTime(LocalDateTime.now());
                reviewAuditEntity.setAuditedBy(username);
                reviewAuditEntity.setAuditedBYFullName(fullName);
                reviewAuditEntity.setAction(AuditStatus.CREATED);
                reviewAuditEntities.add(reviewAuditEntity);
            }
            reviewAuditRepository.persist(reviewAuditEntities);
            return ReviewAuditEntity.toDtoList(reviewAuditEntities);
        }
    }

    @Transactional
    public List<ReviewAuditDto.Response> auditForUpdate(List<ReviewAuditDto.Update> reviewAuditUpdateDtos) {
        List<ReviewAuditEntity> reviewAuditEntities = new ArrayList<>();
        if (reviewAuditUpdateDtos == null || reviewAuditUpdateDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String username = getUsername(accessToken);
            String fullName = exchangeService.getNameFromUsername(username);
            for (ReviewAuditDto.Update reviewAuditUpdateDto : reviewAuditUpdateDtos) {
                ReviewAuditEntity reviewAuditEntity = new ReviewAuditEntity();
                reviewAuditEntity.setReviewId(reviewAuditUpdateDto.reviewId());
                reviewAuditEntity.setColName(reviewAuditUpdateDto.colName());
                reviewAuditEntity.setPrevValue(reviewAuditUpdateDto.prevValue());
                reviewAuditEntity.setNewValue(reviewAuditUpdateDto.newValue());
                reviewAuditEntity.setAuditTime(LocalDateTime.now());
                reviewAuditEntity.setAuditedBy(username);
                reviewAuditEntity.setAuditedBYFullName(fullName);
                reviewAuditEntity.setAction(AuditStatus.UPDATED);
                reviewAuditEntities.add(reviewAuditEntity);
            }
            reviewAuditRepository.persist(reviewAuditEntities);
            return ReviewAuditEntity.toDtoList(reviewAuditEntities);
        }
    }

    public ReviewAuditDto.Response get(@PathParam("auditId") Integer id) {
        Optional<ReviewAuditEntity> optReviewAuditEntity = reviewAuditRepository.findByIdOptional(id);
        if (optReviewAuditEntity.isPresent()) {
            return optReviewAuditEntity.get().toDto();
        } else {
            logger.error("Failed to fetch review audit data for missing id {}", id);
            return null;
        }
    }

    public List<ReviewAuditDto.Response> getByReviewId(@PathParam("reviewId") Integer reviewId) {
        return ReviewAuditEntity.toDtoList(reviewAuditRepository.findByReviewId(reviewId));
    }
}
