package com.walnut.vegaspread.audit.service.common;

import com.walnut.vegaspread.common.clients.ClientFactory;
import com.walnut.vegaspread.common.clients.CoaClient;
import com.walnut.vegaspread.common.clients.ExtractionClient;
import com.walnut.vegaspread.common.clients.IamClient;
import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.common.model.cloud.CloudPlatform;
import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import com.walnut.vegaspread.common.utils.ConfigKeys;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@ApplicationScoped
public class ExchangeService {

    private final CoaClient coaClient;
    private final IamClient iamClient;
    private final ExtractionClient extractionClient;

    public ExchangeService(@ConfigProperty(name = ConfigKeys.ENV_NAME_KEY) String envName,
                           @ConfigProperty(name = ConfigKeys.CLOUD_PROVIDER_TYPE) String cloudProviderType,
                           @ConfigProperty(name = ConfigKeys.AWS_GATEWAY_URL) Optional<String> awsGatewayUrlOptional) {
        if (cloudProviderType.equals(CloudPlatform.GCP.getProvider())) {
            this.iamClient = ClientFactory.createClient(IamClient.class, envName);
            this.coaClient = ClientFactory.createClient(CoaClient.class, envName);
            this.extractionClient = ClientFactory.createClient(ExtractionClient.class, envName);
        } else {
            if (awsGatewayUrlOptional.isEmpty()) {
                ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR,
                        "AWS Gateway URL is not configured");
            }
            String awsGatewayUrl = awsGatewayUrlOptional.get();
            this.iamClient = ClientFactory.createAWSClient(IamClient.class, envName, awsGatewayUrl);
            this.coaClient = ClientFactory.createAWSClient(CoaClient.class, envName, awsGatewayUrl);
            this.extractionClient = ClientFactory.createAWSClient(ExtractionClient.class, envName, awsGatewayUrl);
        }
    }

    public Map<String, String> getNamesFromUsernames(List<String> usernames) {
        return iamClient.getNamesFromUsernames(usernames);
    }

    public String getNameFromUsername(String username) {
        return iamClient.getNameFromUsername(username);
    }

    public List<CoaItemDto> getCoas(List<Integer> coaIds) {
        return coaClient.get(coaIds);
    }

    public List<Integer> getBlockIdsForDoc(UUID docId) {
        return extractionClient.getBlockIdsForDoc(docId);
    }

    public Map<Integer, String> getCategoryForCoaIds(List<Integer> coaIds) {
        return coaClient.get(coaIds).stream().collect(Collectors.toMap(CoaItemDto::coaId, CoaItemDto::lvl1Category));
    }
}
