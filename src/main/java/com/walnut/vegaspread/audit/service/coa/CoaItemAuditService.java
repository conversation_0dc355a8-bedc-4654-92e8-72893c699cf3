package com.walnut.vegaspread.audit.service.coa;

import com.walnut.vegaspread.audit.entity.coa.CoaItemAuditEntity;
import com.walnut.vegaspread.audit.repository.coa.CoaItemAuditRepository;
import com.walnut.vegaspread.audit.service.common.ExchangeService;
import com.walnut.vegaspread.common.model.audit.coa.CoaItemAuditDto;
import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import org.eclipse.microprofile.jwt.JsonWebToken;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.walnut.vegaspread.common.utils.Jwt.getUsername;

@ApplicationScoped
public class CoaItemAuditService {

    private final JsonWebToken accessToken;
    private final CoaItemAuditRepository coaItemAuditRepository;
    private final ExchangeService exchangeService;

    public CoaItemAuditService(JsonWebToken accessToken, CoaItemAuditRepository coaItemAuditRepository,
                               ExchangeService exchangeService) {
        this.accessToken = accessToken;
        this.coaItemAuditRepository = coaItemAuditRepository;
        this.exchangeService = exchangeService;
    }

    @Transactional
    public List<CoaItemAuditDto.Response> auditForCreate(List<CoaItemAuditDto.Create> coaItemAuditEntityCreateDtos) {
        List<CoaItemAuditEntity> coaItemAuditEntities = new ArrayList<>();
        if (coaItemAuditEntityCreateDtos == null || coaItemAuditEntityCreateDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String username = getUsername(accessToken);
            String fullName = exchangeService.getNameFromUsername(username);
            for (CoaItemAuditDto.Create coaItemAuditEntityCreateDto : coaItemAuditEntityCreateDtos) {
                CoaItemAuditEntity coaItemAuditEntity = new CoaItemAuditEntity();
                coaItemAuditEntity.setCoaId(coaItemAuditEntityCreateDto.coaId())
                        .setColName(coaItemAuditEntityCreateDto.colName())
                        .setPrevValue(null)
                        .setNewValue(coaItemAuditEntityCreateDto.newValue())
                        .setAuditTime(LocalDateTime.now())
                        .setAuditedBy(username)
                        .setAuditedBYFullName(fullName)
                        .setAction(AuditStatus.CREATED);
                coaItemAuditEntities.add(coaItemAuditEntity);
            }
            coaItemAuditRepository.persist(coaItemAuditEntities);
            return CoaItemAuditEntity.toDtoList(coaItemAuditEntities);
        }
    }

    public List<CoaItemAuditDto.Response> list() {
        List<CoaItemAuditEntity> coaItemAuditEntities = coaItemAuditRepository.listAll();
        Map<String, String> nameMappings = exchangeService.getNamesFromUsernames(
                coaItemAuditEntities.stream().map(CoaItemAuditEntity::getAuditedBy).distinct().toList());
        coaItemAuditEntities.forEach(coaItemAuditEntity ->
                coaItemAuditEntity.setAuditedBYFullName(nameMappings.get(coaItemAuditEntity.getAuditedBy()))
        );
        return CoaItemAuditEntity.toDtoList(coaItemAuditEntities);
    }

    @Transactional
    public List<CoaItemAuditDto.Response> auditForUpdate(List<CoaItemAuditDto.Update> coaItemAuditEntityUpdateDtos) {
        List<CoaItemAuditEntity> coaItemAuditEntities = new ArrayList<>();
        if (coaItemAuditEntityUpdateDtos == null || coaItemAuditEntityUpdateDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String username = getUsername(accessToken);
            String fullName = exchangeService.getNameFromUsername(username);
            for (CoaItemAuditDto.Update coaItemAuditEntityUpdateDto : coaItemAuditEntityUpdateDtos) {
                CoaItemAuditEntity coaItemAuditEntity = new CoaItemAuditEntity();
                coaItemAuditEntity.setCoaId(coaItemAuditEntityUpdateDto.coaId())
                        .setColName(coaItemAuditEntityUpdateDto.colName())
                        .setPrevValue(coaItemAuditEntityUpdateDto.prevValue())
                        .setNewValue(coaItemAuditEntityUpdateDto.newValue())
                        .setAction(AuditStatus.UPDATED)
                        .setAuditedBy(username)
                        .setAuditedBYFullName(fullName)
                        .setAuditTime(LocalDateTime.now());
                coaItemAuditEntities.add(coaItemAuditEntity);
            }
            coaItemAuditRepository.persist(coaItemAuditEntities);
            return CoaItemAuditEntity.toDtoList(coaItemAuditEntities);
        }
    }

    @Transactional
    public List<CoaItemAuditDto.Response> auditForDelete(List<CoaItemAuditDto.Delete> coaItemAuditEntityDeleteDtos) {
        List<CoaItemAuditEntity> coaItemAuditEntities = new ArrayList<>();
        if (coaItemAuditEntityDeleteDtos == null || coaItemAuditEntityDeleteDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String username = getUsername(accessToken);
            String fullName = exchangeService.getNameFromUsername(username);
            for (CoaItemAuditDto.Delete coaItemAuditEntityDeleteDto : coaItemAuditEntityDeleteDtos) {
                CoaItemAuditEntity coaItemAuditEntity = new CoaItemAuditEntity();
                coaItemAuditEntity.setCoaId(coaItemAuditEntityDeleteDto.coaId())
                        .setColName(coaItemAuditEntityDeleteDto.colName())
                        .setPrevValue(coaItemAuditEntityDeleteDto.prevValue())
                        .setNewValue(null)
                        .setAction(AuditStatus.DELETED)
                        .setAuditedBy(username)
                        .setAuditedBYFullName(fullName)
                        .setAuditTime(LocalDateTime.now());
                coaItemAuditEntities.add(coaItemAuditEntity);
            }
            coaItemAuditRepository.persist(coaItemAuditEntities);
            return CoaItemAuditEntity.toDtoList(coaItemAuditEntities);
        }
    }
}
