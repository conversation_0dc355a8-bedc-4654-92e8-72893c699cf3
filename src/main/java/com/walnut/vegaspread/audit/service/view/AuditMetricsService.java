package com.walnut.vegaspread.audit.service.view;

import com.walnut.vegaspread.audit.entity.view.AuditMetricsEntity;
import com.walnut.vegaspread.audit.model.view.CoaAuditSummary;
import com.walnut.vegaspread.audit.repository.view.AuditMetricsRepository;
import com.walnut.vegaspread.audit.service.common.ExchangeService;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import org.eclipse.microprofile.jwt.JsonWebToken;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.walnut.vegaspread.common.utils.Jwt.getUsername;

@ApplicationScoped
public class AuditMetricsService {

    private final JsonWebToken accessToken;
    private final AuditMetricsRepository auditMetricsRepository;
    private final ExchangeService exchangeService;

    public AuditMetricsService(JsonWebToken accessToken, AuditMetricsRepository auditMetricsRepository,
                               ExchangeService exchangeService) {
        this.accessToken = accessToken;
        this.auditMetricsRepository = auditMetricsRepository;
        this.exchangeService = exchangeService;
    }

    public List<AuditMetricsEntity> getMetricsForDoc(UUID docId) {
        List<AuditMetricsEntity> auditMetricsEntities = auditMetricsRepository.findByDocId(docId);

        if (auditMetricsEntities == null || auditMetricsEntities.isEmpty()) {
            return Collections.emptyList();
        }
        Map<String, String> usernameToNameMap = exchangeService.getNamesFromUsernames(
                auditMetricsEntities.stream().map(AuditMetricsEntity::getCreatedBy).distinct().toList()
        );
        auditMetricsEntities.forEach(auditMetricsEntity ->
                auditMetricsEntity.setCreatedByFullName(
                        usernameToNameMap.get(auditMetricsEntity.getCreatedBy())
                )
        );
        return auditMetricsEntities;
    }

    @Transactional
    public void addMetricsForDoc(List<CoaAuditSummary.CoaAuditSummaryRow> rows, UUID docId) {
        String username = getUsername(accessToken);
        List<AuditMetricsEntity> auditMetricsEntities = rows.stream().map(
                coaAuditSummaryRow ->
                        new AuditMetricsEntity().setDocId(docId)
                                .setCategory(coaAuditSummaryRow.cellsText().get(0))
                                .setCompleteness(Integer.valueOf(coaAuditSummaryRow.cellsText().get(1)))
                                .setAccuracy(Integer.valueOf(coaAuditSummaryRow.cellsText().get(2)))
                                .setCreatedBy(username)
                                .setCreatedTime(LocalDateTime.now())
        ).toList();
        auditMetricsRepository.persist(auditMetricsEntities);
    }
}
