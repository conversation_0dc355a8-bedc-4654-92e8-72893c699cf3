package com.walnut.vegaspread.audit.service.extraction;

import com.walnut.vegaspread.audit.entity.extraction.LayoutBlockAuditEntity;
import com.walnut.vegaspread.audit.model.common.HistoryDto;
import com.walnut.vegaspread.audit.repository.extraction.LayoutBlockAuditRepository;
import com.walnut.vegaspread.audit.service.common.ExchangeService;
import com.walnut.vegaspread.audit.service.view.SpreadAuditedBlockMappingService;
import com.walnut.vegaspread.common.model.audit.extraction.LayoutBlockAuditDto;
import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import io.quarkus.panache.common.Parameters;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import org.eclipse.microprofile.jwt.JsonWebToken;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.walnut.vegaspread.common.utils.Jwt.getUsername;

@ApplicationScoped
public class LayoutBlockAuditService {
    private static final Sort DEFAULT_SORT = Sort.by("auditTime", Sort.Direction.Descending);
    private final JsonWebToken accessToken;
    private final LayoutBlockAuditRepository layoutBlockAuditRepository;
    private final SpreadAuditedBlockMappingService spreadAuditedBlockMappingService;
    private final ExchangeService exchangeService;

    public LayoutBlockAuditService(JsonWebToken accessToken, LayoutBlockAuditRepository layoutBlockAuditRepository,
                                   SpreadAuditedBlockMappingService spreadAuditedBlockMappingService,
                                   ExchangeService exchangeService) {
        this.accessToken = accessToken;
        this.layoutBlockAuditRepository = layoutBlockAuditRepository;
        this.spreadAuditedBlockMappingService = spreadAuditedBlockMappingService;
        this.exchangeService = exchangeService;
    }

    @Transactional
    public List<LayoutBlockAuditDto.Response> auditForCreate(
            List<LayoutBlockAuditDto.Create> layoutBlockAuditCreateDtos, String username) {
        List<LayoutBlockAuditEntity> layoutBlockAuditEntities = new ArrayList<>();
        if (layoutBlockAuditCreateDtos == null || layoutBlockAuditCreateDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String fullName = username;
            if (username == null) {
                username = getUsername(accessToken);
                fullName = exchangeService.getNameFromUsername(username);
            }
            for (LayoutBlockAuditDto.Create layoutBlockAuditCreateDto : layoutBlockAuditCreateDtos) {
                LayoutBlockAuditEntity layoutBlockAuditEntity = new LayoutBlockAuditEntity();
                layoutBlockAuditEntity.setBlockId(layoutBlockAuditCreateDto.blockId());
                layoutBlockAuditEntity.setColName(layoutBlockAuditCreateDto.colName());
                layoutBlockAuditEntity.setPrevValue(null);
                layoutBlockAuditEntity.setNewValue(layoutBlockAuditCreateDto.newValue());
                layoutBlockAuditEntity.setAuditTime(LocalDateTime.now());
                layoutBlockAuditEntity.setAuditedBy(username);
                layoutBlockAuditEntity.setAuditedBYFullName(fullName);
                layoutBlockAuditEntity.setAction(AuditStatus.CREATED);
                layoutBlockAuditEntities.add(layoutBlockAuditEntity);
            }
            layoutBlockAuditRepository.persist(layoutBlockAuditEntities);
            return LayoutBlockAuditEntity.toDtoList(layoutBlockAuditEntities);
        }
    }

    @Transactional
    public List<LayoutBlockAuditDto.Response> auditForUpdate(
            List<LayoutBlockAuditDto.Update> layoutBlockAuditUpdateDtos,
            String username) {
        List<LayoutBlockAuditEntity> layoutBlockAuditEntities = new ArrayList<>();
        if (layoutBlockAuditUpdateDtos == null || layoutBlockAuditUpdateDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String fullName = username;
            if (username == null) {
                username = getUsername(accessToken);
                fullName = exchangeService.getNameFromUsername(username);
            }
            for (LayoutBlockAuditDto.Update layoutBlockAuditUpdateDto : layoutBlockAuditUpdateDtos) {
                LayoutBlockAuditEntity layoutBlockAuditEntity = new LayoutBlockAuditEntity();
                layoutBlockAuditEntity.setBlockId(layoutBlockAuditUpdateDto.blockId());
                layoutBlockAuditEntity.setColName(layoutBlockAuditUpdateDto.colName());
                layoutBlockAuditEntity.setPrevValue(layoutBlockAuditUpdateDto.prevValue());
                layoutBlockAuditEntity.setNewValue(layoutBlockAuditUpdateDto.newValue());
                layoutBlockAuditEntity.setAuditTime(LocalDateTime.now());
                layoutBlockAuditEntity.setAuditedBy(username);
                layoutBlockAuditEntity.setAuditedBYFullName(fullName);
                layoutBlockAuditEntity.setAction(AuditStatus.UPDATED);
                layoutBlockAuditEntities.add(layoutBlockAuditEntity);
            }
            layoutBlockAuditRepository.persist(layoutBlockAuditEntities);
            return LayoutBlockAuditEntity.toDtoList(layoutBlockAuditEntities);
        }
    }

    @Transactional
    public List<LayoutBlockAuditDto.Response> auditForDelete(
            List<LayoutBlockAuditDto.Delete> layoutBlockAuditDeleteDtos, String username) {
        List<LayoutBlockAuditEntity> layoutBlockAuditEntities = new ArrayList<>();
        if (layoutBlockAuditDeleteDtos == null || layoutBlockAuditDeleteDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String fullName = username;
            if (username == null) {
                username = getUsername(accessToken);
                fullName = exchangeService.getNameFromUsername(username);
            }
            for (LayoutBlockAuditDto.Delete layoutBlockAuditDeleteDto : layoutBlockAuditDeleteDtos) {
                LayoutBlockAuditEntity layoutBlockAuditEntity = new LayoutBlockAuditEntity();
                layoutBlockAuditEntity.setBlockId(layoutBlockAuditDeleteDto.blockId());
                layoutBlockAuditEntity.setColName(layoutBlockAuditDeleteDto.colName());
                layoutBlockAuditEntity.setPrevValue(layoutBlockAuditDeleteDto.prevValue());
                layoutBlockAuditEntity.setNewValue(null);
                layoutBlockAuditEntity.setAuditTime(LocalDateTime.now());
                layoutBlockAuditEntity.setAuditedBy(username);
                layoutBlockAuditEntity.setAuditedBYFullName(fullName);
                layoutBlockAuditEntity.setAction(AuditStatus.DELETED);
                layoutBlockAuditEntities.add(layoutBlockAuditEntity);
            }
            layoutBlockAuditRepository.persist(layoutBlockAuditEntities);
            return LayoutBlockAuditEntity.toDtoList(layoutBlockAuditEntities);
        }
    }

    @Transactional
    public LayoutBlockAuditDto.ListResponse filterAuditsForBlocks(LayoutBlockAuditDto.ListForBlocks listBlocksDto) {

        StringBuilder baseQuery = new StringBuilder("blockId IN :blockId AND colName = :colName");
        Parameters baseParams = Parameters.with("blockId", listBlocksDto.blockIds());
        baseParams.and("colName", listBlocksDto.colName());

        StringBuilder filterQuery = new StringBuilder(baseQuery);
        Parameters params = new Parameters();
        baseParams.map().forEach(params::and);

        if (listBlocksDto.filterUser() != null) {
            filterQuery.append(" AND auditedBy = :auditedBy");
            params.and("auditedBy", listBlocksDto.filterUser());
        }
        if (listBlocksDto.filterDate() != null) {
            filterQuery.append(" AND DATE(auditTime) = :auditDate");
            params.and("auditDate", listBlocksDto.filterDate());
        }

        List<LayoutBlockAuditEntity> layoutBlockAuditEntities = layoutBlockAuditRepository.find(
                        filterQuery.toString(), DEFAULT_SORT, params)
                .page(listBlocksDto.pageNumber() - 1, listBlocksDto.pageSize())
                .list();

        Map<String, String> nameMappings = exchangeService.getNamesFromUsernames(layoutBlockAuditEntities.stream()
                .map(LayoutBlockAuditEntity::getAuditedBy)
                .distinct()
                .toList());
        layoutBlockAuditEntities.forEach(layoutBlockAuditEntity ->
                layoutBlockAuditEntity.setAuditedBYFullName(
                        nameMappings.get(layoutBlockAuditEntity.getAuditedBy()))
        );

        long totalAudits = layoutBlockAuditRepository.count(filterQuery.toString(), params);
        int totalPages = (int) Math.ceil((double) totalAudits / listBlocksDto.pageSize());

        List<String> allUsers = layoutBlockAuditRepository.distinctUsersForFilteredFieldInBlocks(
                new StringBuilder(baseQuery),
                baseParams);
        Map<String, String> allUsersMap = exchangeService.getNamesFromUsernames(allUsers);

        List<LocalDate> allDates = layoutBlockAuditRepository.distinctAuditDatesForFilteredFieldInBlocks(
                baseQuery, baseParams);

        return new LayoutBlockAuditDto.ListResponse(listBlocksDto.pageNumber(), listBlocksDto.pageSize(),
                totalPages, LayoutBlockAuditEntity.toDtoList(layoutBlockAuditEntities), allUsersMap,
                allDates);
    }

    public HistoryDto.Response getHistory(HistoryDto.Request auditListDto, String colName) {
        List<Integer> blockIds = spreadAuditedBlockMappingService.getAuditedBlockIdsForSpread(auditListDto.spreadId());

        LayoutBlockAuditDto.ListResponse filteredAudits = filterAuditsForBlocks(
                new LayoutBlockAuditDto.ListForBlocks(blockIds, colName, auditListDto.pageNumber(),
                        auditListDto.pageSize(), auditListDto.auditor(), auditListDto.auditDate()));

        return toHistory(filteredAudits);
    }

    private HistoryDto.Response toHistory(LayoutBlockAuditDto.ListResponse filteredAudits) {
        return new HistoryDto.Response(
                filteredAudits.pageNumber(),
                filteredAudits.pageSize(),
                filteredAudits.totalPages(),
                filteredAudits.layoutBlockAudits().stream().map(filteredAudit -> new HistoryDto.Item(
                        filteredAudit.blockId(),
                        null,
                        filteredAudit.prevValue() == null ? "NA" : filteredAudit.prevValue(),
                        filteredAudit.newValue() == null ? "NA" : filteredAudit.newValue(),
                        filteredAudit.action(),
                        filteredAudit.auditTime(),
                        filteredAudit.auditedBy(),
                        filteredAudit.auditedBYFullName() == null ? "NA" : filteredAudit.auditedBYFullName())).toList(),
                filteredAudits.allUsers(),
                filteredAudits.allDates()
        );
    }

    public List<LayoutBlockAuditDto.Response> getColAuditsForBlocks(List<Integer> blockIds, String colName) {
        if (blockIds == null || blockIds.isEmpty()) {
            return Collections.emptyList();
        }
        return LayoutBlockAuditEntity.toDtoList(
                layoutBlockAuditRepository.findByBlockIdsAndColName(blockIds, colName));
    }
}
