package com.walnut.vegaspread.audit.service.extraction;

import com.walnut.vegaspread.audit.entity.extraction.ExtractedTableHeaderAuditEntity;
import com.walnut.vegaspread.audit.repository.extraction.ExtractedTableHeaderAuditRepository;
import com.walnut.vegaspread.audit.service.common.ExchangeService;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedTableHeaderAuditDto;
import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import org.eclipse.microprofile.jwt.JsonWebToken;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.walnut.vegaspread.common.utils.Jwt.getUsername;

@ApplicationScoped
public class ExtractedTableHeaderAuditService {

    private final JsonWebToken accessToken;
    private final ExtractedTableHeaderAuditRepository extractedTableHeaderAuditRepository;
    private final ExchangeService exchangeService;

    public ExtractedTableHeaderAuditService(JsonWebToken accessToken,
                                            ExtractedTableHeaderAuditRepository extractedTableHeaderAuditRepository,
                                            ExchangeService exchangeService) {
        this.accessToken = accessToken;
        this.extractedTableHeaderAuditRepository = extractedTableHeaderAuditRepository;
        this.exchangeService = exchangeService;
    }

    @Transactional
    public List<ExtractedTableHeaderAuditDto.Response> auditForCreate(
            List<ExtractedTableHeaderAuditDto.Create> extractedTableHeaderCreateAuditDtos, String username) {
        List<ExtractedTableHeaderAuditEntity> extractedTableHeaderAuditEntities = new ArrayList<>();
        if (extractedTableHeaderCreateAuditDtos == null || extractedTableHeaderCreateAuditDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String fullName = username;
            if (username == null) {
                username = getUsername(accessToken);
                fullName = exchangeService.getNameFromUsername(username);
            }
            for (ExtractedTableHeaderAuditDto.Create extractedTableHeaderCreateAuditDto :
                    extractedTableHeaderCreateAuditDtos) {
                ExtractedTableHeaderAuditEntity extractedTableHeaderAuditEntity = new ExtractedTableHeaderAuditEntity();
                extractedTableHeaderAuditEntity.setTableId(extractedTableHeaderCreateAuditDto.tableId())
                        .setHeaderId(extractedTableHeaderCreateAuditDto.headerId())
                        .setColName(extractedTableHeaderCreateAuditDto.colName())
                        .setPrevValue(null)
                        .setNewValue(extractedTableHeaderCreateAuditDto.newValue())
                        .setAction(AuditStatus.CREATED)
                        .setAuditedBy(username)
                        .setAuditedBYFullName(fullName)
                        .setAuditTime(LocalDateTime.now());
                extractedTableHeaderAuditEntities.add(extractedTableHeaderAuditEntity);
            }
            extractedTableHeaderAuditRepository.persist(extractedTableHeaderAuditEntities);
            return ExtractedTableHeaderAuditEntity.toDtoList(extractedTableHeaderAuditEntities);
        }
    }

    @Transactional
    public List<ExtractedTableHeaderAuditDto.Response> auditForUpdate(
            List<ExtractedTableHeaderAuditDto.Update> extractedTableHeaderAuditDtos, String username) {
        List<ExtractedTableHeaderAuditEntity> extractedTableHeaderAuditEntities = new ArrayList<>();
        if (extractedTableHeaderAuditDtos == null || extractedTableHeaderAuditDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String fullName = username;
            if (username == null) {
                username = getUsername(accessToken);
                fullName = exchangeService.getNameFromUsername(username);
            }
            for (ExtractedTableHeaderAuditDto.Update extractedTableHeaderUpdateAuditDto :
                    extractedTableHeaderAuditDtos) {
                ExtractedTableHeaderAuditEntity extractedTableHeaderAuditEntity = new ExtractedTableHeaderAuditEntity();
                extractedTableHeaderAuditEntity.setTableId(extractedTableHeaderUpdateAuditDto.tableId())
                        .setHeaderId(extractedTableHeaderUpdateAuditDto.headerId())
                        .setColName(extractedTableHeaderUpdateAuditDto.colName())
                        .setPrevValue(extractedTableHeaderUpdateAuditDto.prevValue())
                        .setNewValue(extractedTableHeaderUpdateAuditDto.newValue())
                        .setAction(AuditStatus.UPDATED)
                        .setAuditedBy(username)
                        .setAuditedBYFullName(fullName)
                        .setAuditTime(LocalDateTime.now());
                extractedTableHeaderAuditEntities.add(extractedTableHeaderAuditEntity);
            }
            extractedTableHeaderAuditRepository.persist(extractedTableHeaderAuditEntities);
            return ExtractedTableHeaderAuditEntity.toDtoList(extractedTableHeaderAuditEntities);
        }
    }

    @Transactional
    public List<ExtractedTableHeaderAuditDto.Response> auditForDelete(
            List<ExtractedTableHeaderAuditDto.Delete> extractedTableHeaderDeleteAuditDtos, String username) {
        List<ExtractedTableHeaderAuditEntity> extractedTableHeaderAuditEntities = new ArrayList<>();
        if (extractedTableHeaderDeleteAuditDtos == null || extractedTableHeaderDeleteAuditDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String fullName = username;
            if (username == null) {
                username = getUsername(accessToken);
                fullName = exchangeService.getNameFromUsername(username);
            }
            for (ExtractedTableHeaderAuditDto.Delete extractedTableHeaderDeleteAuditDto :
                    extractedTableHeaderDeleteAuditDtos) {
                ExtractedTableHeaderAuditEntity extractedTableHeaderAuditEntity = new ExtractedTableHeaderAuditEntity();
                extractedTableHeaderAuditEntity.setTableId(extractedTableHeaderDeleteAuditDto.tableId())
                        .setHeaderId(extractedTableHeaderDeleteAuditDto.headerId())
                        .setColName(extractedTableHeaderDeleteAuditDto.colName())
                        .setPrevValue(extractedTableHeaderDeleteAuditDto.prevValue())
                        .setNewValue(null)
                        .setAction(AuditStatus.DELETED)
                        .setAuditedBy(username)
                        .setAuditedBYFullName(fullName)
                        .setAuditTime(LocalDateTime.now());
                extractedTableHeaderAuditEntities.add(extractedTableHeaderAuditEntity);
            }
            extractedTableHeaderAuditRepository.persist(extractedTableHeaderAuditEntities);
            return ExtractedTableHeaderAuditEntity.toDtoList(extractedTableHeaderAuditEntities);
        }
    }

    public List<ExtractedTableHeaderAuditDto.Response> getColAuditsForTables(List<Integer> tableIds, String colName) {
        if (tableIds == null || tableIds.isEmpty()) {
            return Collections.emptyList();
        }
        return ExtractedTableHeaderAuditEntity.toDtoList(
                extractedTableHeaderAuditRepository.findByTableIdsAndColName(tableIds, colName));
    }
}
