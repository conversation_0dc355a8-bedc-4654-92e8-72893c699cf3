package com.walnut.vegaspread.audit.service.extraction;

import com.walnut.vegaspread.audit.entity.extraction.ExtractedTableRowAuditEntity;
import com.walnut.vegaspread.audit.model.common.HistoryDto;
import com.walnut.vegaspread.audit.repository.extraction.ExtractedTableRowAuditRepository;
import com.walnut.vegaspread.audit.service.common.ExchangeService;
import com.walnut.vegaspread.audit.service.view.SpreadAuditedBlockMappingService;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedTableRowAuditDto;
import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import io.quarkus.panache.common.Parameters;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import org.eclipse.microprofile.jwt.JsonWebToken;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.walnut.vegaspread.common.utils.Jwt.getUsername;

@ApplicationScoped
public class ExtractedTableRowAuditService {

    private static final Sort DEFAULT_SORT = Sort.by("auditTime", Sort.Direction.Descending);
    private final JsonWebToken accessToken;
    private final ExtractedTableRowAuditRepository extractedTableRowAuditRepository;
    private final SpreadAuditedBlockMappingService spreadAuditedBlockMappingService;
    private final ExchangeService exchangeService;

    public ExtractedTableRowAuditService(JsonWebToken accessToken,
                                         ExtractedTableRowAuditRepository extractedTableRowAuditRepository,
                                         SpreadAuditedBlockMappingService spreadAuditedBlockMappingService,
                                         ExchangeService exchangeService) {
        this.accessToken = accessToken;
        this.extractedTableRowAuditRepository = extractedTableRowAuditRepository;
        this.spreadAuditedBlockMappingService = spreadAuditedBlockMappingService;
        this.exchangeService = exchangeService;
    }

    @Transactional
    public List<ExtractedTableRowAuditDto.Response> auditForCreate(
            List<ExtractedTableRowAuditDto.Create> extractedTableRowCreateAuditDtos) {
        List<ExtractedTableRowAuditEntity> extractedTableRowAuditEntities = new ArrayList<>();
        if (extractedTableRowCreateAuditDtos == null || extractedTableRowCreateAuditDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String username = getUsername(accessToken);
            String fullName = exchangeService.getNameFromUsername(username);
            for (ExtractedTableRowAuditDto.Create extractedTableRowCreateAuditDto : extractedTableRowCreateAuditDtos) {
                ExtractedTableRowAuditEntity extractedTableRowAuditEntity = new ExtractedTableRowAuditEntity();
                extractedTableRowAuditEntity.setTableId(extractedTableRowCreateAuditDto.tableId())
                        .setRowId(extractedTableRowCreateAuditDto.rowId().shortValue())
                        .setColName(extractedTableRowCreateAuditDto.colName())
                        .setPrevValue(null)
                        .setNewValue(extractedTableRowCreateAuditDto.newValue())
                        .setAction(AuditStatus.CREATED)
                        .setAuditedBy(username)
                        .setAuditedBYFullName(fullName)
                        .setAuditTime(LocalDateTime.now());
                extractedTableRowAuditEntities.add(extractedTableRowAuditEntity);
            }
            extractedTableRowAuditRepository.persist(extractedTableRowAuditEntities);
            return ExtractedTableRowAuditEntity.toDtoList(extractedTableRowAuditEntities);
        }
    }

    @Transactional
    public List<ExtractedTableRowAuditDto.Response> auditForUpdate(
            List<ExtractedTableRowAuditDto.Update> extractedTableRowUpdateAuditDtos, String username) {
        List<ExtractedTableRowAuditEntity> extractedTableRowAuditEntities = new ArrayList<>();
        if (extractedTableRowUpdateAuditDtos == null || extractedTableRowUpdateAuditDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String fullName = username;
            if (username == null) {
                username = getUsername(accessToken);
                fullName = exchangeService.getNameFromUsername(username);
            }
            for (ExtractedTableRowAuditDto.Update extractedTableRowUpdateAuditDto : extractedTableRowUpdateAuditDtos) {
                ExtractedTableRowAuditEntity extractedTableRowAuditEntity = new ExtractedTableRowAuditEntity();
                extractedTableRowAuditEntity.setTableId(extractedTableRowUpdateAuditDto.tableId())
                        .setRowId(extractedTableRowUpdateAuditDto.rowId().shortValue())
                        .setColName(extractedTableRowUpdateAuditDto.colName())
                        .setPrevValue(extractedTableRowUpdateAuditDto.prevValue())
                        .setNewValue(extractedTableRowUpdateAuditDto.newValue())
                        .setAction(AuditStatus.UPDATED)
                        .setAuditedBy(username)
                        .setAuditedBYFullName(fullName)
                        .setAuditTime(LocalDateTime.now());
                extractedTableRowAuditEntities.add(extractedTableRowAuditEntity);
            }
            extractedTableRowAuditRepository.persist(extractedTableRowAuditEntities);
            return ExtractedTableRowAuditEntity.toDtoList(extractedTableRowAuditEntities);
        }
    }

    @Transactional
    public List<ExtractedTableRowAuditDto.Response> auditForDelete(
            List<ExtractedTableRowAuditDto.Delete> extractedTableRowDeleteAuditDtos) {
        List<ExtractedTableRowAuditEntity> extractedTableRowAuditEntities = new ArrayList<>();
        if (extractedTableRowDeleteAuditDtos == null || extractedTableRowDeleteAuditDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String username = getUsername(accessToken);
            String fullName = exchangeService.getNameFromUsername(username);
            for (ExtractedTableRowAuditDto.Delete extractedTableRowDeleteAuditDto : extractedTableRowDeleteAuditDtos) {
                ExtractedTableRowAuditEntity extractedTableRowAuditEntity = new ExtractedTableRowAuditEntity();
                extractedTableRowAuditEntity.setTableId(extractedTableRowDeleteAuditDto.tableId())
                        .setRowId(extractedTableRowDeleteAuditDto.rowId().shortValue())
                        .setColName(extractedTableRowDeleteAuditDto.colName())
                        .setPrevValue(extractedTableRowDeleteAuditDto.prevValue())
                        .setNewValue(null)
                        .setAction(AuditStatus.DELETED)
                        .setAuditedBy(username)
                        .setAuditedBYFullName(fullName)
                        .setAuditTime(LocalDateTime.now());
                extractedTableRowAuditEntities.add(extractedTableRowAuditEntity);
            }
            extractedTableRowAuditRepository.persist(extractedTableRowAuditEntities);
            return ExtractedTableRowAuditEntity.toDtoList(extractedTableRowAuditEntities);
        }
    }

    @Transactional
    public ExtractedTableRowAuditDto.ListResponse filterAuditsForTables(
            ExtractedTableRowAuditDto.ListForTables listTablesDto) {

        StringBuilder baseQuery = new StringBuilder("tableId IN :tableId AND colName = :colName");
        Parameters baseParams = Parameters.with("tableId", listTablesDto.tableIds());
        baseParams.and("colName", listTablesDto.colName());

        StringBuilder filterQuery = new StringBuilder(baseQuery);
        Parameters params = new Parameters();
        baseParams.map().forEach(params::and);

        if (listTablesDto.filterUser() != null) {
            filterQuery.append(" AND auditedBy = :auditedBy");
            params.and("auditedBy", listTablesDto.filterUser());
        }
        if (listTablesDto.filterDate() != null) {
            filterQuery.append(" AND DATE(auditTime) = :auditDate");
            params.and("auditDate", listTablesDto.filterDate());
        }

        //Get filtered,sorted and paginated records
        List<ExtractedTableRowAuditEntity> extractedTableRowAuditEntities = extractedTableRowAuditRepository.find(
                        filterQuery.toString(), DEFAULT_SORT, params)
                .page(listTablesDto.pageNumber() - 1, listTablesDto.pageSize())
                .list();
        //Map full name for auditors.
        Map<String, String> nameMappings = exchangeService.getNamesFromUsernames(extractedTableRowAuditEntities.stream()
                .map(ExtractedTableRowAuditEntity::getAuditedBy)
                .distinct()
                .toList());
        extractedTableRowAuditEntities.forEach(extractedTableRowAuditEntity ->
                extractedTableRowAuditEntity.setAuditedBYFullName(
                        nameMappings.get(extractedTableRowAuditEntity.getAuditedBy()))
        );

        long totalAudits = extractedTableRowAuditRepository.count(filterQuery.toString(), params);
        int totalPages = (int) Math.ceil((double) totalAudits / listTablesDto.pageSize());

        List<String> allUsers = extractedTableRowAuditRepository.distinctUsersForFilteredFieldInTables(
                new StringBuilder(baseQuery), baseParams);
        Map<String, String> allUsersMap = exchangeService.getNamesFromUsernames(allUsers);

        List<LocalDate> allDates = extractedTableRowAuditRepository.distinctAuditDatesForFilteredFieldInTables(
                baseQuery, baseParams);

        return new ExtractedTableRowAuditDto.ListResponse(listTablesDto.pageNumber(), listTablesDto.pageSize(),
                totalPages, ExtractedTableRowAuditEntity.toDtoList(extractedTableRowAuditEntities), allUsersMap,
                allDates);
    }

    public HistoryDto.Response getHistory(HistoryDto.Request auditListDto, String colName) {
        List<Integer> blockIds = spreadAuditedBlockMappingService.getAuditedBlockIdsForSpread(auditListDto.spreadId());

        ExtractedTableRowAuditDto.ListResponse filteredAudits = filterAuditsForTables(
                new ExtractedTableRowAuditDto.ListForTables(blockIds, colName, auditListDto.pageNumber(),
                        auditListDto.pageSize(), auditListDto.auditor(), auditListDto.auditDate()));

        return toHistory(filteredAudits);
    }

    public List<ExtractedTableRowAuditDto.Response> getColAuditsForTables(List<Integer> tableIds, String colName) {
        if (tableIds == null || tableIds.isEmpty()) {
            return Collections.emptyList();
        }
        return ExtractedTableRowAuditEntity.toDtoList(
                extractedTableRowAuditRepository.findByTableIdsAndColName(tableIds, colName));
    }

    private HistoryDto.Response toHistory(ExtractedTableRowAuditDto.ListResponse filteredAudits) {
        return new HistoryDto.Response(
                filteredAudits.pageNumber(),
                filteredAudits.pageSize(),
                filteredAudits.totalPages(),
                filteredAudits.extractedTableRowAudits().stream().map(filteredAudit -> new HistoryDto.Item(
                        filteredAudit.tableId(),
                        filteredAudit.rowId(),
                        filteredAudit.prevValue() == null ? "NA" : filteredAudit.prevValue(),
                        filteredAudit.newValue() == null ? "NA" : filteredAudit.newValue(),
                        filteredAudit.action(),
                        filteredAudit.auditTime(),
                        filteredAudit.auditedBy(),
                        filteredAudit.auditedBYFullName() == null ? "NA" : filteredAudit.auditedBYFullName())).toList(),
                filteredAudits.allUsers(),
                filteredAudits.allDates()
        );
    }
}
