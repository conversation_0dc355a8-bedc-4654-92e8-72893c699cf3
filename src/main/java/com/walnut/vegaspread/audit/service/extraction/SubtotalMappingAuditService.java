package com.walnut.vegaspread.audit.service.extraction;

import com.walnut.vegaspread.audit.entity.extraction.SubtotalMappingAuditEntity;
import com.walnut.vegaspread.audit.repository.extraction.SubtotalMappingAuditRepository;
import com.walnut.vegaspread.audit.service.common.ExchangeService;
import com.walnut.vegaspread.common.model.audit.extraction.SubtotalMappingAuditDto;
import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import org.eclipse.microprofile.jwt.JsonWebToken;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.walnut.vegaspread.common.utils.Jwt.getUsername;

@ApplicationScoped
public class SubtotalMappingAuditService {

    private final JsonWebToken accessToken;
    private final SubtotalMappingAuditRepository subtotalMappingAuditRepository;
    private final ExchangeService exchangeService;

    public SubtotalMappingAuditService(JsonWebToken accessToken,
                                       SubtotalMappingAuditRepository subtotalMappingAuditRepository,
                                       ExchangeService exchangeService) {
        this.accessToken = accessToken;
        this.subtotalMappingAuditRepository = subtotalMappingAuditRepository;
        this.exchangeService = exchangeService;
    }

    @Transactional
    public List<SubtotalMappingAuditEntity> auditForCreate(
            List<SubtotalMappingAuditDto.Create> subtotalMappingCreateAuditDtos) {
        List<SubtotalMappingAuditEntity> subtotalMappingAuditEntities = new ArrayList<>();
        if (subtotalMappingCreateAuditDtos == null || subtotalMappingCreateAuditDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String username = getUsername(accessToken);
            String fullName = exchangeService.getNameFromUsername(username);

            for (SubtotalMappingAuditDto.Create subtotalMappingAuditCreateAuditDto :
                    subtotalMappingCreateAuditDtos) {
                SubtotalMappingAuditEntity subtotalMappingAuditEntity = new SubtotalMappingAuditEntity();
                subtotalMappingAuditEntity.setTableId(subtotalMappingAuditCreateAuditDto.tableId())
                        .setRowId(subtotalMappingAuditCreateAuditDto.rowId().shortValue())
                        .setColName(subtotalMappingAuditCreateAuditDto.colName())
                        .setPrevValue("0")
                        .setNewValue(subtotalMappingAuditCreateAuditDto.newValue())
                        .setAction(AuditStatus.CREATED)
                        .setAuditedBy(username)
                        .setAuditedBYFullName(fullName)
                        .setAuditTime(LocalDateTime.now());
                subtotalMappingAuditEntities.add(subtotalMappingAuditEntity);
            }
            subtotalMappingAuditRepository.persist(subtotalMappingAuditEntities);
            return subtotalMappingAuditEntities;
        }
    }

    @Transactional
    public List<SubtotalMappingAuditEntity> auditForUpdate(
            List<SubtotalMappingAuditDto.Update> subtotalMappingUpdateAuditDtos) {
        List<SubtotalMappingAuditEntity> subtotalMappingAuditEntities = new ArrayList<>();
        if (subtotalMappingUpdateAuditDtos == null || subtotalMappingUpdateAuditDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String username = getUsername(accessToken);
            String fullName = exchangeService.getNameFromUsername(username);
            for (SubtotalMappingAuditDto.Update subtotalMappingUpdateAuditDto :
                    subtotalMappingUpdateAuditDtos) {
                SubtotalMappingAuditEntity subtotalMappingAuditEntity = new SubtotalMappingAuditEntity();
                subtotalMappingAuditEntity.setTableId(subtotalMappingUpdateAuditDto.tableId())
                        .setRowId(subtotalMappingUpdateAuditDto.rowId().shortValue())
                        .setColName(subtotalMappingUpdateAuditDto.colName())
                        .setPrevValue(subtotalMappingUpdateAuditDto.prevValue())
                        .setNewValue(subtotalMappingUpdateAuditDto.newValue())
                        .setAction(AuditStatus.UPDATED)
                        .setAuditedBy(username)
                        .setAuditedBYFullName(fullName)
                        .setAuditTime(LocalDateTime.now());
                subtotalMappingAuditEntities.add(subtotalMappingAuditEntity);
            }
            subtotalMappingAuditRepository.persist(subtotalMappingAuditEntities);
            return subtotalMappingAuditEntities;
        }
    }

    @Transactional
    public List<SubtotalMappingAuditEntity> auditForDelete(
            List<SubtotalMappingAuditDto.Delete> subtotalMappingDeleteAuditDtos) {
        List<SubtotalMappingAuditEntity> subtotalMappingAuditEntities = new ArrayList<>();
        if (subtotalMappingDeleteAuditDtos == null || subtotalMappingDeleteAuditDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String username = getUsername(accessToken);
            String fullName = exchangeService.getNameFromUsername(username);
            for (SubtotalMappingAuditDto.Delete subtotalMappingDeleteAuditDto :
                    subtotalMappingDeleteAuditDtos) {
                SubtotalMappingAuditEntity subtotalMappingAuditEntity = new SubtotalMappingAuditEntity();
                subtotalMappingAuditEntity.setTableId(subtotalMappingDeleteAuditDto.tableId())
                        .setRowId(subtotalMappingDeleteAuditDto.rowId().shortValue())
                        .setColName(subtotalMappingDeleteAuditDto.colName())
                        .setPrevValue(subtotalMappingDeleteAuditDto.prevValue())
                        .setNewValue("0")
                        .setAction(AuditStatus.DELETED)
                        .setAuditedBy(username)
                        .setAuditedBYFullName(fullName)
                        .setAuditTime(LocalDateTime.now());
                subtotalMappingAuditEntities.add(subtotalMappingAuditEntity);
            }
            subtotalMappingAuditRepository.persist(subtotalMappingAuditEntities);
            return subtotalMappingAuditEntities;
        }
    }
}
