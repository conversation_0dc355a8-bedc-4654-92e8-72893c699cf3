package com.walnut.vegaspread.audit.service.view;

import com.walnut.vegaspread.audit.entity.view.AuditMetricsEntity;
import com.walnut.vegaspread.audit.model.view.CoaAuditSummary;
import com.walnut.vegaspread.audit.model.view.CoaAuditSummaryAuditType;
import com.walnut.vegaspread.audit.model.view.CoaAuditSummaryDto;
import com.walnut.vegaspread.audit.service.common.ExchangeService;
import com.walnut.vegaspread.audit.service.extraction.ExtractedRowCoaDataAuditService;
import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedRowCoaDataAuditDto;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.core.Response;
import org.apache.commons.lang3.tuple.ImmutablePair;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@ApplicationScoped
public class CoaAuditSummaryService {

    public static final String AI_USERNAME_FOR_AUDITS = "developer";
    private final AuditMetricsService auditMetricsService;
    private final ExchangeService exchangeService;
    private final ExtractedRowCoaDataAuditService extractedRowCoaDataAuditService;

    public CoaAuditSummaryService(AuditMetricsService auditMetricsService,
                                  ExchangeService exchangeService,
                                  ExtractedRowCoaDataAuditService extractedRowCoaDataAuditService
    ) {
        this.auditMetricsService = auditMetricsService;
        this.exchangeService = exchangeService;
        this.extractedRowCoaDataAuditService = extractedRowCoaDataAuditService;
    }

    private List<CoaAuditSummary.CoaAuditSummaryHeader> getSummaryHeaders() {
        return List.of(
                new CoaAuditSummary.CoaAuditSummaryHeader(0, "COA"),
                new CoaAuditSummary.CoaAuditSummaryHeader(1, "Completeness"),
                new CoaAuditSummary.CoaAuditSummaryHeader(2, "Accuracy")
        );
    }

    /**
     * Returns map of category from the view and coa title for UI.
     */
    private Map<String, String> getCategories() {
        return Map.of("Current Assets", "Current Assets",
                "Non Current Assets", "Non Current Assets",
                "Current Liabilities", "Current Liabilities",
                "Non Current Liabilities", "Non Current Liabilities",
                "Net Worth", "Net Worth",
                "Income Statement", "Income Statement"
        );
    }

    public CoaAuditSummary.CoaAuditSummaryOutput getAccuracy(
            UUID docId) throws NoSuchElementException, IllegalStateException {
        List<AuditMetricsEntity> auditMetricsEntities = auditMetricsService.getMetricsForDoc(docId);

        List<CoaAuditSummary.CoaAuditSummaryRow> rows = new ArrayList<>();
        Map<String, String> categories = getCategories();
        Integer rowId = 0;

        if (!auditMetricsEntities.isEmpty()) {
            for (Map.Entry<String, String> category : categories.entrySet()) {
                String categoryKey = category.getKey();
                Optional<AuditMetricsEntity> optAuditMetricsForCurrentCategory = auditMetricsEntities.stream()
                        .filter(auditMetricsEntity -> auditMetricsEntity.getCategory().equals(categoryKey))
                        .findFirst();
                if (optAuditMetricsForCurrentCategory.isEmpty()) {
                    ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR,
                            "Missing audit metric for category " + categoryKey + " for document " + docId);
                } else {
                    AuditMetricsEntity auditMetricsForCurrentCategory = optAuditMetricsForCurrentCategory.get();
                    rows.add(new CoaAuditSummary.CoaAuditSummaryRow(rowId,
                            List.of(category.getValue(),
                                    String.valueOf(auditMetricsForCurrentCategory.getCompleteness()),
                                    String.valueOf(auditMetricsForCurrentCategory.getAccuracy()))));
                }
                rowId++;
            }
        } else {
            return calculateAccuracy(docId);
        }
        List<CoaAuditSummary.CoaAuditSummaryHeader> headers = getSummaryHeaders();
        return new CoaAuditSummary.CoaAuditSummaryOutput(headers, rows);
    }

    public CoaAuditSummary.CoaAuditSummaryOutput calculateAccuracy(UUID docId) {

        List<CoaAuditSummary.CoaAuditSummaryRow> rows = new ArrayList<>();
        Map<String, String> categories = getCategories();
        Integer rowId = 0;

        for (Map.Entry<String, String> category : categories.entrySet()) {
            int completeness = 0;
            int accuracy = 0;
            String categoryKey = category.getKey();
            List<CoaAuditSummaryDto> coaAuditData = generateViewData(docId);
            Set<Long> prevCoaIdForUserRemoved = coaAuditData.stream()
                    .filter(rowAuditData ->
                            rowAuditData.category().startsWith(categoryKey)
                                    && rowAuditData.auditType()
                                    .equals(CoaAuditSummaryAuditType.USER_REMOVED.toString()))
                    .map(CoaAuditSummaryDto::prevCoaId)
                    .map(Long::parseLong).collect(Collectors.toSet());

            Set<Long> newCoaIdForUserUpdatedAndAICorrected =
                    coaAuditData.stream()
                            .filter(rowAuditData ->
                                    rowAuditData.category().startsWith(categoryKey)
                                            && (rowAuditData.auditType()
                                            .equals(CoaAuditSummaryAuditType.USER_UPDATED.toString())
                                            || rowAuditData.auditType()
                                            .equals(CoaAuditSummaryAuditType.AI_CORRECT.toString())))
                            .map(CoaAuditSummaryDto::newCoaId)
                            .map(Long::parseLong).collect(Collectors.toSet());

            Set<Long> newCoaIdForUserUpdatedAndUserAddedAndAICorrected =
                    coaAuditData.stream()
                            .filter(rowAuditData ->
                                    rowAuditData.category().startsWith(categoryKey)
                                            && (rowAuditData.auditType()
                                            .equals(CoaAuditSummaryAuditType.USER_UPDATED.toString())
                                            || rowAuditData.auditType()
                                            .equals(CoaAuditSummaryAuditType.USER_ADDED.toString())
                                            || rowAuditData.auditType()
                                            .equals(CoaAuditSummaryAuditType.AI_CORRECT.toString())))
                            .map(CoaAuditSummaryDto::newCoaId)
                            .map(Long::parseLong).collect(Collectors.toSet());

            Set<Long> unionResult = new HashSet<>(prevCoaIdForUserRemoved);
            unionResult.addAll(newCoaIdForUserUpdatedAndAICorrected);

            Set<Long> intersectionResult = new HashSet<>(unionResult);
            intersectionResult.retainAll(newCoaIdForUserUpdatedAndUserAddedAndAICorrected);

            if (!newCoaIdForUserUpdatedAndUserAddedAndAICorrected.isEmpty()) {
                completeness = Math.round(
                        (float) (intersectionResult.size() * 100) / newCoaIdForUserUpdatedAndUserAddedAndAICorrected.size()
                );
            }

            float aiCorrectCount = coaAuditData.stream()
                    .filter(rowAuditData ->
                            rowAuditData.category().startsWith(categoryKey)
                                    && rowAuditData.auditType()
                                    .equals(CoaAuditSummaryAuditType.AI_CORRECT.toString()))
                    .count();

            long totalCount = coaAuditData.stream()
                    .filter(rowAuditData ->
                            rowAuditData.category().startsWith(categoryKey)
                                    && (rowAuditData.auditType()
                                    .equals(CoaAuditSummaryAuditType.USER_UPDATED.toString())
                                    || rowAuditData.auditType()
                                    .equals(CoaAuditSummaryAuditType.USER_ADDED.toString())
                                    || rowAuditData.auditType()
                                    .equals(CoaAuditSummaryAuditType.AI_CORRECT.toString())))
                    .count();

            if (totalCount != 0) {
                accuracy = Math.round(aiCorrectCount * 100 / totalCount);
            }

            rows.add(new CoaAuditSummary.CoaAuditSummaryRow(rowId,
                    List.of(category.getValue(), String.valueOf(completeness), String.valueOf(accuracy))));
            rowId++;
        }
        auditMetricsService.addMetricsForDoc(rows, docId);

        List<CoaAuditSummary.CoaAuditSummaryHeader> headers = getSummaryHeaders();
        return new CoaAuditSummary.CoaAuditSummaryOutput(headers, rows);
    }

    private List<CoaAuditSummaryDto> generateViewData(UUID docId) {
        List<Integer> blockIds = exchangeService.getBlockIdsForDoc(docId);
        List<ExtractedRowCoaDataAuditDto.Response> rowCoaAudits = extractedRowCoaDataAuditService.getColAuditsForTables(
                blockIds, "coa_id");

        Map<ImmutablePair<Integer, Integer>, List<ExtractedRowCoaDataAuditDto.Response>> coaIdAuditsForRowsInDoc =
                rowCoaAudits.stream().collect(Collectors.groupingBy(
                        auditRow -> new ImmutablePair<>(auditRow.tableId(), auditRow.rowId()),
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted((a1, a2) -> a2.auditTime().compareTo(a1.auditTime()))
                                        .toList()
                        )));

        Map<ImmutablePair<Integer, Integer>, String> auditTypeMap = coaIdAuditsForRowsInDoc.entrySet()
                .parallelStream()
                .collect(
                        Collectors.toMap(
                                Map.Entry::getKey,
                                entry -> {
                                    List<ExtractedRowCoaDataAuditDto.Response> audits = entry.getValue();
                                    ExtractedRowCoaDataAuditDto.Response firstAudit = audits.get(audits.size() - 1);
                                    ExtractedRowCoaDataAuditDto.Response lastAudit = audits.get(0);
                                    if (firstAudit.auditedBy().equals(AI_USERNAME_FOR_AUDITS)) {
                                        if (lastAudit.auditedBy().equals(AI_USERNAME_FOR_AUDITS) || lastAudit.newValue()
                                                .equals(firstAudit.newValue())) {
                                            return CoaAuditSummaryAuditType.AI_CORRECT.toString();
                                        } else if (lastAudit.newValue().equals("1")) {
                                            return CoaAuditSummaryAuditType.USER_REMOVED.toString();
                                        } else {
                                            return CoaAuditSummaryAuditType.USER_UPDATED.toString();
                                        }
                                    } else {
                                        if (!lastAudit.newValue().equals("1")) {
                                            return CoaAuditSummaryAuditType.USER_ADDED.toString();
                                        }
                                        return "";
                                    }
                                }
                        )
                );

        List<Integer> coaIds = coaIdAuditsForRowsInDoc.values()
                .stream()
                .flatMap(auditRows -> auditRows.stream()
                        .flatMap(auditRow -> Stream.of(auditRow.prevValue(), auditRow.newValue())))
                .map(Integer::parseInt)
                .distinct()
                .toList();
        Map<Integer, String> coaIdCategoryMap = exchangeService.getCategoryForCoaIds(coaIds);

        List<CoaAuditSummaryDto> coaAuditData = new ArrayList<>();
        coaIdAuditsForRowsInDoc.forEach((row, auditData) -> {
            String auditType = auditTypeMap.get(row);
            if (auditType.isEmpty()) {
                return;
            }
            int coaIdForCategory = 1;
            ExtractedRowCoaDataAuditDto.Response lastAudit = auditData.get(0);
            ExtractedRowCoaDataAuditDto.Response firstAudit = auditData.get(auditData.size() - 1);

            if (!Objects.equals(lastAudit.prevValue(), "1") && Objects.equals(lastAudit.newValue(), "1")) {
                coaIdForCategory = Integer.parseInt(lastAudit.prevValue());
            } else {
                coaIdForCategory = Integer.parseInt(lastAudit.newValue());
            }
            coaAuditData.add(
                    new CoaAuditSummaryDto(
                            docId,
                            auditTypeMap.get(row),
                            coaIdCategoryMap.get(coaIdForCategory),
                            firstAudit.equals(lastAudit) ? lastAudit.prevValue() : firstAudit.newValue(),
                            lastAudit.newValue()

                    ));
        });
        return coaAuditData;
    }
}
