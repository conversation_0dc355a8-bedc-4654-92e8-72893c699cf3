package com.walnut.vegaspread.audit.service.workflow;

import com.walnut.vegaspread.audit.entity.workflow.DocumentAuditEntity;
import com.walnut.vegaspread.audit.repository.workflow.DocumentAuditRepository;
import com.walnut.vegaspread.audit.service.common.ExchangeService;
import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import com.walnut.vegaspread.common.model.audit.workflow.DocumentAuditDto;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.PathParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.eclipse.microprofile.jwt.JsonWebToken;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.walnut.vegaspread.common.utils.Jwt.getUsername;

@ApplicationScoped
public class DocumentAuditService {

    private static final Logger logger = LogManager.getLogger(DocumentAuditService.class);
    private final JsonWebToken accessToken;
    private final DocumentAuditRepository documentAuditRepository;
    private final ExchangeService exchangeService;

    public DocumentAuditService(JsonWebToken accessToken, DocumentAuditRepository documentAuditRepository,
                                ExchangeService exchangeService) {
        this.accessToken = accessToken;
        this.documentAuditRepository = documentAuditRepository;
        this.exchangeService = exchangeService;
    }

    @Transactional
    public List<DocumentAuditDto.Response> auditForCreate(List<DocumentAuditDto.Create> documentAuditCreateDtos,
                                                          String username) {
        List<DocumentAuditEntity> documentAuditEntities = new ArrayList<>();
        if (documentAuditCreateDtos == null || documentAuditCreateDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String fullName = username;
            if (username == null) {
                username = getUsername(accessToken);
                fullName = exchangeService.getNameFromUsername(username);
            }
            for (DocumentAuditDto.Create documentAuditCreateDto : documentAuditCreateDtos) {
                DocumentAuditEntity documentAuditEntity = new DocumentAuditEntity();
                documentAuditEntity.setDocId(documentAuditCreateDto.docId());
                documentAuditEntity.setColName(documentAuditCreateDto.colName());
                documentAuditEntity.setPrevValue(null);
                documentAuditEntity.setNewValue(documentAuditCreateDto.newValue());
                documentAuditEntity.setAuditTime(LocalDateTime.now());
                documentAuditEntity.setAuditedBy(username);
                documentAuditEntity.setAuditedBYFullName(fullName);
                documentAuditEntity.setAction(AuditStatus.CREATED);
                documentAuditEntities.add(documentAuditEntity);
            }
            documentAuditRepository.persist(documentAuditEntities);
            return DocumentAuditEntity.toDtoList(documentAuditEntities);
        }
    }

    @Transactional
    public List<DocumentAuditDto.Response> auditForUpdate(
            List<DocumentAuditDto.UpdateOrDelete> documentAuditUpdateDtos) {
        List<DocumentAuditEntity> documentAuditEntities = new ArrayList<>();
        if (documentAuditUpdateDtos == null || documentAuditUpdateDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String username = getUsername(accessToken);
            String fullName = exchangeService.getNameFromUsername(username);
            for (DocumentAuditDto.UpdateOrDelete documentAuditUpdateDto : documentAuditUpdateDtos) {
                DocumentAuditEntity documentAuditEntity = new DocumentAuditEntity();
                documentAuditEntity.setDocId(documentAuditUpdateDto.docId());
                documentAuditEntity.setColName(documentAuditUpdateDto.colName());
                documentAuditEntity.setPrevValue(documentAuditUpdateDto.prevValue());
                documentAuditEntity.setNewValue(documentAuditUpdateDto.newValue());
                documentAuditEntity.setAuditTime(LocalDateTime.now());
                documentAuditEntity.setAuditedBy(username);
                documentAuditEntity.setAuditedBYFullName(fullName);
                documentAuditEntity.setAction(AuditStatus.UPDATED);
                documentAuditEntities.add(documentAuditEntity);
            }
            documentAuditRepository.persist(documentAuditEntities);
            return DocumentAuditEntity.toDtoList(documentAuditEntities);
        }
    }

    @Transactional
    public List<DocumentAuditDto.Response> auditForDelete(
            List<DocumentAuditDto.UpdateOrDelete> documentAuditDeleteDtos) {
        List<DocumentAuditEntity> documentAuditEntities = new ArrayList<>();
        if (documentAuditDeleteDtos == null || documentAuditDeleteDtos.isEmpty()) {
            return Collections.emptyList();
        } else {
            String username = getUsername(accessToken);
            String fullName = exchangeService.getNameFromUsername(username);
            for (DocumentAuditDto.UpdateOrDelete documentAuditDeleteDto : documentAuditDeleteDtos) {
                DocumentAuditEntity documentAuditEntity = new DocumentAuditEntity();
                documentAuditEntity.setDocId(documentAuditDeleteDto.docId());
                documentAuditEntity.setColName(documentAuditDeleteDto.colName());
                documentAuditEntity.setPrevValue(documentAuditDeleteDto.prevValue());
                documentAuditEntity.setNewValue(documentAuditDeleteDto.newValue());
                documentAuditEntity.setAuditTime(LocalDateTime.now());
                documentAuditEntity.setAuditedBy(username);
                documentAuditEntity.setAuditedBYFullName(fullName);
                documentAuditEntity.setAction(AuditStatus.DELETED);
                documentAuditEntities.add(documentAuditEntity);
            }
            documentAuditRepository.persist(documentAuditEntities);
            return DocumentAuditEntity.toDtoList(documentAuditEntities);
        }
    }

    public DocumentAuditDto.Response get(@PathParam("auditId") Integer id) {
        Optional<DocumentAuditEntity> optDocumentAuditEntity = documentAuditRepository.findByIdOptional(id);
        if (optDocumentAuditEntity.isPresent()) {
            return optDocumentAuditEntity.get().toDto();
        } else {
            logger.error("Failed to fetch document audit data for missing id {}", id);
            return null;
        }
    }

    public List<DocumentAuditDto.Response> getByDocId(@PathParam("docId") UUID docId) {
        return DocumentAuditEntity.toDtoList(documentAuditRepository.findByDocId(docId));
    }
}
