package com.walnut.vegaspread.audit.resource.extraction;

import com.walnut.vegaspread.audit.service.extraction.SubtotalService;
import com.walnut.vegaspread.common.model.audit.extraction.SubtotalAuditDto;
import io.quarkus.security.Authenticated;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/extracted/subtotal")
@Authenticated
public class SubtotalAuditResource {

    private final SubtotalService subtotalService;

    public SubtotalAuditResource(SubtotalService subtotalService) {
        this.subtotalService = subtotalService;
    }

    @POST
    @Path("/audit-create")
    public List<SubtotalAuditDto.Response> auditForCreate(
            List<SubtotalAuditDto.Create> subtotalAuditCreateDtos) {
        return subtotalService.auditForCreate(subtotalAuditCreateDtos);
    }

    @POST
    @Path("/audit-update")
    public List<SubtotalAuditDto.Response> auditForUpdate(
            List<SubtotalAuditDto.Update> subtotalAuditUpdateDtos) {
        return subtotalService.auditForUpdate(subtotalAuditUpdateDtos, null);
    }

    @POST
    @Path("/audit-delete")
    public List<SubtotalAuditDto.Response> auditForDelete(
            List<SubtotalAuditDto.Delete> subtotalAuditDeleteDtos) {
        return subtotalService.auditForDelete(subtotalAuditDeleteDtos, null);
    }
}