package com.walnut.vegaspread.audit.resource.extraction;

import com.walnut.vegaspread.audit.service.extraction.ExtractedTableHeaderAuditService;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedTableHeaderAuditDto;
import io.quarkus.security.Authenticated;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/extracted/table/header")
@Authenticated
public class ExtractedTableHeaderAuditResource {

    private final ExtractedTableHeaderAuditService extractedTableHeaderAuditService;

    public ExtractedTableHeaderAuditResource(ExtractedTableHeaderAuditService extractedTableHeaderAuditService) {
        this.extractedTableHeaderAuditService = extractedTableHeaderAuditService;
    }

    @POST
    @Path("/audit-create")
    public List<ExtractedTableHeaderAuditDto.Response> auditForCreate(
            List<ExtractedTableHeaderAuditDto.Create> extractedTableHeaderCreateAuditDtos) {
        return extractedTableHeaderAuditService.auditForCreate(extractedTableHeaderCreateAuditDtos, null);
    }

    @POST
    @Path("/audit-update")
    public List<ExtractedTableHeaderAuditDto.Response> auditForUpdate(
            List<ExtractedTableHeaderAuditDto.Update> extractedTableHeaderUpdateAuditDtos) {
        return extractedTableHeaderAuditService.auditForUpdate(extractedTableHeaderUpdateAuditDtos, null);
    }

    @POST
    @Path("/audit-delete")
    public List<ExtractedTableHeaderAuditDto.Response> auditForDelete(
            List<ExtractedTableHeaderAuditDto.Delete> extractedTableHeaderDeleteAuditDtos) {
        return extractedTableHeaderAuditService.auditForDelete(extractedTableHeaderDeleteAuditDtos, null);
    }

    @POST
    @Path("/{colName}/list")
    public List<ExtractedTableHeaderAuditDto.Response> listAuditsForBlocksAndCol(@PathParam("colName") String colName,
                                                                                 List<Integer> blockIds) {
        return extractedTableHeaderAuditService.getColAuditsForTables(blockIds, colName);
    }
}
