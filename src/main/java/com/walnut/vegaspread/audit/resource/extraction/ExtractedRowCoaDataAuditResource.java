package com.walnut.vegaspread.audit.resource.extraction;

import com.walnut.vegaspread.audit.service.extraction.ExtractedRowCoaDataAuditService;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedRowCoaDataAuditDto;
import io.quarkus.security.Authenticated;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/extracted/table/row-coa")
@Authenticated
public class ExtractedRowCoaDataAuditResource {

    private final ExtractedRowCoaDataAuditService extractedRowCoaDataAuditService;

    public ExtractedRowCoaDataAuditResource(ExtractedRowCoaDataAuditService extractedRowCoaDataAuditService) {
        this.extractedRowCoaDataAuditService = extractedRowCoaDataAuditService;
    }

    @POST
    @Path("/audit-create")
    public List<ExtractedRowCoaDataAuditDto.Response> auditForCreate(
            List<ExtractedRowCoaDataAuditDto.Create> extractedRowCoaDataCreateAuditDtos) {
        return extractedRowCoaDataAuditService.auditForCreate(extractedRowCoaDataCreateAuditDtos, null);
    }

    @POST
    @Path("/audit-update")
    public List<ExtractedRowCoaDataAuditDto.Response> auditForUpdate(
            List<ExtractedRowCoaDataAuditDto.Update> extractedRowCoaDataUpdateAuditDtos) {
        return extractedRowCoaDataAuditService.auditForUpdate(extractedRowCoaDataUpdateAuditDtos, null);
    }

    @POST
    @Path("/audit-delete")
    public List<ExtractedRowCoaDataAuditDto.Response> auditForDelete(
            List<ExtractedRowCoaDataAuditDto.Delete> extractedRowCoaDataDeleteAuditDtos) {
        return extractedRowCoaDataAuditService.auditForDelete(extractedRowCoaDataDeleteAuditDtos, null);
    }

    @POST
    @Path("/list")
    public ExtractedRowCoaDataAuditDto.ListResponse listAuditsForTables(
            ExtractedRowCoaDataAuditDto.ListForTables listTablesDto) {
        return extractedRowCoaDataAuditService.filterAuditsForTables(listTablesDto);
    }

    @POST
    @Path("/{colName}/list")
    public List<ExtractedRowCoaDataAuditDto.Response> listAuditsForTablesAndCol(@PathParam("colName") String colName,
                                                                                List<Integer> tableIds) {
        return extractedRowCoaDataAuditService.getColAuditsForTables(tableIds, colName);
    }
}
