package com.walnut.vegaspread.audit.resource.view;

import com.walnut.vegaspread.audit.model.view.CoaAuditSummary;
import com.walnut.vegaspread.audit.service.view.CoaAuditSummaryService;
import io.quarkus.security.Authenticated;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.UUID;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/summary/coa")
@Authenticated
public class CoaAuditSummaryResource {

    private final CoaAuditSummaryService coaAuditSummaryService;

    public CoaAuditSummaryResource(CoaAuditSummaryService coaAuditSummaryService) {
        this.coaAuditSummaryService = coaAuditSummaryService;
    }

    @GET
    @Path("/accuracy/{docId}")
    public CoaAuditSummary.CoaAuditSummaryOutput getAccuracy(@PathParam("docId") UUID docId) {
        return coaAuditSummaryService.getAccuracy(docId);
    }

    @POST
    @Path("/accuracy/{docId}")
    public CoaAuditSummary.CoaAuditSummaryOutput calculateAccuracy(@PathParam("docId") UUID docId) {
        return coaAuditSummaryService.calculateAccuracy(docId);
    }
}
