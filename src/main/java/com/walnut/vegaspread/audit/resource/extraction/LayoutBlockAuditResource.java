package com.walnut.vegaspread.audit.resource.extraction;

import com.walnut.vegaspread.audit.service.extraction.LayoutBlockAuditService;
import com.walnut.vegaspread.common.model.audit.extraction.LayoutBlockAuditDto;
import io.quarkus.security.Authenticated;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("extracted/block")
@Authenticated
public class LayoutBlockAuditResource {

    private final LayoutBlockAuditService layoutBlockAuditService;

    public LayoutBlockAuditResource(LayoutBlockAuditService layoutBlockAuditService) {
        this.layoutBlockAuditService = layoutBlockAuditService;
    }

    @POST
    @Path("/audit-create")
    public List<LayoutBlockAuditDto.Response> auditForCreate(
            List<LayoutBlockAuditDto.Create> layoutBlockAuditCreateDtos) {
        return layoutBlockAuditService.auditForCreate(layoutBlockAuditCreateDtos, null);
    }

    /**
     * Endpoint to audit data for updation of layout block.
     */
    @POST
    @Path("/audit-update")
    public List<LayoutBlockAuditDto.Response> auditForUpdate(
            List<LayoutBlockAuditDto.Update> layoutBlockAuditUpdateDtos) {
        return layoutBlockAuditService.auditForUpdate(layoutBlockAuditUpdateDtos, null);
    }

    @POST
    @Path("/audit-delete")
    public List<LayoutBlockAuditDto.Response> auditForDelete(
            List<LayoutBlockAuditDto.Delete> layoutBlockAuditDeleteDtos) {
        return layoutBlockAuditService.auditForDelete(layoutBlockAuditDeleteDtos, null);
    }

    @POST
    @Path("/{colName}/list")
    public List<LayoutBlockAuditDto.Response> listAuditsForBlocksAndCol(@PathParam("colName") String colName,
                                                                        List<Integer> blockIds) {
        return layoutBlockAuditService.getColAuditsForBlocks(blockIds, colName);
    }
}