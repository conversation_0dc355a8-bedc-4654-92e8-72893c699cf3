package com.walnut.vegaspread.audit.resource.coa;

import com.walnut.vegaspread.audit.service.coa.CoaItemAuditService;
import com.walnut.vegaspread.common.model.audit.coa.CoaItemAuditDto;
import com.walnut.vegaspread.common.roles.Roles;
import io.quarkus.security.Authenticated;
import jakarta.annotation.security.RolesAllowed;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/coa/coaitem")
@Authenticated
public class CoaItemAuditResource {

    private final CoaItemAuditService coaItemAuditService;

    public CoaItemAuditResource(CoaItemAuditService coaItemAuditService) {
        this.coaItemAuditService = coaItemAuditService;
    }

    @POST
    @Path("/audit-create")
    @RolesAllowed(Roles.CREATE_NEW_COA)
    public List<CoaItemAuditDto.Response> auditForCreate(List<CoaItemAuditDto.Create> coaItemAuditEntityCreateDtos) {
        return coaItemAuditService.auditForCreate(coaItemAuditEntityCreateDtos);
    }

    @POST
    @Path("/audit-update")
    public List<CoaItemAuditDto.Response> auditForUpdate(List<CoaItemAuditDto.Update> coaItemAuditEntityUpdateDtos) {
        return coaItemAuditService.auditForUpdate(coaItemAuditEntityUpdateDtos);
    }

    @POST
    @Path("/audit-delete")
    public List<CoaItemAuditDto.Response> auditForDelete(List<CoaItemAuditDto.Delete> coaItemAuditEntityDeleteDtos) {
        return coaItemAuditService.auditForDelete(coaItemAuditEntityDeleteDtos);
    }

    @Path("/list")
    @GET
    public List<CoaItemAuditDto.Response> list() {
        return coaItemAuditService.list();
    }
}
