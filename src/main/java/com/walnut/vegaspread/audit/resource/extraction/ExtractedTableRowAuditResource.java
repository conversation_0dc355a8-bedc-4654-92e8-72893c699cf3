package com.walnut.vegaspread.audit.resource.extraction;

import com.walnut.vegaspread.audit.service.extraction.ExtractedTableRowAuditService;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedTableRowAuditDto;
import io.quarkus.security.Authenticated;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/extracted/table/row")
@Authenticated
public class ExtractedTableRowAuditResource {

    private final ExtractedTableRowAuditService extractedTableRowAuditService;

    public ExtractedTableRowAuditResource(ExtractedTableRowAuditService extractedTableRowAuditService) {
        this.extractedTableRowAuditService = extractedTableRowAuditService;
    }

    @POST
    @Path("/audit-create")
    public List<ExtractedTableRowAuditDto.Response> auditForCreate(
            List<ExtractedTableRowAuditDto.Create> extractedTableRowCreateAuditDtos) {
        return extractedTableRowAuditService.auditForCreate(extractedTableRowCreateAuditDtos);
    }

    @POST
    @Path("/audit-update")
    public List<ExtractedTableRowAuditDto.Response> auditForUpdate(
            List<ExtractedTableRowAuditDto.Update> extractedTableRowUpdateAuditDtos) {
        return extractedTableRowAuditService.auditForUpdate(extractedTableRowUpdateAuditDtos, null);
    }

    @POST
    @Path("/audit-delete")
    public List<ExtractedTableRowAuditDto.Response> auditForDelete(
            List<ExtractedTableRowAuditDto.Delete> extractedTableRowDeleteAuditDtos) {
        return extractedTableRowAuditService.auditForDelete(extractedTableRowDeleteAuditDtos);
    }

    @POST
    @Path("/list")
    public ExtractedTableRowAuditDto.ListResponse listAuditsForTables(
            ExtractedTableRowAuditDto.ListForTables listTablesDto) {
        return extractedTableRowAuditService.filterAuditsForTables(listTablesDto);
    }

    @POST
    @Path("/{colName}/list")
    public List<ExtractedTableRowAuditDto.Response> listAuditsForBlocksAndCol(@PathParam("colName") String colName,
                                                                              List<Integer> blockIds) {
        return extractedTableRowAuditService.getColAuditsForTables(blockIds, colName);
    }
}
