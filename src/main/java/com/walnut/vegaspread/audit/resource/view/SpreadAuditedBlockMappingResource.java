package com.walnut.vegaspread.audit.resource.view;

import com.walnut.vegaspread.audit.service.view.SpreadAuditedBlockMappingService;
import io.quarkus.security.Authenticated;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/mapping/spread-audited-block")
@Authenticated
public class SpreadAuditedBlockMappingResource {

    private final SpreadAuditedBlockMappingService spreadAuditedBlockMappingService;

    public SpreadAuditedBlockMappingResource(SpreadAuditedBlockMappingService spreadAuditedBlockMappingService) {
        this.spreadAuditedBlockMappingService = spreadAuditedBlockMappingService;
    }

    @GET
    @Path("/{spreadId}")
    public List<Integer> getAuditedBlocksForSpread(@PathParam("spreadId") Integer spreadId) {
        return spreadAuditedBlockMappingService.getAuditedBlockIdsForSpread(spreadId);
    }
}
