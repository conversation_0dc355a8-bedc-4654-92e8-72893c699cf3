package com.walnut.vegaspread.audit.resource.extraction;

import com.walnut.vegaspread.audit.entity.extraction.SubtotalMappingAuditEntity;
import com.walnut.vegaspread.audit.service.extraction.SubtotalMappingAuditService;
import com.walnut.vegaspread.common.model.audit.extraction.SubtotalMappingAuditDto;
import io.quarkus.security.Authenticated;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/extracted/subtotal-mapping")
@Authenticated
public class SubtotalMappingAuditResource {

    private final SubtotalMappingAuditService subtotalMappingAuditService;

    public SubtotalMappingAuditResource(SubtotalMappingAuditService subtotalMappingAuditService) {
        this.subtotalMappingAuditService = subtotalMappingAuditService;
    }

    @POST
    @Path("/audit-create")
    public List<SubtotalMappingAuditDto.Response> auditForCreate(
            List<SubtotalMappingAuditDto.Create> subtotalMappingAuditCreateDtos) {
        return SubtotalMappingAuditEntity.toDtoList(
                subtotalMappingAuditService.auditForCreate(subtotalMappingAuditCreateDtos));
    }

    @POST
    @Path("/audit-update")
    public List<SubtotalMappingAuditDto.Response> auditForUpdate(
            List<SubtotalMappingAuditDto.Update> subtotalMappingAuditUpdateDtos) {
        return SubtotalMappingAuditEntity.toDtoList(
                subtotalMappingAuditService.auditForUpdate(subtotalMappingAuditUpdateDtos));
    }

    @POST
    @Path("/audit-delete")
    public List<SubtotalMappingAuditDto.Response> auditForDelete(
            List<SubtotalMappingAuditDto.Delete> subtotalMappingAuditDeleteDtos) {
        return SubtotalMappingAuditEntity.toDtoList(
                subtotalMappingAuditService.auditForDelete(subtotalMappingAuditDeleteDtos));
    }
}