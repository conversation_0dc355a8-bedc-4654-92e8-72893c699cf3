package com.walnut.vegaspread.audit.resource.workflow;

import com.walnut.vegaspread.audit.service.workflow.ReviewAuditService;
import com.walnut.vegaspread.common.model.audit.workflow.ReviewAuditDto;
import io.quarkus.security.Authenticated;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/workflow/review")
@Authenticated
public class ReviewAuditResource {

    private final ReviewAuditService reviewAuditService;

    public ReviewAuditResource(ReviewAuditService reviewAuditService) {
        this.reviewAuditService = reviewAuditService;
    }

    @POST
    @Path("/audit-create")
    public List<ReviewAuditDto.Response> auditForCreate(List<ReviewAuditDto.Create> reviewAuditCreateDtos) {
        return reviewAuditService.auditForCreate(reviewAuditCreateDtos);
    }

    @POST
    @Path("/audit-update")
    public List<ReviewAuditDto.Response> auditForUpdate(List<ReviewAuditDto.Update> reviewAuditUpdateDtos) {
        return reviewAuditService.auditForUpdate(reviewAuditUpdateDtos);
    }

    @GET
    @Path("/audit/{auditId}")
    public ReviewAuditDto.Response get(@PathParam("auditId") Integer id) {
        return reviewAuditService.get(id);
    }

    @GET
    @Path("/{reviewId}")
    public List<ReviewAuditDto.Response> getByReviewId(@PathParam("reviewId") Integer reviewId) {
        return reviewAuditService.getByReviewId(reviewId);
    }
}
