package com.walnut.vegaspread.audit.resource.common;

import com.walnut.vegaspread.audit.model.common.HistoryDto;
import com.walnut.vegaspread.audit.service.common.HistoryService;
import io.quarkus.security.Authenticated;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/history")
@Authenticated
public class HistoryResource {
    private final HistoryService historyService;

    public HistoryResource(HistoryService historyService) {
        this.historyService = historyService;
    }

    @POST
    public HistoryDto.Response getHistory(HistoryDto.Request auditListDto) {
        return historyService.getHistory(auditListDto);
    }

    @GET
    @Path("/types")
    public List<String> getAuditTypes() {
        return historyService.getAuditTypes();
    }
}
