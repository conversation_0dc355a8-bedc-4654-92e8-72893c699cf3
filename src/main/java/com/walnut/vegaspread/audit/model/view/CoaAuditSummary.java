package com.walnut.vegaspread.audit.model.view;

import java.util.List;
import java.util.Objects;

public interface CoaAuditSummary {

    record CoaAuditSummaryRow(Integer rowId, List<String> cellsText) {
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof CoaAuditSummaryRow that)) return false;
            return Objects.equals(rowId, that.rowId) && Objects.equals(cellsText, that.cellsText);
        }
    }

    record CoaAuditSummaryHeader(Integer headerId, String text) {
    }

    record CoaAuditSummaryOutput(List<CoaAuditSummaryHeader> headers, List<CoaAuditSummaryRow> rows) {
    }
}
