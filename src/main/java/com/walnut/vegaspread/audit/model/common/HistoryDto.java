package com.walnut.vegaspread.audit.model.common;

import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface HistoryDto {
    record Request(int spreadId, @NotNull AuditListType auditType, int pageNumber, int pageSize, String auditor,
                   LocalDate auditDate) {
    }

    record Response(int pageNumber, int pageSize, int totalPages, List<Item> auditItems,
                    Map<String, String> allUsers, List<LocalDate> allDates) {
    }

    record Item(@NotNull Integer blockId, @NotNull Integer rowId, String prevValue,
                String newValue, @NotNull AuditStatus action, @NotNull LocalDateTime auditTime,
                @NotNull String auditedBy,
                String auditedBYFullName) {
    }
}