package com.walnut.vegaspread.audit.model.common;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.AllArgsConstructor;
import lombok.Getter;

public interface JpaReflection {
    @Getter
    @RegisterForReflection
    class CoaIdDto {
        Long coaId;

        public CoaIdDto(Long coaId) {

            this.coaId = coaId;
        }
    }

    @Getter
    @AllArgsConstructor
    @RegisterForReflection
    class BlockIdDto {
        Integer blockId;
    }
}