package com.walnut.vegaspread.audit.repository.extraction;

import com.walnut.vegaspread.audit.entity.extraction.ExtractedTableHeaderAuditEntity;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

@ApplicationScoped
public class ExtractedTableHeaderAuditRepository implements PanacheRepositoryBase<ExtractedTableHeaderAuditEntity,
        Integer> {

    public List<ExtractedTableHeaderAuditEntity> findByTableIdsAndColName(List<Integer> tableIds, String colName) {
        return find("tableId in ?1 AND colName = ?2", tableIds, colName).list();
    }
}
