package com.walnut.vegaspread.audit.repository.view;

import com.walnut.vegaspread.audit.entity.view.CoaAuditSummary;
import com.walnut.vegaspread.audit.model.common.JpaReflection;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@ApplicationScoped
public class CoaAuditSummaryRepository implements PanacheRepositoryBase<CoaAuditSummary, Long> {
    public static final String WILDCARD = "%";

    public Set<Long> getUniquePrevCoaIdForDocAndCategoryAndAuditType(UUID docId, String category,
                                                                     List<String> auditType) {
        return find(
                "SELECT DISTINCT prevCoaId FROM "
                        + "CoaAuditSummary" +
                        " WHERE docId = ?1 AND category LIKE ?2 AND auditType IN ?3",
                docId, category.concat(WILDCARD), auditType).project(JpaReflection.CoaIdDto.class)
                .stream()
                .map(JpaReflection.CoaIdDto::getCoaId)
                .collect(Collectors.toSet());
    }

    public Set<Long> getUniqueNewCoaIdForDocAndCategoryAndAuditType(UUID docId, String category,
                                                                    List<String> auditType) {
        return find(
                "SELECT DISTINCT newCoaId FROM "
                        + "CoaAuditSummary" +
                        " WHERE docId = ?1 AND category LIKE ?2 AND auditType IN ?3",
                docId, category.concat(WILDCARD), auditType).project(JpaReflection.CoaIdDto.class)
                .stream()
                .map(JpaReflection.CoaIdDto::getCoaId)
                .collect(Collectors.toSet());
    }

    public long countAuditsWithDocIdAndCategoryAndAuditType(UUID docId, String category, List<String> auditType) {
        return count("docId = ?1 AND category LIKE ?2 AND auditType IN ?3", docId, category.concat(WILDCARD),
                auditType);
    }
}
