package com.walnut.vegaspread.audit.repository.extraction;

import com.walnut.vegaspread.audit.entity.extraction.LayoutBlockAuditEntity;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import io.quarkus.panache.common.Parameters;
import jakarta.enterprise.context.ApplicationScoped;

import java.time.LocalDate;
import java.util.List;

@ApplicationScoped
public class LayoutBlockAuditRepository implements PanacheRepositoryBase<LayoutBlockAuditEntity, Integer> {
    public List<String> distinctUsersForFilteredFieldInBlocks(StringBuilder query, Parameters params) {
        query.insert(0, "SELECT DISTINCT auditedBy FROM LayoutBlockAuditEntity WHERE ");
        return find(query.toString(), params).project(String.class).list();
    }

    public List<LocalDate> distinctAuditDatesForFilteredFieldInBlocks(StringBuilder query, Parameters params) {
        query.insert(0, "SELECT DISTINCT DATE(auditTime) FROM LayoutBlockAuditEntity WHERE ");
        return find(query.toString(), params)
                .project(java.sql.Date.class)
                .stream()
                .map(java.sql.Date::toLocalDate)
                .toList();
    }

    public List<LayoutBlockAuditEntity> findByBlockIdsAndColName(List<Integer> blockIds, String colName) {
        return find("blockId in ?1 AND colName = ?2", blockIds, colName).list();
    }
}
