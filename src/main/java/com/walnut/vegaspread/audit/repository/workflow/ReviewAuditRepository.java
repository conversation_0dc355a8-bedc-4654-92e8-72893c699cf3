package com.walnut.vegaspread.audit.repository.workflow;

import com.walnut.vegaspread.audit.entity.workflow.ReviewAuditEntity;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

@ApplicationScoped
public class ReviewAuditRepository implements PanacheRepositoryBase<ReviewAuditEntity, Integer> {

    public List<ReviewAuditEntity> findByReviewId(Integer reviewId) {
        return find("reviewId", reviewId).list();
    }
}
