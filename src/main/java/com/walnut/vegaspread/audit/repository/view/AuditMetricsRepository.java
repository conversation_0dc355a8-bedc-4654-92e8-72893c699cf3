package com.walnut.vegaspread.audit.repository.view;

import com.walnut.vegaspread.audit.entity.view.AuditMetricsEntity;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;
import java.util.UUID;

@ApplicationScoped
public class AuditMetricsRepository implements PanacheRepositoryBase<AuditMetricsEntity, Long> {
    public List<AuditMetricsEntity> findByDocId(UUID docId) {
        Sort sort = Sort.by("createdTime", Sort.Direction.Descending);
        return find("docId", sort, docId).list();
    }
}
