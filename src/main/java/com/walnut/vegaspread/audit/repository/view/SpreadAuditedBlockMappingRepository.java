package com.walnut.vegaspread.audit.repository.view;

import com.walnut.vegaspread.audit.entity.view.SpreadAuditedBlockMapping;
import com.walnut.vegaspread.audit.model.common.JpaReflection;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

@ApplicationScoped
public class SpreadAuditedBlockMappingRepository implements PanacheRepositoryBase<SpreadAuditedBlockMapping, Long> {

    public List<Integer> getAuditedBlockIdsForSpread(Integer spreadId) {
        return find("SELECT blockId FROM SpreadAuditedBlockMapping WHERE spreadId = ?1", spreadId)
                .project(JpaReflection.BlockIdDto.class)
                .stream()
                .map(JpaReflection.BlockIdDto::getBlockId)
                .toList();
    }
}
