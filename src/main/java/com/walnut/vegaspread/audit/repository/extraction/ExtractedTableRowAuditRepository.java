package com.walnut.vegaspread.audit.repository.extraction;

import com.walnut.vegaspread.audit.entity.extraction.ExtractedTableRowAuditEntity;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import io.quarkus.panache.common.Parameters;
import jakarta.enterprise.context.ApplicationScoped;

import java.time.LocalDate;
import java.util.List;

@ApplicationScoped
public class ExtractedTableRowAuditRepository implements PanacheRepositoryBase<ExtractedTableRowAuditEntity, Integer> {

    public List<String> distinctUsersForFilteredFieldInTables(StringBuilder query, Parameters params) {
        query.insert(0, "SELECT DISTINCT auditedBy FROM ExtractedTableRowAuditEntity WHERE ");
        return find(query.toString(), params).project(String.class).list();
    }

    public List<LocalDate> distinctAuditDatesForFilteredFieldInTables(StringBuilder query, Parameters params) {
        query.insert(0, "SELECT DISTINCT DATE(auditTime) FROM ExtractedTableRowAuditEntity WHERE ");
        return find(query.toString(), params)
                .project(java.sql.Date.class)
                .stream()
                .map(java.sql.Date::toLocalDate)
                .toList();
    }

    public List<ExtractedTableRowAuditEntity> findByTableIdsAndColName(List<Integer> tableIds, String colName) {
        return find("tableId in ?1 AND colName = ?2", tableIds, colName).list();
    }
}
