package com.walnut.vegaspread.audit.repository.workflow;

import com.walnut.vegaspread.audit.entity.workflow.DocumentAuditEntity;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;
import java.util.UUID;

@ApplicationScoped
public class DocumentAuditRepository implements PanacheRepositoryBase<DocumentAuditEntity, Integer> {
    public List<DocumentAuditEntity> findByDocId(UUID docId) {
        return find("docId", docId).list();
    }
}
