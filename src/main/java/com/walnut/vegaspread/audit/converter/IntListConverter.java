package com.walnut.vegaspread.audit.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Converter
public class IntListConverter implements AttributeConverter<List<Integer>, String> {

    @Override
    public String convertToDatabaseColumn(List<Integer> integers) {
        if (integers != null && !integers.isEmpty()) {
            return integers.stream()
                    .map(Object::toString)
                    .collect(Collectors.joining(","));
        }
        return "";
    }

    @Override
    public List<Integer> convertToEntityAttribute(String s) {
        if (!Objects.equals(s, "")) {
            return Arrays.stream(s.split(","))
                    .map(String::trim)
                    .map(Integer::valueOf)
                    .toList();
        }
        return Collections.emptyList();
    }
}
