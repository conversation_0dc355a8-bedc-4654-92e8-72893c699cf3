package com.walnut.vegaspread.workflow.service;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.common.model.workflow.StatusEnum;
import com.walnut.vegaspread.workflow.entity.Auditable;
import com.walnut.vegaspread.workflow.entity.Document;
import com.walnut.vegaspread.workflow.entity.EntityName;
import com.walnut.vegaspread.workflow.entity.Industry;
import com.walnut.vegaspread.workflow.entity.Region;
import com.walnut.vegaspread.workflow.entity.SpreadingTask;
import com.walnut.vegaspread.workflow.model.CreateTaskDto;
import com.walnut.vegaspread.workflow.model.DocOutputDto;
import com.walnut.vegaspread.workflow.model.MetadataDto;
import com.walnut.vegaspread.workflow.model.SpreadOutputDto;
import com.walnut.vegaspread.workflow.repository.SpreadingTaskRepository;
import com.walnut.vegaspread.workflow.utils.OutputFormatter;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Validator;
import jakarta.ws.rs.core.Response;
import org.jboss.logging.Logger;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

@ApplicationScoped
public class TaskService {
    private static final String ALL_FILTER = "all";
    private static final Logger logger = Logger.getLogger(TaskService.class);
    private static final List<String> FILTER_BY_COLUMN_NAMES = Arrays.asList(
            SpreadingTask.ENTITY_NAME_FOREIGN_KEY_COL_NAME, SpreadingTask.INDUSTRY_FOREIGN_KEY_COL_NAME,
            SpreadingTask.REGION_FOREIGN_KEY_COL_NAME, ALL_FILTER);
    private static final Map<String, String> FILTER_BY_COLUMN_MAPPING = Map.of(
            SpreadingTask.ENTITY_NAME_FOREIGN_KEY_COL_NAME, "entityName.entityId",
            SpreadingTask.INDUSTRY_FOREIGN_KEY_COL_NAME, "industry.industryId",
            SpreadingTask.REGION_FOREIGN_KEY_COL_NAME, "region.regionId",
            ALL_FILTER, ALL_FILTER);

    private final SpreadingTaskRepository spreadingTaskRepository;
    private final DocumentService documentService;
    private final OutputFormatter outputFormatter;
    private final MetadataService metadataService;
    private final Validator validator;

    public TaskService(SpreadingTaskRepository spreadingTaskRepository, DocumentService documentService,
                       ExchangeService exchangeService, MetadataService metadataService, Validator validator) {
        this.spreadingTaskRepository = spreadingTaskRepository;
        this.documentService = documentService;
        this.outputFormatter = new OutputFormatter(exchangeService);
        this.metadataService = metadataService;
        this.validator = validator;
    }

    public DocOutputDto taskToOutput(SpreadingTask task) {
        return outputFormatter.taskToOutput(task.getDocuments().get(0), task);
    }

    public SpreadOutputDto taskToOutputList(SpreadingTask task) {
        return outputFormatter.taskToOutput(task.getDocuments(), task);
    }

    @Transactional
    public SpreadingTask create(CreateTaskDto taskDto, String clientName, String username) {
        if (taskDto == null) {
            logger.errorf("Task dto is null");
            return ResponseException.throwResponseException(Response.Status.BAD_REQUEST, "Task dto is null");
        }
        Set<ConstraintViolation<CreateTaskDto>> violations = validator.validate(taskDto);
        if (!violations.isEmpty()) {
            throw new ConstraintViolationException(violations);
        }
        Document doc = documentService.getFromDb(taskDto.docId());
        doc.setPeriod(taskDto.period());
        doc.setSpreadLevel(taskDto.spreadLevel());
        doc.setIsDigital(taskDto.isDigital());
        doc.setStatus(StatusEnum.CREATED);
        doc.setStatusText(StatusEnum.CREATED.toString());
        doc.setFileDenomination(taskDto.fileDenomination());
        doc.setOutputDenomination(taskDto.outputDenomination());
        doc.setLastModifiedBy(username);
        doc.setLastModifiedTime(LocalDateTime.now());

        SpreadingTask task = new SpreadingTask();
        task.setClientName(clientName);

        task.setEntityName(
                metadataService.getOrCreateEntity(
                        new MetadataDto.Entity(taskDto.entityName().id(), taskDto.entityName().name()), username));
        task.setIndustry(metadataService.getOrCreateIndustry(new MetadataDto.Industry(taskDto.industry().id(),
                taskDto.industry().name()), username));
        task.setRegion(metadataService.getOrCreateRegion(new MetadataDto.Region(taskDto.region().id(),
                taskDto.region().name()), username));

        task.setAuditable(new Auditable(username));
        task.setLastModifiedBy(username);
        task.setLastModifiedTime(task.getAuditable().getCreatedTime());

        spreadingTaskRepository.persist(task);
        doc.setSpreadingTask(task);
        documentService.updateInDb(doc);
        task.getDocuments().add(doc);
        return task;
    }

    @Transactional
    public SpreadingTask createTaskForUpdateInDbByUi(MetadataDto.Entity entityName, MetadataDto.Industry industry,
                                                     MetadataDto.Region region, String clientName, String username,
                                                     Document doc) {
        SpreadingTask task = new SpreadingTask();
        EntityName entityForTask = metadataService.getOrCreateEntity(entityName, username);
        Industry industryForTask = metadataService.getOrCreateIndustry(industry, username);
        Region regionForTask = metadataService.getOrCreateRegion(region, username);

        Optional<SpreadingTask> optSpreadingTask =
                spreadingTaskRepository.findByEntityNameAndIndustryNameAndRegionNameOptional(
                        entityForTask.getEntityId(),
                        industryForTask.getIndustryId(), regionForTask.getRegionId());
        if (optSpreadingTask.isPresent()) {
            task = optSpreadingTask.get();
        } else {
            task.setClientName(clientName);
            task.setEntityName(entityForTask);
            task.setIndustry(industryForTask);
            task.setRegion(regionForTask);
            task.setAuditable(new Auditable(username));
            task.setLastModifiedBy(username);
            task.setLastModifiedTime(task.getAuditable().getCreatedTime());
        }
        spreadingTaskRepository.persist(task);
        task.getDocuments().add(doc);
        return task;
    }

    public SpreadingTask get(Integer spreadId) {
        return spreadingTaskRepository.findById(spreadId);
    }

    public List<Integer> filterSpreadingTask(String filterColumn, Integer filterValue, String clientName) {
        if (!isValidFilterColumn(filterColumn)) {
            logger.errorf("Invalid column name: %s", filterColumn);
            return List.of();
        }
        return spreadingTaskRepository.filterTask(FILTER_BY_COLUMN_MAPPING.get(filterColumn), filterValue, clientName);
    }

    private boolean isValidFilterColumn(String columnName) {
        if (columnName == null || columnName.isBlank()) {
            return false;
        }
        return FILTER_BY_COLUMN_NAMES.contains(columnName);
    }

    @Transactional
    public SpreadingTask duplicateTask(UUID srcDocId, UUID destDocId, String username) {
        Document srcDoc = documentService.getFromDb(srcDocId);
        if (srcDoc == null) {
            logger.errorf("Source document %s not found to create duplicate task", srcDocId);
            return null;
        }
        Document destDoc = documentService.getFromDb(destDocId);
        if (destDoc == null) {
            logger.errorf("Destination document %s not found to create duplicate task", destDocId);
            return null;
        }

        SpreadingTask srcTask = srcDoc.getSpreadingTask();
        if (srcTask == null) {
            logger.errorf("Source document %s does not have a task to duplicate", srcDocId);
            return null;
        }

        SpreadingTask task = new SpreadingTask();
        task.setClientName(srcTask.getClientName());

        task.setEntityName(srcTask.getEntityName());
        task.setIndustry(srcTask.getIndustry());
        task.setRegion(srcTask.getRegion());
        task.setAuditable(new Auditable(username));
        task.setLastModifiedBy(username);
        task.setLastModifiedTime(LocalDateTime.now());

        spreadingTaskRepository.persist(task);
        destDoc.setSpreadingTask(task);
        documentService.updateInDb(destDoc);
        task.getDocuments().add(destDoc);
        return task;
    }

    @Transactional
    public void delete(Integer spreadId) {
        boolean isDeleted = spreadingTaskRepository.deleteById(spreadId);
        if (!isDeleted) {
            logger.errorf("Failed to delete task with id %s", spreadId);
            ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR,
                    "Failed to delete task with id " + spreadId);
        }
    }
}
