package com.walnut.vegaspread.workflow.service;

import com.google.auth.oauth2.AccessToken;
import com.google.auth.oauth2.GoogleCredentials;
import com.walnut.vegaspread.common.exceptions.ResponseException;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.core.Response;
import org.jboss.logging.Logger;

import java.io.IOException;

@ApplicationScoped
public class GcpAuthService {
    private static final Logger logger = Logger.getLogger(GcpAuthService.class);

    public String getGcpAccessToken() {
        try {
            GoogleCredentials credentials = GoogleCredentials.getApplicationDefault();
            credentials.refreshIfExpired();
            AccessToken token = credentials.getAccessToken();
            return token.getTokenValue();
        } catch (IOException e) {
            logger.error(e);
            ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR,
                    "Failed to get GCP access token");
            return null;
        }
    }
}
