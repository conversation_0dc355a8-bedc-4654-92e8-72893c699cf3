package com.walnut.vegaspread.workflow.service;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.workflow.entity.JobExecutionMonitoring;
import com.walnut.vegaspread.workflow.model.JobExecutionDto;
import com.walnut.vegaspread.workflow.model.StageEnum;
import com.walnut.vegaspread.workflow.repository.JobExecutionMonitoringRepository;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response;
import org.jboss.logging.Logger;

import java.util.List;
import java.util.UUID;

@ApplicationScoped
public class JobExecutionMonitoringService {
    private static final Logger logger = Logger.getLogger(JobExecutionMonitoringService.class);
    private final JobExecutionMonitoringRepository jobExecutionMonitoringRepository;

    public JobExecutionMonitoringService(JobExecutionMonitoringRepository jobExecutionMonitoringRepository) {
        this.jobExecutionMonitoringRepository = jobExecutionMonitoringRepository;
    }

    @Transactional
    public JobExecutionMonitoring auditStartJob(JobExecutionDto.StartJob jobExecutionStartDto) {
        JobExecutionMonitoring jobExecutionMonitoring = JobExecutionMonitoring.builder()
                .jobId(jobExecutionStartDto.jobId())
                .docId(jobExecutionStartDto.docId())
                .stage(jobExecutionStartDto.stage())
                .startTime(jobExecutionStartDto.startTime())
                .endTime(null)
                .isSuccess(null)
                .build();
        jobExecutionMonitoringRepository.persist(jobExecutionMonitoring);
        return jobExecutionMonitoring;
    }

    @Transactional
    public JobExecutionMonitoring auditEndJob(JobExecutionDto.EndJob jobExecutionEndDto) {
        JobExecutionMonitoring jobExecutionMonitoring = jobExecutionMonitoringRepository.findById(
                jobExecutionEndDto.jobId());
        if (jobExecutionMonitoring == null) {
            ResponseException.throwResponseException(Response.Status.NOT_FOUND,
                    "Start job monitoring record not found for job id " + jobExecutionEndDto.jobId());
            return null;
        }
        jobExecutionMonitoring.setEndTime(jobExecutionEndDto.endTime());
        jobExecutionMonitoring.setIsSuccess(jobExecutionEndDto.isSuccess());
        jobExecutionMonitoringRepository.persist(jobExecutionMonitoring);
        return jobExecutionMonitoring;
    }

    public List<JobExecutionMonitoring> findByDocId(UUID docId) {
        return jobExecutionMonitoringRepository.findByDocId(docId);
    }

    public List<JobExecutionMonitoring> findIncompleteJobs() {
        return jobExecutionMonitoringRepository.findIncompleteJobs();
    }

    @Transactional
    public void saveJobs(List<JobExecutionMonitoring> incompleteJobs) {
        jobExecutionMonitoringRepository.persist(incompleteJobs);
    }

    public List<JobExecutionMonitoring> findByDocIdAndStage(UUID docId, StageEnum docStage) {
        return jobExecutionMonitoringRepository.findByDocIdAndStage(docId, docStage);
    }

    public List<JobExecutionMonitoring> listAllJobs(int pageNumber, int pageSize) {
        return jobExecutionMonitoringRepository.findAll(Sort.by("startTime", Sort.Direction.Descending))
                .page(pageNumber, pageSize)
                .list();
    }

    public long getTotalJobs() {
        return jobExecutionMonitoringRepository.count();
    }

    public List<JobExecutionMonitoring> listAllJobsForDoc(UUID docId) {
        return jobExecutionMonitoringRepository.list("docId = ?1", Sort.by("startTime", Sort.Direction.Descending),
                docId);
    }

    public List<JobExecutionMonitoring> findByDocIds(List<UUID> docIds) {
        return jobExecutionMonitoringRepository.list("docId IN ?1", docIds);
    }
}
