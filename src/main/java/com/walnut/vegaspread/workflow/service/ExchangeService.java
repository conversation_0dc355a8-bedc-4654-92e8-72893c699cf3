package com.walnut.vegaspread.workflow.service;

import com.walnut.vegaspread.common.clients.AuditClient;
import com.walnut.vegaspread.common.clients.ClientFactory;
import com.walnut.vegaspread.common.clients.ExtractionClient;
import com.walnut.vegaspread.common.clients.IamApiClient;
import com.walnut.vegaspread.common.clients.IamClient;
import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.common.model.audit.workflow.DocumentAuditDto;
import com.walnut.vegaspread.common.model.audit.workflow.ReviewAuditDto;
import com.walnut.vegaspread.common.model.cloud.CloudPlatform;
import com.walnut.vegaspread.common.utils.ConfigKeys;
import com.walnut.vegaspread.workflow.entity.Document;
import com.walnut.vegaspread.workflow.entity.Review;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.logging.Logger;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

@ApplicationScoped
public class ExchangeService {
    private static final Logger logger = Logger.getLogger(ExchangeService.class);
    private final IamClient iamClient;
    private final IamApiClient iamApiClient;
    private final AuditClient auditClient;
    private final ExtractionClient extractionClient;

    public ExchangeService(@ConfigProperty(name = ConfigKeys.ENV_NAME_KEY) String envName,
                           @ConfigProperty(name = ConfigKeys.CLOUD_PROVIDER_TYPE) String cloudProviderType,
                           @ConfigProperty(name = ConfigKeys.AWS_GATEWAY_URL) Optional<String> awsGatewayUrl
    ) {
        logger.info("Creating exchange service for env: " + envName + " and cloud provider: " + cloudProviderType);
        if (cloudProviderType.equals(CloudPlatform.GCP.getProvider())) {
            this.iamClient = ClientFactory.createClient(IamClient.class, envName);
            this.auditClient = ClientFactory.createClient(AuditClient.class, envName);
            this.iamApiClient = ClientFactory.createClient(IamApiClient.class, envName);
            this.extractionClient = ClientFactory.createClient(ExtractionClient.class, envName);
        } else {
            if (awsGatewayUrl.isEmpty()) {
                ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR,
                        "AWS Gateway URL is not configured");
            }
            this.iamClient = ClientFactory.createAWSClient(IamClient.class, envName, awsGatewayUrl.get());
            this.auditClient = ClientFactory.createAWSClient(AuditClient.class, envName, awsGatewayUrl.get());
            this.iamApiClient = ClientFactory.createAWSClient(IamApiClient.class, envName, awsGatewayUrl.get());
            this.extractionClient = ClientFactory.createAWSClient(ExtractionClient.class, envName, awsGatewayUrl.get());
        }
    }

    public Map<String, String> getUsernameMapping(Stream<String> usernames) {
        return iamClient.getNamesFromUsernames(usernames.distinct().toList());
    }

    public String usernameToName(String username) {
        return iamClient.getNameFromUsername(username);
    }

    public Map<String, String> getUsernameMappingForDocs(List<Document> docs) {
        List<String> usernames = new ArrayList<>();
        for (Document doc : docs) {
            usernames.add(doc.auditable.createdBy);
            usernames.add(doc.lastModifiedBy);
            if (doc.reviews != null) {
                doc.reviews.stream()
                        .max(Comparator.comparing(Review::getReviewedTime))
                        .map(Review::getReviewer)
                        .ifPresent(usernames::add);
            }
        }
        return getUsernameMapping(usernames.stream());
    }

    public void createDocAudit(List<DocumentAuditDto.Create> auditDtoList, boolean isApiKeyAuthenticated) {
        if (Boolean.TRUE.equals(isApiKeyAuthenticated)) {
            auditClient.documentAuditForCreateByProcessor(auditDtoList);
        } else {
            auditClient.documentAuditForCreate(auditDtoList);
        }
    }

    public void updateDocAudit(List<DocumentAuditDto.UpdateOrDelete> auditDtoList) {
        auditClient.documentAuditForUpdate(auditDtoList);
    }

    public void createReviewAudit(List<ReviewAuditDto.Create> reviewAuditDtoList) {
        auditClient.reviewAuditForCreate(reviewAuditDtoList);
    }

    public void updateReviewAudit(List<ReviewAuditDto.Update> reviewAuditDtoList) {
        auditClient.reviewAuditForUpdate(reviewAuditDtoList);
    }

    public String getEmailForUser(String username) {
        return iamApiClient.getUserEmail(username).getEntity();
    }

    public List<Integer> getBlockIdsForDoc(UUID docId) {
        return extractionClient.getBlockIdsForDoc(docId);
    }

    public void deleteBlocksForDoc(UUID docId) {
        extractionClient.deleteBlocksByDocId(docId);
    }

    public void deleteDocAudit(List<DocumentAuditDto.UpdateOrDelete> auditDtoList) {
        auditClient.documentAuditForDelete(auditDtoList);
    }

    public long deleteCoaMappingForDoc(UUID docId) {
        return extractionClient.deleteCoaMappingByDocId(docId);
    }
}
