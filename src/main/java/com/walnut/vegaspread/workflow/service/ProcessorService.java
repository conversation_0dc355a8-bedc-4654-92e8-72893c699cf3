package com.walnut.vegaspread.workflow.service;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.common.model.audit.workflow.DocumentAuditDto;
import com.walnut.vegaspread.common.model.extraction.UrlDto;
import com.walnut.vegaspread.common.model.workflow.StatusEnum;
import com.walnut.vegaspread.common.utils.ConfigKeys;
import com.walnut.vegaspread.workflow.cloud.CloudProvider;
import com.walnut.vegaspread.workflow.cloud.CloudProviderFactory;
import com.walnut.vegaspread.workflow.entity.Document;
import com.walnut.vegaspread.workflow.entity.JobExecutionMonitoring;
import com.walnut.vegaspread.workflow.model.ApiKeyCredentialsDto;
import com.walnut.vegaspread.workflow.model.CamReportFormat;
import com.walnut.vegaspread.workflow.model.DocOutputDto;
import com.walnut.vegaspread.workflow.model.FileLocationDto;
import com.walnut.vegaspread.workflow.model.StageEnum;
import com.walnut.vegaspread.workflow.model.UpdateDocDto;
import com.walnut.vegaspread.workflow.model.WiseCallbackDto;
import com.walnut.vegaspread.workflow.utils.Config;
import com.walnut.vegaspread.workflow.utils.Utils;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.core.Response;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.multipart.FileUpload;

import java.io.IOException;
import java.net.URI;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static com.walnut.vegaspread.workflow.service.WiseWorkflowService.JOB_MONITORING_ENDPOINT;
import static com.walnut.vegaspread.workflow.utils.Config.WALNUT_AI_USER;

@ApplicationScoped
public class ProcessorService {

    private static final String CAM_REPORT_PATH = "cam/%s_cam.%s";
    private static final Logger logger = Logger.getLogger(ProcessorService.class);
    private static final List<StageEnum> REPROCESS_STAGES = List.of(StageEnum.REPROCESS_COMPLETE,
            StageEnum.REPROCESS_TABLE_TAG, StageEnum.REPROCESS_COA, StageEnum.REPROCESS_NTA_BLOCK);
    private final DocumentService documentService;
    private final ExchangeService exchangeService;
    private final CloudProvider cloudProvider;
    private final MailingService mailingService;
    private final JobExecutionMonitoringService jobExecutionMonitoringService;
    @ConfigProperty(name = ConfigKeys.ENV_NAME_KEY)
    String envName;
    @ConfigProperty(name = Config.WISE_CLIENT_SECRET_KEY)
    String clientSecret;
    @ConfigProperty(name = Config.API_CLIENT_NAME)
    String clientName;
    @ConfigProperty(name = Config.FRONTEND_HOST_URL)
    String frontendHostUrl;
    @ConfigProperty(name = Config.ROTATION_RETRY_COUNT)
    int rotationRetryCount;
    @ConfigProperty(name = Config.OCR_RETRY_COUNT)
    int ocrRetryCount;
    @ConfigProperty(name = Config.DOC_AI_RETRY_COUNT)
    int docAiRetryCount;
    @ConfigProperty(name = Config.FS_CLF_RETRY_COUNT)
    int fsClfRetryCount;
    @ConfigProperty(name = Config.PROCESSOR_RETRY_COUNT)
    int processorRetryCount;
    @ConfigProperty(name = Config.LEARNING_RETRY_COUNT)
    int learningRetryCount;
    @ConfigProperty(name = Config.REPROCESS_RETRY_COUNT)
    int reprocessRetryCount;

    public ProcessorService(DocumentService documentService, ExchangeService exchangeService,
                            CloudProviderFactory cloudProviderFactory,
                            MailingService mailingService,
                            JobExecutionMonitoringService jobExecutionMonitoringService) {
        this.documentService = documentService;
        this.exchangeService = exchangeService;
        this.cloudProvider = cloudProviderFactory.getCloudProvider();

        this.mailingService = mailingService;
        this.jobExecutionMonitoringService = jobExecutionMonitoringService;
    }

    public Response startProcess(UUID docId, StageEnum processStage, URI baseUri,
                                 boolean isApiKeyAuthenticated) {
        DocOutputDto doc;
        Document document = documentService.getFromDb(docId);

        if (document == null) {
            logger.errorf("Document not found for docId: %s", docId);
            Response.ResponseBuilder responseBuilder = Response.status(Response.Status.NOT_FOUND).entity(
                    "Document not found for docId: " + docId);
            return responseBuilder.build();
        }

        boolean isReprocess = REPROCESS_STAGES.contains(processStage);
        StatusEnum status = document.getStatus();

        if (!(isReprocess
                ? status.equals(StatusEnum.UNDER_REVIEW_LVL_1)
                : DocumentService.VALID_STATUS_FOR_PROCESSING.contains(status))
        ) {
            Response.ResponseBuilder responseBuilder = Response.status(Response.Status.BAD_REQUEST).entity(
                    "Document is in " + document.getStatus() + " state. Only documents in " +
                            DocumentService.VALID_STATUS_FOR_PROCESSING + " can be processed and documents in " +
                            "UNDER_REVIEW_LVL_1 can be reprocessed.");
            return responseBuilder.build();
        }

        if (processStage.equals(StageEnum.LEARNING) || processStage.equals(StageEnum.PROCESS_CAM_REPORT)) {
            doc = documentService.docToOutput(document);
        } else {
            doc = documentService.updateDocStatus(docId, StatusEnum.PROCESSING, processStage.toString());
        }

        if (doc.spreadId() == null || doc.spreadId() == 0) {
            logger.errorf("Spread id is null or 0 for docId: %s", docId);
            Response.ResponseBuilder responseBuilder = Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(
                    "Spread id is null or 0 for docId: " + docId + ". Please create a spread before processing the " +
                            "document.");
            return responseBuilder.build();
        }

        StageEnum cbStage = processStage.getNextStage();
        WiseWorkflowService wiseWorkflowService = getWiseWorkflowService(baseUri, doc, cbStage, processStage);
        wiseWorkflowService.startProcess(processStage);
        exchangeService.createDocAudit(
                List.of(new DocumentAuditDto.Create(docId, "PROCESSING", processStage.toString())),
                isApiKeyAuthenticated);
        return Response.ok().build();
    }

    public Response processBlock(UUID docId, URI baseUri, Integer blockId) throws InterruptedException {
        StageEnum processStage = StageEnum.REPROCESS_NTA_BLOCK;
        StageEnum cbStage = processStage.getNextStage();
        Document doc = documentService.getFromDb(docId);
        if (!doc.getStatus().equals(StatusEnum.UNDER_REVIEW_LVL_1)) {
            Response.ResponseBuilder responseBuilder = Response.status(Response.Status.BAD_REQUEST).entity(
                    "Document is in " + doc.getStatus() + " state. Only documents in UNDER_REVIEW_LVL_1 state can " +
                            "reprocess block"
            );
            return responseBuilder.build();
        }
        DocOutputDto docOutputDto = documentService.docToOutputForApi(doc);
        if (docOutputDto == null) {
            logger.errorf("Document not found for docId: %s", docId);
            Response.ResponseBuilder responseBuilder = Response.status(Response.Status.NOT_FOUND).entity(
                    "Document not found for docId: " + docId + ". Please verify the document id for the block.");
            return responseBuilder.build();
        }
        if (docOutputDto.spreadId() == null || docOutputDto.spreadId() == 0) {
            logger.errorf("Spread id is null or 0 for docId: %s", docId);
            Response.ResponseBuilder responseBuilder = Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(
                    "Spread id is null or 0 for docId: " + docId + ". Please create a spread before processing the " +
                            "document.");
            return responseBuilder.build();
        }

        if (!exchangeService.getBlockIdsForDoc(docId).contains(blockId)) {
            logger.errorf("Block id %s not found for docId: %s", blockId, docId);
            Response.ResponseBuilder responseBuilder = Response.status(Response.Status.NOT_FOUND).entity(
                    "Block id " + blockId + " not found for docId: " + docId + ". Please verify the block id.");
            return responseBuilder.build();
        }
        WiseWorkflowService wiseWorkflowService = getWiseWorkflowService(baseUri, docOutputDto, cbStage,
                processStage);
        exchangeService.createDocAudit(
                List.of(new DocumentAuditDto.Create(docId, "PROCESSING_BLOCK", blockId.toString())), false);
        wiseWorkflowService.startReprocessBlock(blockId);

        return Response.ok().build();
    }

    private WiseWorkflowService getWiseWorkflowService(URI baseUri, DocOutputDto doc, StageEnum cbStage,
                                                       StageEnum errorStage) {
        String bucketName = cloudProvider.getBucketName();

        String startJobMonitoringCallbackUrl = baseUri.toString() + JOB_MONITORING_ENDPOINT + "/" + "start-job";
        logger.debugf("Starting job monitoring callback url: %s", startJobMonitoringCallbackUrl);

        String endJobMonitoringCallbackUrl = baseUri + JOB_MONITORING_ENDPOINT + "/" + "end-job";
        logger.debugf("End job monitoring callback url: %s", endJobMonitoringCallbackUrl);

        WiseCallbackDto.CbMetadata cbMetadata = new WiseCallbackDto.CbMetadata(doc.docId(),
                doc.createdTime().toLocalDate(), bucketName, Objects.equals(doc.documentType(), "Digital"), cbStage, 0,
                StringUtils.EMPTY, errorStage);

        ApiKeyCredentialsDto credentialsDto = new ApiKeyCredentialsDto(this.clientName, this.clientSecret);
        return new WiseWorkflowService(cbMetadata, credentialsDto, baseUri, cloudProvider, this.envName, doc);
    }

    public void handleFinishProcess(UUID docId) {

        List<JobExecutionMonitoring> jobs = jobExecutionMonitoringService.findByDocId(docId);
        Document doc = documentService.getFromDb(docId);

        String createdBy = doc.getAuditable().getCreatedBy();
        documentService.updateInDbByProcessor(docId, new UpdateDocDto(null, null, null, StatusEnum.UNDER_REVIEW_LVL_1),
                WALNUT_AI_USER);
        String email = exchangeService.getEmailForUser(createdBy);
        if (StringUtils.isEmpty(email)) {
            logger.errorf("Failed to send job summary email for docId: %s, no email found for user: %s",
                    docId, createdBy);
        } else {
            logger.debugf("Sending job summary email for docId: %s to %s with jobs %s", docId, email, jobs);
            mailingService.sendJobSummaryEmail(jobs, email, getDocUrl(doc), doc);
        }
    }

    public void handleFinishReprocess(UUID docId) {

        Document doc = documentService.getFromDb(docId);
        documentService.updateInDbByProcessor(docId, new UpdateDocDto(null, null, null, StatusEnum.UNDER_REVIEW_LVL_1),
                WALNUT_AI_USER);
        String currentReviewer = Utils.getCurrentReviewer(doc);
        if (StringUtils.isEmpty(currentReviewer)) {
            logger.errorf("Failed to send reprocess complete email for docId: %s, no current reviewer found",
                    docId);
            return;
        }
        String email = exchangeService.getEmailForUser(currentReviewer);
        if (StringUtils.isEmpty(email)) {
            logger.errorf("Failed to send reprocess complete email for docId: %s, no email found for user: %s",
                    docId, currentReviewer);
        } else {
            logger.debugf("Sending reprocess complete email for docId: %s to %s", docId, email);
            mailingService.sendReprocessCompleteEmail(doc, email, getDocUrl(doc));
        }
    }

    public String getDocUrl(Document doc) {
        return frontendHostUrl + "/review/" + doc.getSpreadingTask().getSpreadId() + "/doc/" + doc.getDocId();
    }

    public void handleFailedDocProcessing(UUID docId) {
        List<JobExecutionMonitoring> jobs = jobExecutionMonitoringService.findByDocId(docId);
        Document doc = documentService.getFromDb(docId);

        String createdBy = doc.getAuditable().getCreatedBy();
        String email = exchangeService.getEmailForUser(createdBy);
        if (StringUtils.isEmpty(email)) {
            logger.errorf("Failed to send job summary email for docId: %s, no email found for user: %s",
                    docId, createdBy);
        } else {
            logger.debugf("Sending failed doc processing email for docId: %s to %s", docId, email);
            mailingService.sendFailedDocProcessingEmail(jobs, email, getDocUrl(doc), doc);
        }
    }

    public boolean isMaxRetry(int retryCount, StageEnum stage) {
        int maxRetries = switch (stage) {
            case ROTATION -> rotationRetryCount;
            case OCR -> ocrRetryCount;
            case DOC_AI -> docAiRetryCount;
            case FS_CLF -> fsClfRetryCount;
            case PROCESS -> processorRetryCount;
            case LEARNING -> learningRetryCount;
            case REPROCESS_COMPLETE, REPROCESS_TABLE_TAG, REPROCESS_COA, REPROCESS_NTA_BLOCK -> reprocessRetryCount;
            default -> 0;
        };
        return retryCount >= maxRetries;
    }

    public String uploadCamReport(UUID docId, FileUpload file) throws IOException {
        if (documentService.getFromDb(docId) == null) {
            ResponseException.throwResponseException(Response.Status.NOT_FOUND, "Document " + docId + " not found");
        }
        if (!CamReportFormat.getMimeTypes().contains(file.contentType().toLowerCase())) {
            ResponseException.throwResponseException(Response.Status.BAD_REQUEST,
                    "Invalid file type. Supported file types are " + CamReportFormat.getExtensions());
        }

        String reportPath = getCamReportPath(docId, CamReportFormat.getExtensionForMimeType(file.contentType()));
        cloudProvider.upload(reportPath, file.uploadedFile());
        return reportPath;
    }

    public UrlDto getCamReportLink(UUID docId, CamReportFormat format) {
        String reportPath = getCamReportPath(docId, format.getExtension());
        boolean reportExists = cloudProvider.objectExists(
                new FileLocationDto(cloudProvider.getBucketName(), reportPath));
        if (!reportExists) {
            ResponseException.throwResponseException(Response.Status.NOT_FOUND, "Cam report not found");
        }
        return new UrlDto(cloudProvider.getLink(reportPath, 60 * 24 * 7));
    }

    private String getCamReportPath(UUID docId, String extension) {
        return String.format(CAM_REPORT_PATH, docId, extension);
    }
}
