package com.walnut.vegaspread.workflow.service;

import com.walnut.vegaspread.common.model.workflow.StatusEnum;
import com.walnut.vegaspread.workflow.cloud.CloudProvider;
import com.walnut.vegaspread.workflow.cloud.CloudProviderFactory;
import com.walnut.vegaspread.workflow.entity.Document;
import com.walnut.vegaspread.workflow.entity.JobExecutionMonitoring;
import com.walnut.vegaspread.workflow.model.StageEnum;
import com.walnut.vegaspread.workflow.utils.Config;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.logging.Logger;

import java.net.URI;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

@ApplicationScoped
public class CleanupService {

    private static final Logger logger = Logger.getLogger(CleanupService.class);

    private final CloudProvider cloudProvider;
    private final JobExecutionMonitoringService jobExecutionMonitoringService;
    private final DocumentService documentService;
    private final ProcessorService processorService;

    @ConfigProperty(name = Config.MINUTES_BEFORE_CLEANUP)
    int minutesBeforeCleanup;

    public CleanupService(JobExecutionMonitoringService jobExecutionMonitoringService,
                          DocumentService documentService,
                          ProcessorService processorService,
                          CloudProviderFactory cloudProviderFactory) {
        this.jobExecutionMonitoringService = jobExecutionMonitoringService;
        this.documentService = documentService;
        this.processorService = processorService;
        this.cloudProvider = cloudProviderFactory.getCloudProvider();
    }

    public void cleanupIncompleteJobs() {
        List<Document> docs = documentService.findByStatusAndDurationSinceLastModified(
                StatusEnum.PROCESSING, minutesBeforeCleanup);

        if (docs.isEmpty()) {
            logger.debug("No docs found in processing state for cleanup");
            return;
        }

        for (Document doc : docs) {
            try {
                StageEnum stage = resolveStage(doc);
                RetryContext context = handleJobExecutionState(doc, stage);
                retryOrFail(context);
            } catch (Exception e) {
                logger.errorf("Error in cleanup for docId: %s, error: %s", doc.getDocId(), e.getMessage());
                logger.error(e);
            }
        }
    }

    private StageEnum resolveStage(Document doc) {
        try {
            return StageEnum.valueOf(doc.getStatusText());
        } catch (IllegalArgumentException e) {
            logger.warnf("Invalid status text %s for docId: %s during cleanup, setting stage to ROTATION",
                    doc.getStatusText(), doc.getDocId());
            return StageEnum.ROTATION;
        }
    }

    @Transactional
    public RetryContext handleJobExecutionState(Document doc, StageEnum stage) {
        List<JobExecutionMonitoring> jobs = jobExecutionMonitoringService.findByDocIdAndStage(doc.getDocId(), stage);

        if (jobs.isEmpty()) {
            return handleNoJobsFound(doc, stage);
        } else {
            return handleExistingJobs(doc, stage, jobs);
        }
    }

    private RetryContext handleNoJobsFound(Document doc, StageEnum stage) {
        createAndSaveFailedJob(doc, stage);
        logger.debugf("Created missing job monitoring record for docId: %s in stage: %s", doc.getDocId(), stage);
        return new RetryContext(doc, stage, 0);
    }

    private RetryContext handleExistingJobs(Document doc, StageEnum stage, List<JobExecutionMonitoring> jobs) {
        JobExecutionMonitoring latest = jobs.stream()
                .max(Comparator.comparing(JobExecutionMonitoring::getStartTime))
                .orElseThrow();

        if (latest.getEndTime() == null) {
            logger.debugf("Found incomplete job for docId: %s in stage: %s", doc.getDocId(), stage);
            latest.setEndTime(LocalDateTime.now());
            latest.setIsSuccess(false);
            jobExecutionMonitoringService.saveJobs(List.of(latest));
            return new RetryContext(doc, stage, jobs.size() - 1);
        } else {
            createAndSaveFailedJob(doc, stage);
            return new RetryContext(doc, stage, jobs.size());
        }
    }

    private void createAndSaveFailedJob(Document doc, StageEnum stage) {
        LocalDateTime now = LocalDateTime.now();

        JobExecutionMonitoring failedJob = JobExecutionMonitoring.builder()
                .jobId(String.format("job-%s-%s-%s", doc.getDocId(), stage, now))
                .docId(doc.getDocId())
                .stage(stage)
                .startTime(doc.getLastModifiedTime())
                .endTime(now)
                .isSuccess(false)
                .build();
        jobExecutionMonitoringService.saveJobs(List.of(failedJob));
    }

    private void retryOrFail(RetryContext context) {
        Document doc = context.doc();
        StageEnum stage = context.stage();
        int attempts = context.attemptCount();

        if (processorService.isMaxRetry(attempts, stage)) {
            logger.errorf("Max retries reached for doc %s at stage %s", doc.getDocId(), stage);
            documentService.updateDocStatus(doc.getDocId(), StatusEnum.FAILED, "FAILED_" + stage);
            processorService.handleFailedDocProcessing(doc.getDocId());
        } else {
            logger.infof("Retrying stage %s for docId: %s", stage, doc.getDocId());
            processorService.startProcess(doc.getDocId(), stage, cloudProvider.buildServiceBaseUrl(), true);
        }
    }

    public void retryFailedDocs(int count, Optional<StageEnum> optStage) {

        if (count <= 0) {
            throw new IllegalArgumentException("Count must be greater than 0");
        }

        List<Document> failedDocs = documentService.getFailedDocs(count);
        if (failedDocs.isEmpty()) {
            logger.debug("No failed docs found for retry");
            return;
        }

        List<UUID> failedDocIds = failedDocs.stream().map(Document::getDocId).toList();
        Map<UUID, StageEnum> docIdToStageMap;

        if (optStage.isPresent()) {
            StageEnum stage = optStage.get();
            docIdToStageMap = failedDocIds.stream().collect(Collectors.toMap(Function.identity(), docId -> stage));
        } else {
            docIdToStageMap = jobExecutionMonitoringService.findByDocIds(failedDocIds).stream()
                    .collect(Collectors.toMap(
                            JobExecutionMonitoring::getDocId,
                            Function.identity(),
                            (job1, job2) -> job1.getStartTime().isAfter(job2.getStartTime()) ? job1 : job2
                    ))
                    .entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> entry.getValue().getStage()
                    ));
        }

        URI serviceUrl = cloudProvider.buildServiceBaseUrl();
        for (Document failedDoc : failedDocs) {
            logger.debugf("Retrying failed doc %s at stage %s", failedDoc.getDocId(),
                    docIdToStageMap.get(failedDoc.getDocId()));
            UUID failedDocId = failedDoc.getDocId();
            StageEnum stage = docIdToStageMap.get(failedDocId);
            processorService.startProcess(failedDocId, stage, serviceUrl, true);
        }
    }

    private record RetryContext(Document doc, StageEnum stage, int attemptCount) {
    }
}
