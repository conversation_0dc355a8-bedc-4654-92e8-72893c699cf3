package com.walnut.vegaspread.workflow.service;

import com.walnut.vegaspread.workflow.entity.IdentifierKeyword;
import com.walnut.vegaspread.workflow.repository.IdentifierKeywordRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;

import java.util.List;

@ApplicationScoped
public class KwService {
    private final IdentifierKeywordRepository identifierKeywordRepository;

    public KwService(IdentifierKeywordRepository identifierKeywordRepository) {
        this.identifierKeywordRepository = identifierKeywordRepository;
    }

    public List<IdentifierKeyword> list(String category) {
        return identifierKeywordRepository.listForCategory(category);
    }

    @Transactional
    public void saveKeywords(List<IdentifierKeyword> keywords) {
        identifierKeywordRepository.persist(keywords);
    }
}
