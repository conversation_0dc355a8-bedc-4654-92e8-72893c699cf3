package com.walnut.vegaspread.workflow.service;

import com.walnut.vegaspread.common.model.workflow.StatusEnum;
import com.walnut.vegaspread.workflow.client.EzeeCallbackClient;
import com.walnut.vegaspread.workflow.model.EzeeCallbackDto;
import com.walnut.vegaspread.workflow.utils.Config;
import jakarta.enterprise.context.ApplicationScoped;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import java.util.UUID;

/**
 * Service for handling EZEE callbacks on status changes
 */
@ApplicationScoped
public class EzeeCallbackService {

    private static final Logger logger = Logger.getLogger(EzeeCallbackService.class);

    @RestClient
    EzeeCallbackClient ezeeCallbackClient;

    @ConfigProperty(name = Config.EZEE_CALLBACK_ENABLED, defaultValue = "false")
    boolean callbackEnabled;

    /**
     * Sends a callback to EZEE when document status changes
     *
     * @param docId         The document ID
     * @param applicationId The application ID from the document
     * @param status        The new status
     */
    public void sendStatusChangeCallback(UUID docId, String applicationId, StatusEnum status) {
        if (!callbackEnabled) {
            logger.debugf("EZEE callback is disabled, skipping callback for docId: %s", docId);
            return;
        }

        if (applicationId == null || applicationId.trim().isEmpty()) {
            logger.warnf("Application ID is null or empty for docId: %s, skipping EZEE callback", docId);
            return;
        }

        try {
            EzeeCallbackDto callbackData = new EzeeCallbackDto(applicationId, status.toString());

            logger.infof("Sending EZEE callback for docId: %s, applicationId: %s, status: %s",
                    docId, applicationId, status);

            ezeeCallbackClient.sendCallback(docId, callbackData);
        } catch (Exception e) {
            logger.errorf(e, "Error sending EZEE callback for docId: %s, applicationId: %s, status: %s",
                    docId, applicationId, status);
        }
    }
}
