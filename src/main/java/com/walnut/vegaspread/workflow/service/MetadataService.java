package com.walnut.vegaspread.workflow.service;

import com.walnut.vegaspread.common.model.extraction.DenominationEnum;
import com.walnut.vegaspread.workflow.entity.Auditable;
import com.walnut.vegaspread.workflow.entity.EntityName;
import com.walnut.vegaspread.workflow.entity.Industry;
import com.walnut.vegaspread.workflow.entity.Region;
import com.walnut.vegaspread.workflow.model.MetadataDto;
import com.walnut.vegaspread.workflow.repository.EntityNameRepository;
import com.walnut.vegaspread.workflow.repository.IndustryRepository;
import com.walnut.vegaspread.workflow.repository.RegionRepository;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@ApplicationScoped
public class MetadataService {
    private final EntityNameRepository entityNameRepository;
    private final IndustryRepository industryRepository;
    private final RegionRepository regionRepository;
    private final ExchangeService exchangeService;

    public MetadataService(EntityNameRepository entityNameRepository, IndustryRepository industryRepository,
                           RegionRepository regionRepository, ExchangeService exchangeService) {
        this.entityNameRepository = entityNameRepository;
        this.industryRepository = industryRepository;
        this.regionRepository = regionRepository;
        this.exchangeService = exchangeService;
    }

    @Transactional
    public EntityName getOrCreateEntity(MetadataDto.Entity entityDto, String username) {
        if (entityDto.entityId() != null) {
            return entityNameRepository.findById(entityDto.entityId());
        }
        Optional<EntityName> existingEntity = entityNameRepository.findByName(entityDto.name());
        if (existingEntity.isPresent()) {
            return existingEntity.get();
        }
        EntityName newEntity = EntityName.builder()
                .name(entityDto.name())
                .auditable(new Auditable(username))
                .build();
        entityNameRepository.persist(newEntity);
        return newEntity;
    }

    @Transactional
    public Industry getOrCreateIndustry(MetadataDto.Industry industryDto, String username) {
        if (industryDto.industryId() != null) {
            return industryRepository.findById(industryDto.industryId());
        }
        Optional<Industry> existingIndustry = industryRepository.findByIndustryName(industryDto.industryName());
        if (existingIndustry.isPresent()) {
            return existingIndustry.get();
        }
        Industry newIndustry = Industry.builder()
                .industryName(industryDto.industryName())
                .auditable(new Auditable(username))
                .build();
        industryRepository.persist(newIndustry);
        return newIndustry;
    }

    @Transactional
    public Region getOrCreateRegion(MetadataDto.Region region, String username) {
        if (region.regionId() != null) {
            return regionRepository.findById(region.regionId());
        }
        Optional<Region> existingRegion = regionRepository.findByRegionName(region.regionName());
        if (existingRegion.isPresent()) {
            return existingRegion.get();
        }
        Region newRegion = Region.builder()
                .regionName(region.regionName())
                .auditable(new Auditable(username))
                .build();
        regionRepository.persist(newRegion);
        return newRegion;
    }

    public List<EntityName> listEntities() {
        List<EntityName> entityNames = entityNameRepository.listAll(Sort.ascending("name"));
        Map<String, String> usernameToNameMapping = exchangeService.getUsernameMapping(
                entityNames.stream().map(entityName -> entityName.getAuditable().getCreatedBy()));
        entityNames.forEach(entityName -> entityName.getAuditable().setCreatedByFullName(usernameToNameMapping.get(
                entityName.getAuditable().getCreatedBy())));
        return entityNames;
    }

    public List<Industry> listIndustries() {
        List<Industry> industries = industryRepository.listAll(Sort.ascending("industryName"));
        Map<String, String> usernameToNameMapping = exchangeService.getUsernameMapping(
                industries.stream().map(industry -> industry.getAuditable().getCreatedBy()));
        industries.forEach(industry -> industry.getAuditable().setCreatedByFullName(usernameToNameMapping.get(
                industry.getAuditable().getCreatedBy())));
        return industries;
    }

    public List<Region> listRegions() {
        List<Region> regions = regionRepository.listAll(Sort.ascending("regionName"));
        Map<String, String> usernameToNameMapping = exchangeService.getUsernameMapping(
                regions.stream().map(region -> region.getAuditable().getCreatedBy()));
        regions.forEach(region -> region.getAuditable()
                .setCreatedByFullName(usernameToNameMapping.get(region.getAuditable().getCreatedBy())));
        return regions;
    }

    public List<DenominationEnum> listDenominations() {
        return List.of(DenominationEnum.values());
    }
}
