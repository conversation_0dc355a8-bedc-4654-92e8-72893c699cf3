package com.walnut.vegaspread.workflow.model;

import com.walnut.vegaspread.common.model.extraction.DenominationEnum;

import java.time.LocalDate;

public record UpdateUiDocDto(LocalDate period, SpreadLevelEnum spreadLevel, Boolean isDigital,
                             Boolean isOutlier, String outlierComment, DenominationEnum fileDenomination,
                             DenominationEnum outputDenomination, MetadataDto.Entity entityName,
                             MetadataDto.Industry industry, MetadataDto.Region region) {
}
