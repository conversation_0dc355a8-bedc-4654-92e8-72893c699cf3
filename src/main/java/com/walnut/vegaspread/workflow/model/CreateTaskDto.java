package com.walnut.vegaspread.workflow.model;

import com.walnut.vegaspread.common.model.extraction.DenominationEnum;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;
import java.util.UUID;

public record CreateTaskDto(@NotNull LocalDate period, @NotNull SpreadLevelEnum spreadLevel,
                            @NotNull MetadataDto.Entity entityName, @NotNull MetadataDto.Industry industry,
                            @NotNull MetadataDto.Region region, @NotNull Boolean isDigital, @NotNull UUID docId,
                            @NotNull DenominationEnum fileDenomination, @NotNull DenominationEnum outputDenomination) {
}
