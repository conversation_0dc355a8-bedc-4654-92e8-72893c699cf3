package com.walnut.vegaspread.workflow.model;

import java.util.List;

public interface GcpCloudRunDto {
    record OverrideEnvVar(String name, String value) {
    }

    record ExecuteContainerOverride(String name, List<String> args, List<OverrideEnvVar> env) {
    }

    record ExecuteOverride(ExecuteContainerOverride containerOverrides) {
    }

    record ExecuteRequestBody(Boolean validateOnly, String etag, ExecuteOverride overrides) {
    }

    record ExecuteMetadata(String name) {
    }

    record ExecuteOperation(String name, ExecuteMetadata metadata) {
    }

    record ExecutionStatus(String name, Integer succeededCount, Integer failedCount) {
    }
}
