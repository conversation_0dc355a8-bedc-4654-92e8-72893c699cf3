package com.walnut.vegaspread.workflow.model;

import com.walnut.vegaspread.common.model.extraction.DenominationEnum;
import com.walnut.vegaspread.common.model.workflow.StatusEnum;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

public record DocOutputDto(Integer spreadId, LocalDate period, SpreadLevelEnum spreadLevel, Integer dpi,
                           Integer extractedItems, Integer mappedItems, StatusEnum status, String statusText,
                           String entityName, Integer entityId, String industryName, Integer industryId,
                           String regionName, Integer regionId, UUID docId, String docFileName, Integer docFileSize,
                           Boolean isOutlier, String outlierComment,
                           String createdBy, String createdByFullName, String documentType, Integer ocrScore,
                           LocalDateTime createdTime, String lastModifiedBy, String lastModifiedByFullName,
                           LocalDateTime lastModifiedTime, String clientName, String currentReviewer,
                           String currentReviewerFullName, DenominationEnum fileDenomination,
                           DenominationEnum outputDenomination, Boolean isRevised, LocalDateTime lastProcessingTime,
                           String applicationId) {
}
