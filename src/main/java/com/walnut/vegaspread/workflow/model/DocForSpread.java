package com.walnut.vegaspread.workflow.model;

import com.walnut.vegaspread.common.model.extraction.DenominationEnum;
import com.walnut.vegaspread.common.model.workflow.StatusEnum;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

public record DocForSpread(LocalDate period, SpreadLevelEnum spreadLevel, Integer dpi, Integer extractedItems,
                           Integer mappedItems, StatusEnum status, String statusText, UUID docId, String docFileName,
                           Integer docFileSize, String createdBy, String createdByFullName, String documentType,
                           Integer ocrScore, LocalDateTime createdTime, String lastModifiedBy,
                           String lastModifiedByFullName, LocalDateTime lastModifiedTime, String currentReviewer,
                           String currentReviewerFullName, DenominationEnum fileDenomination,
                           DenominationEnum outputDenomination, Boolean isRevised, LocalDateTime lastProcessingTime) {

}
