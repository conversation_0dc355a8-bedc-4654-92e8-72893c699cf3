package com.walnut.vegaspread.workflow.model;

import com.walnut.vegaspread.workflow.entity.JobExecutionMonitoring;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * DTO for {@link JobExecutionMonitoring}
 */
public interface JobExecutionDto {
    record Response(@Size(max = 255) String jobId, @NotNull UUID docId, @NotNull StageEnum stage,
                    @NotNull LocalDateTime startTime, LocalDateTime endTime,
                    Boolean isSuccess) implements Serializable {
    }

    record StartJob(@Size(max = 255) String jobId, @NotNull UUID docId,
                    @NotNull StageEnum stage,
                    @NotNull LocalDateTime startTime) implements Serializable {
    }

    record EndJob(@Size(max = 255) String jobId, @NotNull LocalDateTime endTime,
                  @NotNull Boolean isSuccess) implements Serializable {
    }

    record ListResponse(int pageNumber, int pageSize, int totalPages, long totalJobs,
                        List<Response> jobExecutionMonitorings) {
    }
}