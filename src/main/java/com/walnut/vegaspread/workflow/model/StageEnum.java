package com.walnut.vegaspread.workflow.model;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import jakarta.ws.rs.core.Response;
import org.jboss.logging.Logger;

public enum StageEnum {
    ROTATION,
    OCR,
    DOC_AI,
    FS_CLF,
    PROCESS(ReprocessStage.COMPLETE),
    LEARNING(ReprocessStage.LEARNING),
    PROCESS_CAM_REPORT(ReprocessStage.PROCESS_CAM_REPORT),
    REPROCESS_COMPLETE(ReprocessStage.COMPLETE),
    REPROCESS_TABLE_TAG(ReprocessStage.TABLE_TAG),
    REPROCESS_COA(ReprocessStage.COA),
    REPROCESS_NTA_BLOCK(ReprocessStage.NTA_BLOCK),
    //Stages to denote completion of a processing stage and end of callback.
    FINISH_PROCESS,
    FINISH_REPROCESS,
    FINISH_LEARNING,
    //Stage to denote no callback being performed at the end of a processing stage.
    NO_CALLBACK;

    private static final Logger logger = Logger.getLogger(StageEnum.class);
    private final ReprocessStage reprocessStage;

    StageEnum() {
        this.reprocessStage = null;
    }

    StageEnum(ReprocessStage reprocessStage) {
        this.reprocessStage = reprocessStage;
    }

    public boolean isReprocess() {
        return reprocessStage != null;
    }

    public ReprocessStage getReprocessStage() {
        return reprocessStage;
    }

    public StageEnum getNextStage() {
        return switch (this) {
            case ROTATION -> StageEnum.OCR;
            case OCR -> StageEnum.DOC_AI;
            case DOC_AI -> StageEnum.FS_CLF;
            case FS_CLF -> StageEnum.PROCESS;
            case PROCESS -> StageEnum.FINISH_PROCESS;
            case LEARNING -> StageEnum.FINISH_LEARNING;
            case REPROCESS_COA, REPROCESS_COMPLETE, REPROCESS_TABLE_TAG -> StageEnum.FINISH_REPROCESS;
            case REPROCESS_NTA_BLOCK, PROCESS_CAM_REPORT -> StageEnum.NO_CALLBACK;
            default -> {
                ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR,
                        "Invalid process stage for next step: " + this);
                yield null;
            }
        };
    }

    public enum ReprocessStage {
        COMPLETE,
        TABLE_TAG,
        COA,
        NTA_BLOCK,
        LEARNING,
        PROCESS_CAM_REPORT
    }
}
