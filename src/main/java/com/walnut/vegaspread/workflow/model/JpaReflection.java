package com.walnut.vegaspread.workflow.model;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.UUID;

public interface JpaReflection {

    @Getter
    @AllArgsConstructor
    @RegisterForReflection
    class DocIdOnly {
        UUID docId;
    }

    @Getter
    @AllArgsConstructor
    @RegisterForReflection
    class SpreadIdOnly {
        Integer spreadId;
    }
}
