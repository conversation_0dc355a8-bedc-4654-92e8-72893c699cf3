package com.walnut.vegaspread.workflow.repository;

import com.walnut.vegaspread.workflow.entity.EntityName;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.Optional;

@ApplicationScoped
public class EntityNameRepository implements PanacheRepositoryBase<EntityName, Integer> {

    public Optional<EntityName> findByName(String name) {
        return find("name", name).firstResultOptional();
    }

}
