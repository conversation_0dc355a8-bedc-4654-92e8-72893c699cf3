package com.walnut.vegaspread.workflow.repository;

import com.walnut.vegaspread.workflow.entity.Review;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@ApplicationScoped
public class ReviewRepository implements PanacheRepositoryBase<Review, Integer> {

    public Review findMostRecentlyUpdated(UUID docId) {

        return find("document.id = :docId ORDER BY reviewedTime DESC, id DESC", Map.of("docId", docId)).firstResult();
    }

    public List<Review> getReviews(UUID docId) {
        return find("document.id = :docId ORDER BY reviewedTime DESC, id DESC", Map.of("docId", docId)).list();
    }
}
