package com.walnut.vegaspread.workflow.repository;

import com.walnut.vegaspread.workflow.entity.Region;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.Optional;

@ApplicationScoped
public class RegionRepository implements PanacheRepositoryBase<Region, Integer> {

    public Optional<Region> findByRegionName(String regionName) {
        return find("regionName", regionName).firstResultOptional();
    }

}
