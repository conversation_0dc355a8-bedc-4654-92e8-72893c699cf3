package com.walnut.vegaspread.workflow.repository;

import com.walnut.vegaspread.workflow.entity.Industry;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.Optional;

@ApplicationScoped
public class IndustryRepository implements PanacheRepositoryBase<Industry, Integer> {

    public Optional<Industry> findByIndustryName(String industryName) {
        return find("industryName", industryName).firstResultOptional();
    }

}
