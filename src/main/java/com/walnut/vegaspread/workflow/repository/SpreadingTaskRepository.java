package com.walnut.vegaspread.workflow.repository;

import com.walnut.vegaspread.workflow.entity.SpreadingTask;
import com.walnut.vegaspread.workflow.model.JpaReflection;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;
import java.util.Optional;

import static com.walnut.vegaspread.workflow.utils.Config.COMMON_CLIENT_NAME;

@ApplicationScoped
public class SpreadingTaskRepository implements PanacheRepositoryBase<SpreadingTask, Integer> {

    public List<Integer> filterTask(String filterColumn, Integer filterValue, String clientName) {
        List<String> clientList = List.of(COMMON_CLIENT_NAME,
                Optional.ofNullable(clientName).orElse(COMMON_CLIENT_NAME));
        if (filterColumn.equals("all")) {
            return find("clientName IN ?1", clientList).project(JpaReflection.SpreadIdOnly.class)
                    .stream()
                    .map(JpaReflection.SpreadIdOnly::getSpreadId)
                    .toList();
        }
        String query = String.format("%s = ?1 AND clientName IN ?2", filterColumn);
        return find(query, filterValue, clientList).project(JpaReflection.SpreadIdOnly.class).stream()
                .map(JpaReflection.SpreadIdOnly::getSpreadId)
                .toList();
    }

    public Optional<SpreadingTask> findByEntityNameAndIndustryNameAndRegionNameOptional(Integer entityId,
                                                                                        Integer industryId,
                                                                                        Integer regionId) {
        return find("entityName.entityId = ?1 AND industry.industryId = ?2 AND region.regionId = ?3", entityId,
                industryId, regionId).firstResultOptional();
    }
}
