package com.walnut.vegaspread.workflow.repository;

import java.util.List;

import com.walnut.vegaspread.workflow.entity.IdentifierKeyword;

import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

@ApplicationScoped
public class IdentifierKeywordRepository implements PanacheRepositoryBase<IdentifierKeyword,Integer> {

    public List<IdentifierKeyword> listForCategory(String category) {
        return list("category", category);
    }
}
