package com.walnut.vegaspread.workflow.client;

import com.walnut.vegaspread.workflow.model.GcpCloudRunDto;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/projects/vegaspread-7586a/locations/asia-southeast1")
@RegisterRestClient(baseUri = "https://run.googleapis.com/v2")
public interface GcpCloudRunClient {

    @POST
    @Path("/jobs/{job}:run")
    GcpCloudRunDto.ExecuteOperation executeJob(@PathParam("job") String job,
                                               GcpCloudRunDto.ExecuteRequestBody requestBody,
                                               @HeaderParam("Authorization") String token);

    @GET
    @Path("/jobs/{job}/executions/{execution}")
    GcpCloudRunDto.ExecutionStatus getExecution(@PathParam("job") String job,
                                                @PathParam("execution") String execution,
                                                @HeaderParam("Authorization") String token);
}
