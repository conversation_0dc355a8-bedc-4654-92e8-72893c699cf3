package com.walnut.vegaspread.workflow.client;

import com.walnut.vegaspread.common.clients.filter.RestClientLoggingFilter;
import com.walnut.vegaspread.workflow.model.EzeeCallbackDto;
import io.quarkus.runtime.annotations.RegisterForReflection;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.rest.client.annotation.RegisterClientHeaders;
import org.eclipse.microprofile.rest.client.annotation.RegisterProvider;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import java.util.UUID;

/**
 * REST client for EZEE callback API
 */
@RegisterForReflection
@RegisterRestClient(configKey = "ezee-callback")
@RegisterProvider(RestClientLoggingFilter.class)
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("")
@RegisterClientHeaders
public interface EzeeCallbackClient {

    @POST
    @Path("/walnut/workflow/callback/{docId}")
    Response sendCallback(@PathParam("docId") UUID docId, EzeeCallbackDto callbackData);
}
