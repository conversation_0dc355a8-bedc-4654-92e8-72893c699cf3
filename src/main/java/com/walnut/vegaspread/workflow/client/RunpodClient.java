package com.walnut.vegaspread.workflow.client;

import com.walnut.vegaspread.workflow.model.RunpodDto;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.rest.client.annotation.ClientHeaderParam;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jose4j.json.internal.json_simple.JSONObject;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/")
@RegisterRestClient(configKey = "runpod")
@ClientHeaderParam(name = "Authorization", value = "Bearer ${runpod.api-key}")
public interface RunpodClient {

    @POST
    @Path("/run")
    RunpodDto.StartJobRsp startJob(JSONObject payload);
}
