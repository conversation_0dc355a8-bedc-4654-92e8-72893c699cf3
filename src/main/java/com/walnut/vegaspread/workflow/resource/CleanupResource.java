package com.walnut.vegaspread.workflow.resource;

import com.walnut.vegaspread.common.security.ApiKeyAuthenticate;
import com.walnut.vegaspread.workflow.model.StageEnum;
import com.walnut.vegaspread.workflow.service.CleanupService;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;

import java.util.Optional;

@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Path("/cleanup")
@ApiKeyAuthenticate
public class CleanupResource {
    private final CleanupService cleanupService;

    public CleanupResource(CleanupService cleanupService) {
        this.cleanupService = cleanupService;
    }

    @POST
    @Path("/incomplete-jobs")
    public void cleanupIncompleteJobs() {
        cleanupService.cleanupIncompleteJobs();
    }

    /**
     * Retry failed docs.
     *
     * @param count    The number of documents to retry.
     * @param optStage If specified, retry from this stage else retry from the failed stage.
     */
    @POST
    @Path("/retry-failed-docs")
    public void retryFailedDocs(@QueryParam("count") int count, @QueryParam("stage") Optional<StageEnum> optStage) {
        cleanupService.retryFailedDocs(count, optStage);
    }
}
