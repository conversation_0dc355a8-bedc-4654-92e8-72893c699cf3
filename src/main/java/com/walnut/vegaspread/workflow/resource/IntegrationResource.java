package com.walnut.vegaspread.workflow.resource;

import com.walnut.vegaspread.common.security.ApiKeyAuthenticate;
import com.walnut.vegaspread.workflow.model.UploadItemSchema;
import com.walnut.vegaspread.workflow.service.IntegrationService;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.UriInfo;
import org.eclipse.microprofile.openapi.annotations.OpenAPIDefinition;
import org.eclipse.microprofile.openapi.annotations.info.Info;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.jboss.resteasy.reactive.RestForm;
import org.jboss.resteasy.reactive.multipart.FileUpload;

import java.io.IOException;
import java.net.URI;

@OpenAPIDefinition(info = @Info(title = "APIs for initiating processing via different channels", version = "1.0"))
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.MULTIPART_FORM_DATA)
@Path("/integration")
@ApiKeyAuthenticate
public class IntegrationResource {
    private final IntegrationService integrationService;

    public IntegrationResource(IntegrationService integrationService) {
        this.integrationService = integrationService;
    }

    @Path("/base64")
    @POST
    public void processBase64Doc(
            @RestForm("file") @Schema(implementation = UploadItemSchema.class) FileUpload fileUpload,
            @RestForm("folderName") String folderName, @Context UriInfo uriInfo) throws IOException {
        URI baseUri = uriInfo.getBaseUri();
        integrationService.processBase64Doc(fileUpload, folderName, baseUri);
    }
}
