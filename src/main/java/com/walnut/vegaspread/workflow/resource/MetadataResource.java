package com.walnut.vegaspread.workflow.resource;

import com.walnut.vegaspread.common.model.extraction.DenominationEnum;
import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.workflow.entity.EntityName;
import com.walnut.vegaspread.workflow.entity.Industry;
import com.walnut.vegaspread.workflow.entity.Region;
import com.walnut.vegaspread.workflow.service.MetadataService;
import jakarta.annotation.security.RolesAllowed;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Path("/metadata")
@RolesAllowed(Roles.UPLOAD_FILE)
public class MetadataResource {

    private final MetadataService metadataService;

    public MetadataResource(MetadataService metadataService) {
        this.metadataService = metadataService;
    }

    @Path("/entity")
    @GET
    public List<EntityName> listEntities() {
        return metadataService.listEntities();
    }

    @Path("/industry")
    @GET
    public List<Industry> listIndustries() {
        return metadataService.listIndustries();
    }

    @Path("/region")
    @GET
    public List<Region> listRegions() {
        return metadataService.listRegions();
    }

    @Path("/denomination")
    @GET
    public List<DenominationEnum> listDenominations() {
        return metadataService.listDenominations();
    }
}
