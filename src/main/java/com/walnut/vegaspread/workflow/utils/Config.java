package com.walnut.vegaspread.workflow.utils;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import jakarta.ws.rs.core.Response;

public class Config {
    public static final String WISE_CLIENT_SECRET_KEY = "wise.client.secret";
    public static final String VEGA_BUCKET_NAME = "vega.cloud.bucket-name";
    public static final String AWS_ECS_CPU_CLUSTER = "aws.ecs.cpu.cluster";
    public static final String AWS_ECS_GPU_CLUSTER = "aws.ecs.gpu.cluster";
    public static final String AWS_ECS_SUBNETS = "aws.ecs.subnets";
    public static final String AWS_ECS_SECURITY_GROUPS = "aws.ecs.cpu.security-groups";
    public static final String AWS_S3_BUCKET = "aws.s3.bucket";
    public static final String AWS_S3_VPC_ENDPOINT_HOSTNAME = "aws.s3.vpc-endpoint-hostname";
    public static final String AWS_SQS_QUEUE_URL = "aws.sqs.queue.url";
    public static final String ROOT_PATH_URL = "quarkus.http.root-path";
    public static final String ROTATION_TASK_FAMILY_NAME = "aws.ecs.task-family-name.rotation";
    public static final String OCR_TASK_FAMILY_NAME = "aws.ecs.task-family-name.ocr";
    public static final String DOC_AI_TASK_FAMILY_NAME = "aws.ecs.task-family-name.docai";
    public static final String FS_CLF_TASK_FAMILY_NAME = "aws.ecs.task-family-name.fs-clf";
    public static final String PROCESSOR_TASK_FAMILY_NAME = "aws.ecs.task-family-name.processor";
    public static final String COMPLETE_TASK_FAMILY_NAME = "aws.ecs.task-family-name.complete";
    public static final String CAM_REPORT_TASK_FAMILY_NAME = "aws.ecs.task-family-name.cam-report";

    public static final String DOC_AI_CAPACITY_PROVIDER_NAME = "aws.ecs.gpu.capacity-provider-name";

    public static final String API_CLIENT_NAME = "vega.api-client-name";

    public static final String CB_API_KEY_NAME = "CALLBACK_API_KEY";
    public static final String WISE_API_ENV_NAME = "WISE_API_KEY";
    public static final String GCP_HOST_URL = "vega.cloud.host-url";

    public static final String COMMON_CLIENT_NAME = "walnut";
    public static final String WALNUT_AI_USER = "developer";

    public static final String ROTATION_RETRY_COUNT = "vega.retry-count.rotation";
    public static final String OCR_RETRY_COUNT = "vega.retry-count.ocr";
    public static final String DOC_AI_RETRY_COUNT = "vega.retry-count.doc-ai";
    public static final String FS_CLF_RETRY_COUNT = "vega.retry-count.fs-clf";
    public static final String PROCESSOR_RETRY_COUNT = "vega.retry-count.processor";
    public static final String FRONTEND_HOST_URL = "vega.frontend-host-url";
    public static final String LEARNING_RETRY_COUNT = "vega.retry-count.learning";
    public static final String REPROCESS_RETRY_COUNT = "vega.retry-count.reprocess";
    public static final String MINUTES_BEFORE_CLEANUP = "vega.minutes-before-cleanup";
    public static final String JSON_UPLOAD_PATHS = "vega.json-upload-paths";

    public static final String CAM_REPORT_ENABLED = "vega.cam-report.enabled";

    private Config() {
        ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR, "Utility class");
    }
}
