package com.walnut.vegaspread.workflow.utils;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.workflow.entity.Document;
import com.walnut.vegaspread.workflow.model.JpaReflection;
import com.walnut.vegaspread.workflow.model.ListTaskDto;
import com.walnut.vegaspread.workflow.model.SpreadLevelEnum;
import com.walnut.vegaspread.workflow.repository.DocumentRepository;
import io.quarkus.hibernate.orm.panache.PanacheQuery;
import io.quarkus.panache.common.Page;
import io.quarkus.panache.common.Sort;
import jakarta.ws.rs.core.Response;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

public class QueryBuilder {

    private static final String DOC_QUERY = " AND doc.%s = :%s";
    private static final String LAST_MODIFIED_BY = "lastModifiedBy";
    private static final String LAST_MODIFIED_TIME = "lastModifiedTime";
    private static final Map<String, String> COL_MAP = Map.of("spreadId", "spreadingTask.spreadId",
            "entityName", "spreadingTask.entityName.name",
            "industryName", "spreadingTask.industry.industryName",
            "regionName", "spreadingTask.region.regionName",
            LAST_MODIFIED_BY, LAST_MODIFIED_BY,
            "createdBy", "auditable.createdBy",
            "documentType", "isDigital",
            LAST_MODIFIED_TIME, LAST_MODIFIED_TIME,
            "createdTime", "auditable.createdTime"
    );
    private static final List<String> SEARCH_COLS = List.of(COL_MAP.get("spreadId"), COL_MAP.get("entityName"),
            COL_MAP.get("industryName"), COL_MAP.get("regionName"), COL_MAP.get(LAST_MODIFIED_BY),
            COL_MAP.get("createdBy"));
    private static final String CURRENT_REVIEWER_FILTER_BY_NAME = "currentReviewer";
    private final ListTaskDto.GetTaskList taskList;
    private final DocumentRepository documentRepository;

    @Getter
    private final HashMap<String, Object> params = new HashMap<>();
    @Getter
    private String query;

    public QueryBuilder(ListTaskDto.GetTaskList taskList, DocumentRepository documentRepository) {
        this.taskList = taskList;
        this.documentRepository = documentRepository;
    }

    private Sort sort() {
        Sort finalSort;
        Map<String, Sort.Direction> sortOrderMap = Map.of("ASC", Sort.Direction.Ascending,
                "DESC", Sort.Direction.Descending);
        if (!taskList.sort().isEmpty()) {
            List<ListTaskDto.SortDto> sortDtos = taskList.sort();
            ListTaskDto.SortDto firstSort = sortDtos.get(0);
            finalSort = Sort.by(COL_MAP.get(firstSort.sortBy()), sortOrderMap.get(firstSort.sortType()));
            for (ListTaskDto.SortDto sortDto : sortDtos.stream().skip(1).toList()) {
                finalSort.and(sortDto.sortBy(), sortOrderMap.get(sortDto.sortType()));
            }
        } else {
            finalSort = Sort.by(LAST_MODIFIED_TIME, Sort.Direction.Descending);
        }
        return finalSort;
    }

    private void filter(ListTaskDto.FilterDto filterDto) {
        String filterBy = filterDto.filterBy();
        List<String> filters = new java.util.ArrayList<>(COL_MAP.keySet().stream().toList());
        filters.addAll(List.of("spreadLevel", "period"));
        if (!filters.contains(filterBy)) {
            ResponseException.throwResponseException(Response.Status.BAD_REQUEST,
                    "Invalid filter type " + filterBy);
        }
        String filterValue = filterDto.filterValue();
        switch (filterBy) {
            case "spreadLevel":
                query += String.format(DOC_QUERY, filterBy, filterBy);
                params.put(filterBy, SpreadLevelEnum.valueOf(filterValue));
                break;
            case "documentType":
                String isDigitalCol = COL_MAP.get(filterBy);
                query += String.format(DOC_QUERY, isDigitalCol, isDigitalCol);
                params.put(isDigitalCol, Objects.equals(filterValue, "Digital"));
                break;
            case LAST_MODIFIED_TIME, "createdTime":
                LocalDateTime startFilterTime = LocalDateTime.parse(filterValue);
                LocalDateTime endFilterTime = startFilterTime.plusDays(1);
                String startFilterName = filterBy + "Start";
                String endFilterName = filterBy + "End";
                query += String.format(" AND doc.%s BETWEEN :%s AND :%s", COL_MAP.get(filterBy), startFilterName,
                        endFilterName);
                params.put(startFilterName, startFilterTime);
                params.put(endFilterName, endFilterTime);
                break;
            case "period":
                query += String.format(" AND YEAR(doc.period) = :%s", filterBy);
                params.put(filterBy, filterValue);
                break;
            default:
                query += String.format(DOC_QUERY, COL_MAP.get(filterBy), filterBy);
                params.put(filterBy, filterValue);
        }
    }

    private void search() {
        String searchStr = taskList.search();
        try {
            UUID docId = UUID.fromString(searchStr);
            query += " AND doc.docId = :searchQuery";
            params.put("searchQuery", docId);
        } catch (IllegalArgumentException e) {
            query += String.format(" AND (CAST(doc.%s AS text) LIKE :searchQuery", SEARCH_COLS.get(0));
            SEARCH_COLS.stream()
                    .skip(1)
                    .forEach(col -> query += String.format(" OR CAST(doc.%s AS text) LIKE :searchQuery", col));
            query += " OR CAST(YEAR(doc.period) AS text) LIKE :searchQuery OR CAST(doc.spreadLevel AS text) LIKE " +
                    ":searchQuery)";
            params.put("searchQuery", String.format("%%%s%%", taskList.search()));
        }
    }

    public List<Document> getList(String clientName) {
        boolean isFilterByCurrentReviewer = false;
        StringBuilder currentReviewerFilterList = new StringBuilder(StringUtils.EMPTY);
        Sort finalSort = sort();

        query = "doc.status IN :status AND doc.spreadingTask.clientName = :clientName";
        params.put("status", taskList.status());
        params.put("clientName", clientName);

        for (ListTaskDto.FilterDto filterDto : taskList.filter()) {
            if (filterDto.filterBy().equals(CURRENT_REVIEWER_FILTER_BY_NAME)) {
                currentReviewerFilterList.append(filterDto.filterValue()).append(",");
                isFilterByCurrentReviewer = true;
            } else {
                filter(filterDto);
            }
        }
        if (!Objects.equals(taskList.search(), "")) {
            search();
        }

        if (isFilterByCurrentReviewer) {
            query = String.format(
                    "SELECT doc.docId FROM Document doc JOIN (SELECT r.document.docId as docId, r.reviewedTime as " +
                            "reviewedTime, r.reviewer as reviewer, r.id as id,  ROW_NUMBER() OVER (PARTITION BY r" +
                            ".document.docId ORDER BY r.reviewedTime DESC, r.id DESC) as rn FROM Review r ) as rt ON " +
                            "doc" +
                            ".docId = rt.docId AND rt.reviewer IN (:%s) AND CAST(rt.rn AS int) = 1 WHERE %s",
                    CURRENT_REVIEWER_FILTER_BY_NAME, query);
            params.put(CURRENT_REVIEWER_FILTER_BY_NAME,
                    StringUtils.removeEnd(currentReviewerFilterList.toString(), ","));
        } else {
            query = String.format("SELECT doc.docId FROM Document doc WHERE %s", query);
        }

        // Frontend sends pages starting from 1, not from 0
        PanacheQuery<JpaReflection.DocIdOnly> panacheQuery = documentRepository.find(query, finalSort, params)
                .project(JpaReflection.DocIdOnly.class)
                .page(Page.of(taskList.pageNumber() - 1, taskList.pageSize()));
        List<JpaReflection.DocIdOnly> docIds = panacheQuery.list();

        return documentRepository.findByIds(docIds.stream().map(JpaReflection.DocIdOnly::getDocId).toList(), finalSort);
    }
}
