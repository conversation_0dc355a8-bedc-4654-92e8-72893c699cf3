package com.walnut.vegaspread.workflow.utils;

import com.walnut.vegaspread.common.model.extraction.DenominationEnum;
import com.walnut.vegaspread.workflow.entity.Document;
import com.walnut.vegaspread.workflow.entity.EntityName;
import com.walnut.vegaspread.workflow.entity.Industry;
import com.walnut.vegaspread.workflow.entity.Region;
import com.walnut.vegaspread.workflow.entity.SpreadingTask;
import com.walnut.vegaspread.workflow.model.DocForSpread;
import com.walnut.vegaspread.workflow.model.DocOutputDto;
import com.walnut.vegaspread.workflow.model.SpreadOutputDto;
import com.walnut.vegaspread.workflow.service.ExchangeService;
import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.Logger;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.walnut.vegaspread.workflow.utils.Utils.getCurrentReviewer;
import static com.walnut.vegaspread.workflow.utils.Utils.getLastProcessingTime;
import static com.walnut.vegaspread.workflow.utils.Utils.isRevised;

public class OutputFormatter {
    private static final Logger logger = Logger.getLogger(OutputFormatter.class);
    private final ExchangeService exchangeService;

    public OutputFormatter(ExchangeService exchangeService) {
        this.exchangeService = exchangeService;
    }

    private DocOutputDto convertToOutput(Document doc, SpreadingTask task, Map<String, String> usernameToNameMappings) {
        if (task == null) {
            task = doc.getSpreadingTask();
            if (task == null) {
                logger.errorf("Task is null for document {}", doc.getDocId());
                task = SpreadingTask.builder()
                        .spreadId(0)
                        .entityName(EntityName.builder().name("NA").build())
                        .industry(Industry.builder().industryName("NA").build())
                        .region(Region.builder().regionName("NA").build())
                        .clientName("NA")
                        .build();
            }
        }
        String currentReviewer = getCurrentReviewer(doc);
        String currentReviewerName = StringUtils.EMPTY;
        if (currentReviewer != null) {
            currentReviewerName = usernameToNameMappings.getOrDefault(currentReviewer, StringUtils.EMPTY);
        }
        LocalDateTime lastProcessingTime = getLastProcessingTime(doc);
        String createdBy = doc.getAuditable().getCreatedBy();
        String lastModifiedBy = doc.getLastModifiedBy();
        return new DocOutputDto(task.getSpreadId(), doc.getPeriod(), doc.getSpreadLevel(),
                Integer.valueOf(Optional.ofNullable(doc.getDpi()).orElse((short) 0)), 0,
                Integer.valueOf(Optional.ofNullable(doc.getMappedItems()).orElse((short) 0)), doc.getStatus(),
                doc.getStatusText(), task.getEntityName().getName(), task.getEntityName().getEntityId(),
                task.getIndustry().getIndustryName(), task.getIndustry().getIndustryId(),
                task.getRegion().getRegionName(), task.getRegion().getRegionId(), doc.getDocId(),
                doc.getFileName(), doc.getFileSize(), doc.getIsOutlier(), doc.getOutlierComment(),
                createdBy, usernameToNameMappings.getOrDefault(createdBy, createdBy),
                Boolean.TRUE.equals(doc.getIsDigital()) ? "Digital" : "Scanned",
                Integer.valueOf(Optional.ofNullable(doc.getOcrScore()).orElse((byte) 100)),
                doc.getAuditable().getCreatedTime(), lastModifiedBy,
                usernameToNameMappings.getOrDefault(lastModifiedBy, lastModifiedBy), doc.getLastModifiedTime(),
                task.getClientName(), currentReviewer, currentReviewerName,
                Optional.ofNullable(doc.getFileDenomination()).orElse(DenominationEnum.NONE),
                Optional.ofNullable(doc.getOutputDenomination()).orElse(DenominationEnum.NONE), isRevised(doc),
                lastProcessingTime, doc.getApplicationId());
    }

    private SpreadOutputDto convertToOutput(List<Document> docs, SpreadingTask task,
                                            Map<String, String> usernameToNameMappings) {
        List<DocForSpread> docForSpreads = new ArrayList<>();
        for (Document doc : docs) {
            String currentReviewer = getCurrentReviewer(doc);
            String currentReviewerName = StringUtils.EMPTY;
            if (currentReviewer != null) {
                currentReviewerName = usernameToNameMappings.getOrDefault(currentReviewer, StringUtils.EMPTY);
            }
            String createdBy = doc.getAuditable().getCreatedBy();
            String lastModifiedBy = doc.getLastModifiedBy();
            LocalDateTime lastProcessingTime = getLastProcessingTime(doc);
            docForSpreads.add(new DocForSpread(doc.getPeriod(), doc.getSpreadLevel(),
                    Integer.valueOf(Optional.ofNullable(doc.getDpi()).orElse((short) 0)), 0,
                    Integer.valueOf(Optional.ofNullable(doc.getMappedItems()).orElse((short) 0)), doc.getStatus(),
                    doc.getStatusText(), doc.getDocId(), doc.getFileName(), doc.getFileSize(), createdBy,
                    usernameToNameMappings.getOrDefault(createdBy, createdBy),
                    Boolean.TRUE.equals(doc.getIsDigital()) ? "Digital" : "Scanned",
                    Integer.valueOf(Optional.ofNullable(doc.getOcrScore()).orElse((byte) 100)),
                    doc.getAuditable().getCreatedTime(), lastModifiedBy,
                    usernameToNameMappings.getOrDefault(lastModifiedBy, lastModifiedBy),
                    doc.getLastModifiedTime(), currentReviewer, currentReviewerName,
                    Optional.ofNullable(doc.getFileDenomination()).orElse(DenominationEnum.NONE),
                    Optional.ofNullable(doc.getOutputDenomination()).orElse(DenominationEnum.NONE), isRevised(doc),
                    lastProcessingTime));
        }
        return new SpreadOutputDto(task.getSpreadId(), task.getEntityName().getName(),
                task.getEntityName().getEntityId(), task.getIndustry().getIndustryName(),
                task.getIndustry().getIndustryId(), task.getRegion().getRegionName(),
                task.getRegion().getRegionId(), task.getClientName(), docForSpreads);
    }

    public List<DocOutputDto> taskToOutput(List<Document> docs) {
        Map<String, String> usernameToNameMappings = exchangeService.getUsernameMappingForDocs(docs);
        return docs.stream().map(doc -> convertToOutput(doc, null, usernameToNameMappings)).toList();
    }

    public DocOutputDto taskToOutput(Document doc) {
        Map<String, String> usernameToNameMappings = exchangeService.getUsernameMappingForDocs(List.of(doc));
        return convertToOutput(doc, null, usernameToNameMappings);
    }

    public DocOutputDto taskToOutput(Document doc, SpreadingTask task) {
        Map<String, String> usernameToNameMappings = exchangeService.getUsernameMappingForDocs(List.of(doc));
        return convertToOutput(doc, task, usernameToNameMappings);
    }

    public SpreadOutputDto taskToOutput(List<Document> docs, SpreadingTask task) {
        Map<String, String> usernameToNameMappings = exchangeService.getUsernameMappingForDocs(docs);
        return convertToOutput(docs, task, usernameToNameMappings);
    }

    public DocOutputDto taskToOutputForApi(Document doc) {
        return convertToOutput(doc, null, Map.of());
    }
}
