package com.walnut.vegaspread.workflow.utils;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.workflow.entity.Document;
import com.walnut.vegaspread.workflow.entity.Review;
import com.walnut.vegaspread.workflow.model.ReviewStatusEnum;
import jakarta.ws.rs.core.Response;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Comparator;
import java.util.List;

import static com.walnut.vegaspread.workflow.utils.Config.WALNUT_AI_USER;

public class Utils {

    private Utils() {
        ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR, "Utility class");
    }

    public static String getCurrentReviewer(Document doc) {
        return doc.getReviews().isEmpty() ? null : doc.getReviews().stream()
                .max(Comparator.comparing(Review::getReviewedTime))
                .map(Review::getReviewer)
                .orElse(null);
    }

    public static Boolean isRevised(Document doc) {
        return !doc.getReviews().isEmpty() && doc.getReviews().stream()
                .anyMatch(review -> List.of(ReviewStatusEnum.REVISED, ReviewStatusEnum.REVISED_APPROVED)
                        .contains(review.getStatus()));
    }

    public static String cleanFilenameString(String str) {
        return str.replaceAll("\\s+", "_").toLowerCase();
    }

    public static LocalDateTime getLastProcessingTime(Document doc) {
        return doc.getReviews().stream().filter(review -> review.getReviewer().equals(WALNUT_AI_USER))
                .map(Review::getReviewedTime)
                .max(Comparator.naturalOrder())
                .orElse(null);
    }

    public static LocalDateTime convertUtcToUtcPlus8(LocalDateTime utcTime) {
        ZonedDateTime utcZoned = utcTime.atZone(ZoneId.of("UTC"));
        ZonedDateTime utcPlus8Zoned = utcZoned.withZoneSameInstant(ZoneId.of("UTC+08:00"));
        return utcPlus8Zoned.toLocalDateTime();
    }
}
