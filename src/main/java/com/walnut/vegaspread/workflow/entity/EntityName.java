package com.walnut.vegaspread.workflow.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.FilterDef;
import org.hibernate.annotations.ParamDef;

@FilterDef(name = "entityName", defaultCondition = "name = :entityName",
        parameters = @ParamDef(name = "entityName", type = String.class))
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Entity
@Getter
@Table(name = "entity_name")
public class EntityName {

    public static final String ENTITY_ID_COL_NAME = "entity_id";
    public static final String ENTITY_NAME_COL_NAME = "name";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = ENTITY_ID_COL_NAME, nullable = false)
    private Integer entityId;

    @Column(name = ENTITY_NAME_COL_NAME, nullable = false, unique = true)
    private String name;

    @Embedded
    private Auditable auditable;
}
