package com.walnut.vegaspread.workflow.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.FilterDef;
import org.hibernate.annotations.ParamDef;

@FilterDef(name = "industryName", defaultCondition = "industryName = :industryName",
        parameters = @ParamDef(name = "industryName", type = String.class))
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Entity
@Getter
@Table(name = "industry")
public class Industry {

    public static final String INDUSTRY_ID_COL_NAME = "industry_id";
    public static final String INDUSTRY_NAME_COL_NAME = "industry_name";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = INDUSTRY_ID_COL_NAME, nullable = false)
    private Integer industryId;

    @Column(name = INDUSTRY_NAME_COL_NAME, nullable = false, unique = true)
    private String industryName;

    @Embedded
    private Auditable auditable;
}
