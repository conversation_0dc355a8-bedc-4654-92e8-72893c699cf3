package com.walnut.vegaspread.workflow.entity;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "spreading_task")
public class SpreadingTask {

    public static final String SPREAD_ID_COL_NAME = "spread_id";
    public static final String CLIENT_NAME_COL_NAME = "client_name";
    public static final String LAST_MODIFIED_BY_COL_NAME = "last_modified_by";
    public static final String LAST_MODIFIED_TIME_COL_NAME = "last_modified_time";
    public static final String ENTITY_NAME_FOREIGN_KEY_COL_NAME = "entity_name_id";
    public static final String INDUSTRY_FOREIGN_KEY_COL_NAME = "industry_id";
    public static final String REGION_FOREIGN_KEY_COL_NAME = "region_id";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = SPREAD_ID_COL_NAME, nullable = false)
    public Integer spreadId;

    @Column(name = CLIENT_NAME_COL_NAME, nullable = false)
    public String clientName;

    @ToString.Exclude
    @ManyToOne(fetch = FetchType.EAGER, optional = false)
    @JoinColumn(name = ENTITY_NAME_FOREIGN_KEY_COL_NAME, nullable = false)
    public EntityName entityName;

    @ToString.Exclude
    @ManyToOne(fetch = FetchType.EAGER, optional = false)
    @JoinColumn(name = INDUSTRY_FOREIGN_KEY_COL_NAME, nullable = false)
    public Industry industry;

    @ToString.Exclude
    @ManyToOne(fetch = FetchType.EAGER, optional = false)
    @JoinColumn(name = REGION_FOREIGN_KEY_COL_NAME, nullable = false)
    public Region region;

    @JsonUnwrapped
    @Embedded
    public Auditable auditable;

    @Column(name = LAST_MODIFIED_BY_COL_NAME, nullable = false)
    public String lastModifiedBy;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Column(name = LAST_MODIFIED_TIME_COL_NAME, nullable = false)
    public LocalDateTime lastModifiedTime;

    @ToString.Exclude
    @OneToMany(mappedBy = "spreadingTask", cascade = CascadeType.ALL, orphanRemoval = true,
            fetch = FetchType.EAGER)
    @OrderBy("auditable.createdTime DESC")
    @JsonManagedReference(value = "documents")
    @Builder.Default
    public List<Document> documents = new ArrayList<>();

}