package com.walnut.vegaspread.workflow.entity;

import com.walnut.vegaspread.workflow.model.JobExecutionDto;
import com.walnut.vegaspread.workflow.model.JobSummary;
import com.walnut.vegaspread.workflow.model.StageEnum;
import com.walnut.vegaspread.workflow.utils.Utils;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

@Getter
@Setter
@Entity
@Builder
@Table(name = JobExecutionMonitoring.TABLE_NAME)
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class JobExecutionMonitoring {
    public static final String TABLE_NAME = "job_execution_monitoring";
    public static final String JOB_ID_COL_NAME = "job_id";
    public static final String DOC_ID_COL_NAME = "doc_id";
    public static final String STAGE_COL_NAME = "stage";
    public static final String START_TIME_COL_NAME = "start_time";
    public static final String END_TIME_COL_NAME = "end_time";
    public static final String IS_SUCCESS_COL_NAME = "is_success";

    @Id
    @Size(max = 255)
    @Column(name = JOB_ID_COL_NAME, nullable = false)
    private String jobId;

    @NotNull
    @Column(name = DOC_ID_COL_NAME, nullable = false)
    private UUID docId;

    @Enumerated(EnumType.STRING)
    @Column(name = STAGE_COL_NAME, nullable = false)
    private StageEnum stage;

    @NotNull
    @Column(name = START_TIME_COL_NAME, nullable = false)
    private LocalDateTime startTime;

    @Column(name = END_TIME_COL_NAME)
    private LocalDateTime endTime;

    @Column(name = IS_SUCCESS_COL_NAME)
    private Boolean isSuccess;

    public static List<JobExecutionDto.Response> toDtoList(List<JobExecutionMonitoring> jobExecutionMonitorings) {
        return jobExecutionMonitorings.stream()
                .map(JobExecutionMonitoring::toDto)
                .toList();
    }

    public JobExecutionDto.Response toDto() {
        return new JobExecutionDto.Response(this.getJobId(), this.getDocId(), this.getStage(), this.getStartTime(),
                this.getEndTime(), this.getIsSuccess());
    }

    public JobSummary toJobSummary(DateTimeFormatter formatter) {
        Duration duration = Duration.between(this.getStartTime(), this.getEndTime());
        return new JobSummary(
                this.stage.toString(),
                Utils.convertUtcToUtcPlus8(this.startTime).format(formatter),
                Utils.convertUtcToUtcPlus8(this.endTime).format(formatter),
                duration,
                duration.toMinutes(),
                duration.toSecondsPart(),
                this.isSuccess
        );
    }
}
