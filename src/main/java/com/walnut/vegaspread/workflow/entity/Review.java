package com.walnut.vegaspread.workflow.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.walnut.vegaspread.workflow.model.ReviewStatusEnum;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Builder
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = Review.TABLE_NAME)
public class Review {

    public static final String TABLE_NAME = "review";
    public static final String REVIEW_ID_COL_NAME = "id";
    public static final String STATUS_COL_NAME = "status";
    public static final String DOCUMENT_FOREIGN_KEY_COL_NAME = "doc_id";
    public static final String REVIEWER_COL_NAME = "reviewer";
    public static final String REVIEW_LEVEL_COL_NAME = "review_level";
    public static final String REVIEWED_TIME_COL_NAME = "reviewed_time";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = REVIEW_ID_COL_NAME, nullable = false)
    public Integer id;

    @ManyToOne
    @JoinColumn(name = DOCUMENT_FOREIGN_KEY_COL_NAME)
    @JsonBackReference
    public Document document;

    @Column(name = REVIEWER_COL_NAME, nullable = false)
    public String reviewer;

    @Transient
    public String reviewerFullName;

    @Column(name = REVIEW_LEVEL_COL_NAME, nullable = false)
    public Integer reviewLevel;

    @Enumerated(EnumType.STRING)
    @Column(name = STATUS_COL_NAME, nullable = false)
    public ReviewStatusEnum status;

    @Column(name = REVIEWED_TIME_COL_NAME, nullable = false)
    public LocalDateTime reviewedTime;

    public Review(Review other) {
        this.id = other.id;
        if (other.document != null) {
            this.document = new Document(other.document, false);
        }
        this.reviewer = other.reviewer;
        this.reviewerFullName = other.reviewerFullName;
        this.reviewLevel = other.reviewLevel;
        this.status = other.status;
        this.reviewedTime = other.reviewedTime;
    }
}
