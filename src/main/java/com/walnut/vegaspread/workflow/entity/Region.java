package com.walnut.vegaspread.workflow.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Entity
@Getter
@Table(name = "region")
public class Region {

    public static final String REGION_ID_COL_NAME = "region_id";
    public static final String REGION_NAME_COL_NAME = "region_name";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = REGION_ID_COL_NAME, nullable = false)
    private Integer regionId;

    @Column(name = REGION_NAME_COL_NAME, nullable = false, unique = true)
    private String regionName;

    @Embedded
    private Auditable auditable;
}
