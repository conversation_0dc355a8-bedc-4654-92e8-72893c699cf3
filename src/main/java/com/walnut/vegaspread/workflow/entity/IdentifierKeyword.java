package com.walnut.vegaspread.workflow.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Entity
@Table(name = "identifier_keyword")
public class IdentifierKeyword {

    public static final String KW_ID_COL_NAME = "kw_id";
    public static final String KW_TEXT_COL_NAME = "kw_text";
    public static final String CATEGORY_COL_NAME = "category";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = KW_ID_COL_NAME, nullable = false)
    public Integer kwId;

    @Column(name = KW_TEXT_COL_NAME, nullable = false, unique = true, length = 150)
    public String kwText;

    @Column(name = CATEGORY_COL_NAME, nullable = false, length = 20)
    public String category;

    @JsonIgnore
    @Embedded
    public Auditable auditable;
}