package com.walnut.vegaspread.workflow.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.Transient;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Embeddable
public class Auditable {

    public static final String CREATED_BY_COL_NAME = "created_by";
    public static final String CREATED_TIME_COL_NAME = "created_time";

    @Column(name = CREATED_BY_COL_NAME, nullable = false)
    public String createdBy;

    @Transient
    public String createdByFullName;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Column(name = CREATED_TIME_COL_NAME, nullable = false)
    public LocalDateTime createdTime;

    public Auditable(String createdBy) {
        this.createdBy = createdBy;
        this.createdTime = LocalDateTime.now();
    }

}
