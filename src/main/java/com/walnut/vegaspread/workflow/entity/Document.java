package com.walnut.vegaspread.workflow.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.walnut.vegaspread.common.model.extraction.DenominationEnum;
import com.walnut.vegaspread.common.model.workflow.StatusEnum;
import com.walnut.vegaspread.workflow.model.SpreadLevelEnum;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Entity
@Table(name = "document")
public class Document {

    public static final String DOC_ID_COL_NAME = "doc_id";
    public static final String PERIOD_COL_NAME = "period";
    public static final String SPREAD_LEVEL_COL_NAME = "spread_level";
    public static final String FILE_NAME_COL_NAME = "file_name";
    public static final String FILE_PATH_COL_NAME = "file_path";
    public static final String FILE_SIZE_COL_NAME = "file_size";
    public static final String DPI_COL_NAME = "dpi";
    public static final String MAPPED_ITEMS_COL_NAME = "mapped_items";
    public static final String IS_DIGITAL_COL_NAME = "is_digital";
    public static final String OCR_SCORE_COL_NAME = "ocr_score";
    public static final String FILE_DENOMINATION_COL_NAME = "file_denomination";
    public static final String OUTPUT_DENOMINATION_COL_NAME = "output_denomination";
    public static final String CURRENCY_COL_NAME = "currency";
    public static final String STATUS_COL_NAME = "status";
    public static final String STATUS_TEXT_COL_NAME = "status_text";
    public static final String LAST_MODIFIED_BY_COL_NAME = "last_modified_by";
    public static final String LAST_MODIFIED_TIME_COL_NAME = "last_modified_time";
    public static final String SPREADING_TASK_FOREIGN_KEY_COL_NAME = "spread_id";
    public static final String REVIEW_MAPPED_COL_NAME = "document";
    public static final String IS_OUTLIER_COL_NAME = "is_outlier";
    public static final String OUTLIER_COMMENT_COL_NAME = "outlier_comment";
    public static final String APPLICATION_ID_COL_NAME = "application_id";

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = DOC_ID_COL_NAME, columnDefinition = "uuid", nullable = false)
    public UUID docId;

    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Column(name = PERIOD_COL_NAME, nullable = false)
    public LocalDate period;

    @Enumerated(EnumType.STRING)
    @Column(name = SPREAD_LEVEL_COL_NAME, nullable = false, length = 20)
    public SpreadLevelEnum spreadLevel;

    @Column(name = FILE_NAME_COL_NAME, nullable = false)
    public String fileName;

    @Column(name = FILE_PATH_COL_NAME, nullable = false, unique = true)
    public String filePath;

    @Column(name = FILE_SIZE_COL_NAME, nullable = false)
    public Integer fileSize;

    @Schema(type = SchemaType.INTEGER, examples = {"0"})
    @Column(name = DPI_COL_NAME)
    public Short dpi;

    @Schema(type = SchemaType.INTEGER, examples = {"0"})
    @Column(name = MAPPED_ITEMS_COL_NAME)
    public Short mappedItems;

    @Column(name = IS_DIGITAL_COL_NAME)
    public Boolean isDigital;

    @Schema(type = SchemaType.INTEGER, examples = {"0"})
    @Column(name = OCR_SCORE_COL_NAME)
    public Byte ocrScore;

    @Enumerated(EnumType.STRING)
    @Column(name = FILE_DENOMINATION_COL_NAME)
    public DenominationEnum fileDenomination;

    @Enumerated(EnumType.STRING)
    @Column(name = OUTPUT_DENOMINATION_COL_NAME)
    public DenominationEnum outputDenomination;

    @Column(name = CURRENCY_COL_NAME)
    public String currency;

    @Enumerated(EnumType.STRING)
    @Column(name = STATUS_COL_NAME, nullable = false, length = 20)
    public StatusEnum status;

    @Column(name = STATUS_TEXT_COL_NAME, nullable = false)
    public String statusText;

    @JsonUnwrapped
    @Embedded
    public Auditable auditable;

    @Column(name = LAST_MODIFIED_BY_COL_NAME, nullable = false)
    public String lastModifiedBy;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Column(name = LAST_MODIFIED_TIME_COL_NAME, nullable = false)
    public LocalDateTime lastModifiedTime;

    @ToString.Exclude
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = SPREADING_TASK_FOREIGN_KEY_COL_NAME)
    @JsonBackReference(value = "documents")
    public SpreadingTask spreadingTask;

    @Builder.Default
    @OneToMany(mappedBy = REVIEW_MAPPED_COL_NAME, orphanRemoval = true, cascade = CascadeType.ALL, fetch =
            FetchType.EAGER)
    @JsonManagedReference
    public List<Review> reviews = new ArrayList<>();

    @Builder.Default
    @Column(name = IS_OUTLIER_COL_NAME, nullable = false)
    public Boolean isOutlier = Boolean.FALSE;

    @Builder.Default
    @Column(name = OUTLIER_COMMENT_COL_NAME, nullable = false)
    public String outlierComment = StringUtils.EMPTY;

    @Column(name = APPLICATION_ID_COL_NAME)
    public String applicationId;

    public Document(Document other, boolean includeReverseRelationship) {
        this.docId = other.docId;
        this.period = other.period;
        this.spreadLevel = other.spreadLevel;
        this.fileName = other.fileName;
        this.filePath = other.filePath;
        this.fileSize = other.fileSize;
        this.dpi = other.dpi;
        this.mappedItems = other.mappedItems;
        this.isDigital = other.isDigital;
        this.ocrScore = other.ocrScore;
        this.fileDenomination = other.fileDenomination;
        this.outputDenomination = other.outputDenomination;
        this.applicationId = other.applicationId;
        this.currency = other.currency;
        this.status = other.status;
        this.statusText = other.statusText;
        if (other.auditable != null) {
            this.auditable = other.auditable;
        }
        this.lastModifiedBy = other.lastModifiedBy;
        this.lastModifiedTime = other.lastModifiedTime;
        if (other.spreadingTask != null && includeReverseRelationship) {
            this.spreadingTask = other.spreadingTask;  // This assumes spreadingTask doesn't need a deep copy.
        }
        if (includeReverseRelationship && other.reviews != null) {
            for (Review review : other.reviews) {
                Review copy = new Review(review);
                this.reviews.add(copy);
            }
        }
        this.isOutlier = other.isOutlier;
        this.outlierComment = other.outlierComment;
    }
}
