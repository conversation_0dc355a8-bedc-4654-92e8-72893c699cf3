package com.walnut.vegaspread.workflow.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.ToString;

@AllArgsConstructor
@NoArgsConstructor
@ToString
@Entity
@Table(name = "wise_service")
public class WiseService{

    public static final String NAME_COL_NAME = "name";
    public static final String URL_COL_NAME = "url";

    @Id
    @Column(name = NAME_COL_NAME, nullable = false, length = 50)
    public String name;

    @Column(name = URL_COL_NAME, nullable = false)
    public String url;

}