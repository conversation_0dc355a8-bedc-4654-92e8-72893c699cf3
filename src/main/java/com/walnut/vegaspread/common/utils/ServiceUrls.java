package com.walnut.vegaspread.common.utils;

public class ServiceUrls {

    // Service url's for google cloud services.
    public static final String IAM_URL = "https://%s-vega-iam-agxygoruoq-as.a.run.app/vegaspread/api/v1/iam";
    public static final String AUDIT_URL = "https://%s-vega-audit-agxygoruoq-as.a.run.app/vegaspread/api/v1/audit";
    public static final String WORKFLOW_URL = "https://%s-vega-workflow-agxygoruoq-as.a.run" +
            ".app/vegaspread/api/v1/workflow";
    public static final String EXTRACTION_URL = "https://%s-vega-extraction-agxygoruoq-as.a.run" +
            ".app/vegaspread/api/v1/extraction";
    public static final String COA_URL = "https://%s-vega-coa-agxygoruoq-as.a.run.app/vegaspread/api/v1/coa";

    // Service url's for aws lambda services.
    public static final String AWS_IAM_URL = "%s/vegaspread/api/v1/iam";
    public static final String AWS_AUDIT_URL = "%s/vegaspread/api/v1/audit";
    public static final String AWS_WORKFLOW_URL = "%s/vegaspread/api/v1/workflow";
    public static final String AWS_EXTRACTION_URL = "%s/vegaspread/api/v1/extraction";
    public static final String AWS_COA_URL = "%s/vegaspread/api/v1/coa";

    public static final String LOCAL_IAM_URL = "http://localhost:8084/vegaspread/api/v1/iam";
    public static final String LOCAL_AUDIT_URL = "http://localhost:8082/vegaspread/api/v1/audit";
    public static final String LOCAL_WORKFLOW_URL = "http://localhost:8080/vegaspread/api/v1/workflow";
    public static final String LOCAL_EXTRACTION_URL = "http://localhost:8081/vegaspread/api/v1/extraction";
    public static final String LOCAL_COA_URL = "http://localhost:8083/vegaspread/api/v1/coa";
    public static final String LOCAL_GPU_POD_MANAGEMENT_URL = "http://localhost:8085/gpu-pod-management";
    public static final String LOCAL_DOC_AI_URL = "http://localhost:8086/docai";

    public static final String AWS_LOCAL_IAM_URL = "http://localhost:8084/vegaspread/api/v1/iam";
    public static final String AWS_LOCAL_AUDIT_URL = "http://localhost:8082/vegaspread/api/v1/audit";
    public static final String AWS_LOCAL_WORKFLOW_URL = "http://localhost:8080/vegaspread/api/v1/workflow";
    public static final String AWS_LOCAL_EXTRACTION_URL = "http://localhost:8081/vegaspread/api/v1/extraction";
    public static final String AWS_LOCAL_COA_URL = "http://localhost:8083/vegaspread/api/v1/coa";
    public static final String AWS_LOCAL_GPU_POD_MANAGEMENT_URL = "http://localhost:8085/gpu-pod-management";
    public static final String AWS_LOCAL_DOC_AI_URL = "http://localhost:8086/docai";

    private ServiceUrls() {
        throw new IllegalStateException("Utility class");
    }
}
