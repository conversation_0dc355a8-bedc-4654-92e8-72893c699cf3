package com.walnut.vegaspread.common.utils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public enum Client {
    WALNUT("walnut"),
    <PERSON><PERSON>("demo"),
    <PERSON><PERSON>("yun"),
    TRAINING("training"),
    RCBC("rcbc"),
    SECURITYBANK("securitybank"),
    HDFC("hdfc"),
    JSW("jsw"),
    COMMON("common");

    public static final Logger logger = LogManager.getLogger(Client.class);
    private final String clientName;

    Client(String clientName) {
        this.clientName = clientName.toLowerCase();
    }

    public static boolean isValidClient(String clientName) {
        if (clientName != null && !clientName.isEmpty()) {
            clientName = clientName.toLowerCase();
            for (Client client : Client.values()) {
                logger.debug(client.getClientName());
                if ((client.getClientName()).equals(clientName)) {
                    return true;
                }
            }
        }
        return false;
    }

    public String getClientName() {
        return clientName.toLowerCase();
    }
}
