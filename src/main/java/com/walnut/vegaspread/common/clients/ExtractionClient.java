package com.walnut.vegaspread.common.clients;

import com.walnut.vegaspread.common.clients.filter.RestClientLoggingFilter;
import com.walnut.vegaspread.common.model.extraction.TableTagDto;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.rest.client.annotation.RegisterClientHeaders;
import org.eclipse.microprofile.rest.client.annotation.RegisterProvider;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import java.util.List;
import java.util.UUID;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("")
@RegisterRestClient(configKey = "extraction")
@RegisterProvider(RestClientLoggingFilter.class)
@RegisterClientHeaders
public interface ExtractionClient {

    String LAYOUT_BLOCK_URL = "/block";
    String TABLE_TAG_URL = "/table/tag";
    String API_AUTH_PREFIX = "/wise/processor";
    String COA_MAPPING_URL = "/coa-mapping";

    @Path(LAYOUT_BLOCK_URL + "/doc/{docId}/ids")
    @GET
    List<Integer> getBlockIdsForDoc(@PathParam("docId") UUID docId);

    @DELETE
    @Path(LAYOUT_BLOCK_URL + "/doc/{docId}")
    long deleteBlocksByDocId(@PathParam("docId") UUID docId);

    @POST
    @Path(TABLE_TAG_URL + "/list/ids")
    List<TableTagDto.Response> getTableTagsByIds(List<Integer> tagIds);

    @POST
    @Path(API_AUTH_PREFIX + TABLE_TAG_URL + "/list/ids")
    List<TableTagDto.Response> getTableTagsByIdsForProcessor(List<Integer> tagIds);

    @DELETE
    @Path(COA_MAPPING_URL + "/doc/{docId}")
    long deleteCoaMappingByDocId(@PathParam("docId") UUID docId);
}
