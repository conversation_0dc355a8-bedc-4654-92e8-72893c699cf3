package com.walnut.vegaspread.common.clients;

import com.walnut.vegaspread.common.clients.filter.RestClientLoggingFilter;
import io.quarkus.runtime.annotations.RegisterForReflection;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.rest.client.annotation.RegisterClientHeaders;
import org.eclipse.microprofile.rest.client.annotation.RegisterProvider;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import java.util.List;
import java.util.Map;

@RegisterForReflection
@RegisterRestClient(configKey = "iam")
@Path("/admin")
@RegisterClientHeaders
@RegisterProvider(RestClientLoggingFilter.class)
@Consumes(MediaType.APPLICATION_JSON)
public interface IamClient {

    @POST
    @Path("/names-from-usernames")
    @Produces(MediaType.APPLICATION_JSON)
    Map<String, String> getNamesFromUsernames(List<String> usernames);

    @POST
    @Path("/name-from-username")
    @Produces(MediaType.TEXT_PLAIN)
    String getNameFromUsername(String username);

    @POST
    @Path("/usernames-from-names")
    @Produces(MediaType.APPLICATION_JSON)
    Map<String, String> getUsernamesFromNames(List<String> names);

    @POST
    @Path("/username-from-name")
    @Produces(MediaType.TEXT_PLAIN)
    String getUsernameFromName(String name);
}


