package com.walnut.vegaspread.common.clients;

import com.walnut.vegaspread.common.clients.filter.RestClientLoggingFilter;
import com.walnut.vegaspread.common.model.extraction.DocData;
import com.walnut.vegaspread.common.model.extraction.UrlDto;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.rest.client.annotation.RegisterClientHeaders;
import org.eclipse.microprofile.rest.client.annotation.RegisterProvider;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestQuery;

import java.time.LocalDateTime;
import java.util.UUID;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("")
@RegisterRestClient(configKey = "workflow")
@RegisterProvider(RestClientLoggingFilter.class)
@RegisterClientHeaders
public interface WorkflowClient {
    String API_AUTH_PREFIX = "/wise/processor";

    @Path("/document/{docId}/time")
    @PATCH
    LocalDateTime updateTime(@PathParam("docId") UUID docId);

    @Path("/document/{docId}")
    @GET
    DocData getDocument(@PathParam("docId") UUID docId);

    @Path("/processor/doc/summary/template/excel/link/{clientName}")
    @GET
    UrlDto getExcelTemplate(@PathParam("clientName") String clientName, @RestQuery("coaClientName") String coaClient);

    //Placeholder API in workflow to reprocess document.
    @Path("/re-process")
    @POST
    @Transactional
    void reprocess();
}
