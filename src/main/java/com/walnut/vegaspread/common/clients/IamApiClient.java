package com.walnut.vegaspread.common.clients;

import com.walnut.vegaspread.common.clients.filter.RestClientLoggingFilter;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.rest.client.annotation.RegisterClientHeaders;
import org.eclipse.microprofile.rest.client.annotation.RegisterProvider;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestResponse;

@RegisterRestClient(configKey = "iam")
@Path("/api")
@RegisterClientHeaders
@RegisterProvider(RestClientLoggingFilter.class)
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.TEXT_PLAIN)
public interface IamApiClient {

    @POST
    @Path("/validate-api-key")
    RestResponse<String> validateApiKey(@HeaderParam("X-CLIENT-ID") String clientId,
                                        @HeaderParam("X-API-KEY") String apiKey);

    @GET
    @Path("/user/{username}/email")
    RestResponse<String> getUserEmail(@PathParam("username") String username);
}
