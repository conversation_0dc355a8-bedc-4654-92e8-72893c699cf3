package com.walnut.vegaspread.common.clients;

import com.walnut.vegaspread.common.clients.filter.RestClientLoggingFilter;
import com.walnut.vegaspread.common.model.audit.coa.CoaItemAuditDto;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedRowCoaDataAuditDto;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedTableHeaderAuditDto;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedTableRowAuditDto;
import com.walnut.vegaspread.common.model.audit.extraction.LayoutBlockAuditDto;
import com.walnut.vegaspread.common.model.audit.extraction.SubtotalAuditDto;
import com.walnut.vegaspread.common.model.audit.extraction.SubtotalMappingAuditDto;
import com.walnut.vegaspread.common.model.audit.workflow.DocumentAuditDto;
import com.walnut.vegaspread.common.model.audit.workflow.ReviewAuditDto;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.rest.client.annotation.RegisterClientHeaders;
import org.eclipse.microprofile.rest.client.annotation.RegisterProvider;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestPath;

import java.util.List;
import java.util.UUID;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("")
@RegisterRestClient(configKey = "audit")
@RegisterProvider(RestClientLoggingFilter.class)
@RegisterClientHeaders
public interface AuditClient {
    String COA_PREFIX = "/coa/coaitem";
    String EXTRACTION_ROW_PREFIX = "/extracted/table/row";
    String EXTRACTION_BLOCK_PREFIX = "/extracted/block";
    String API_AUTH_PREFIX = "/api-auth";
    String WORKFLOW_DOC_PREFIX = "/workflow/document";
    String WORKFLOW_REVIEW_PREFIX = "/workflow/review";
    String EXTRACTION_ROW_COA_DATA_JOIN_PREFIX = "/extracted/table/row-coa";
    String SUBTOTAL_PREFIX = "/extracted/subtotal";
    String SUBTOTAL_MAPPING_PREFIX = "/extracted/subtotal-mapping";
    String EXTRACTION_HEADER_PREFIX = "/extracted/table/header";

    @POST
    @Path(COA_PREFIX + "/audit-create")
    List<CoaItemAuditDto.Response> coaItemAuditForCreate(List<CoaItemAuditDto.Create> coaItemAuditEntityCreateDto);

    @POST
    @Path(COA_PREFIX + "/audit-update")
    List<CoaItemAuditDto.Response> coaItemAuditForUpdate(List<CoaItemAuditDto.Update> coaItemAuditEntityUpdateDto);

    @POST
    @Path(COA_PREFIX + "/audit-delete")
    List<CoaItemAuditDto.Response> coaItemAuditForDelete(List<CoaItemAuditDto.Delete> coaItemAuditEntityDeleteDtos);

    @Path(COA_PREFIX + "/list")
    @GET
    List<CoaItemAuditDto.Response> coaItemAuditList();

    @POST
    @Path(EXTRACTION_ROW_PREFIX + "/audit-create")
    List<ExtractedTableRowAuditDto.Response> extractedTableRowAuditForCreate(
            List<ExtractedTableRowAuditDto.Create> extractedTableRowCreateAuditDtos);

    @POST
    @Path(EXTRACTION_ROW_PREFIX + "/audit-update")
    List<ExtractedTableRowAuditDto.Response> extractedTableRowAuditForUpdate(
            List<ExtractedTableRowAuditDto.Update> extractedTableRowUpdateAuditDtos);

    @POST
    @Path(API_AUTH_PREFIX + EXTRACTION_ROW_PREFIX + "/audit-update")
    List<ExtractedTableRowAuditDto.Response> extractedTableRowAuditForUpdateByProcessor(
            List<ExtractedTableRowAuditDto.Update> extractedTableRowUpdateAuditDtos);

    @POST
    @Path(EXTRACTION_ROW_PREFIX + "/audit-delete")
    List<ExtractedTableRowAuditDto.Response> extractedTableRowAuditForDelete(
            List<ExtractedTableRowAuditDto.Delete> extractedTableRowDeleteAuditDtos);

    @POST
    @Path(EXTRACTION_ROW_PREFIX + "/list")
    ExtractedTableRowAuditDto.ListResponse listAuditsForTables(ExtractedTableRowAuditDto.ListForTables listTablesDto);

    @POST
    @Path(EXTRACTION_BLOCK_PREFIX + "/audit-create")
    List<LayoutBlockAuditDto.Response> layoutBlockAuditForCreate(
            List<LayoutBlockAuditDto.Create> layoutBlockAuditCreateDtos);

    @POST
    @Path(EXTRACTION_BLOCK_PREFIX + "/audit-update")
    List<LayoutBlockAuditDto.Response> layoutBlockAuditForUpdate(
            List<LayoutBlockAuditDto.Update> layoutBlockAuditUpdateDtos);

    @POST
    @Path(EXTRACTION_BLOCK_PREFIX + "/audit-delete")
    List<LayoutBlockAuditDto.Response> layoutBlockAuditForDelete(
            List<LayoutBlockAuditDto.Delete> layoutBlockAuditDeleteDtos);

    @POST
    @Path(API_AUTH_PREFIX + EXTRACTION_BLOCK_PREFIX + "/audit-create")
    List<LayoutBlockAuditDto.Response> layoutBlockAuditForCreateByProcessor(
            List<LayoutBlockAuditDto.Create> layoutBlockAuditCreateDtos);

    @POST
    @Path(API_AUTH_PREFIX + EXTRACTION_BLOCK_PREFIX + "/audit-update")
    List<LayoutBlockAuditDto.Response> layoutBlockAuditForUpdateByProcessor(
            List<LayoutBlockAuditDto.Update> layoutBlockAuditUpdateDtos);

    @POST
    @Path(API_AUTH_PREFIX + EXTRACTION_BLOCK_PREFIX + "/audit-delete")
    List<LayoutBlockAuditDto.Response> layoutBlockAuditForDeleteByProcessor(
            List<LayoutBlockAuditDto.Delete> layoutBlockAuditDeleteDtos);

    @POST
    @Path(WORKFLOW_DOC_PREFIX + "/audit-create")
    List<DocumentAuditDto.Response> documentAuditForCreate(List<DocumentAuditDto.Create> documentAuditCreateDtos);

    @POST
    @Path(API_AUTH_PREFIX + WORKFLOW_DOC_PREFIX + "/audit-create")
    List<DocumentAuditDto.Response> documentAuditForCreateByProcessor(
            List<DocumentAuditDto.Create> documentAuditCreateDtos);

    @POST
    @Path(WORKFLOW_DOC_PREFIX + "/audit-update")
    List<DocumentAuditDto.Response> documentAuditForUpdate(
            List<DocumentAuditDto.UpdateOrDelete> documentAuditUpdateDtos);

    @POST
    @Path(WORKFLOW_DOC_PREFIX + "/audit-delete")
    List<DocumentAuditDto.Response> documentAuditForDelete(
            List<DocumentAuditDto.UpdateOrDelete> documentAuditDeleteDtos);

    @GET
    @Path(WORKFLOW_DOC_PREFIX + "/{auditId}")
    DocumentAuditDto.Response documentAuditGet(@RestPath("auditId") Integer id);

    @GET
    @Path(WORKFLOW_DOC_PREFIX + "/{docId}")
    List<DocumentAuditDto.Response> documentAuditGetByDocId(@RestPath("docId") UUID docId);

    @POST
    @Path(WORKFLOW_REVIEW_PREFIX + "/audit-create")
    List<ReviewAuditDto.Response> reviewAuditForCreate(List<ReviewAuditDto.Create> reviewAuditCreateDtos);

    @POST
    @Path(WORKFLOW_REVIEW_PREFIX + "/audit-update")
    List<ReviewAuditDto.Response> reviewAuditForUpdate(List<ReviewAuditDto.Update> reviewAuditUpdateDtos);

    @GET
    @Path(WORKFLOW_REVIEW_PREFIX + "/{auditId}")
    ReviewAuditDto.Response reviewAuditGet(@RestPath("auditId") Integer id);

    @GET
    @Path(WORKFLOW_REVIEW_PREFIX + "/{reviewId}")
    List<ReviewAuditDto.Response> reviewAuditGetByReviewId(@RestPath("reviewId") Integer reviewId);

    @POST
    @Path(API_AUTH_PREFIX + EXTRACTION_ROW_COA_DATA_JOIN_PREFIX + "/audit-create")
    void extractedRowCoaDataAuditForCreateByProcessor(
            List<ExtractedRowCoaDataAuditDto.Create> extractedRowCoaDataCreateAuditDtos);

    @POST
    @Path(EXTRACTION_ROW_COA_DATA_JOIN_PREFIX + "/audit-create")
    void extractedRowCoaDataAuditForCreate(List<ExtractedRowCoaDataAuditDto.Create> extractedRowCoaDataCreateAuditDtos);

    @POST
    @Path(API_AUTH_PREFIX + EXTRACTION_ROW_COA_DATA_JOIN_PREFIX + "/audit-update")
    void extractedRowCoaDataAuditForUpdateByProcessor(
            List<ExtractedRowCoaDataAuditDto.Update> extractedRowCoaDataUpdateAuditDtos);

    @POST
    @Path(EXTRACTION_ROW_COA_DATA_JOIN_PREFIX + "/audit-update")
    void extractedRowCoaDataAuditForUpdate(List<ExtractedRowCoaDataAuditDto.Update> extractedRowCoaDataUpdateAuditDtos);

    @POST
    @Path(API_AUTH_PREFIX + EXTRACTION_ROW_COA_DATA_JOIN_PREFIX + "/audit-delete")
    void extractedRowCoaDataAuditForDeleteByProcessor(
            List<ExtractedRowCoaDataAuditDto.Delete> extractedRowCoaDatDeleteAuditDtos);

    @POST
    @Path(EXTRACTION_ROW_COA_DATA_JOIN_PREFIX + "/audit-delete")
    void extractedRowCoaDataAuditForDelete(List<ExtractedRowCoaDataAuditDto.Delete> extractedRowCoaDatDeleteAuditDtos);

    @POST
    @Path(SUBTOTAL_PREFIX + "/audit-create")
    List<SubtotalAuditDto.Response> subtotalAuditForCreate(
            List<SubtotalAuditDto.Create> subtotalAuditCreateDtos);

    @POST
    @Path(SUBTOTAL_PREFIX + "/audit-update")
    List<SubtotalAuditDto.Response> subtotalAuditForUpdate(
            List<SubtotalAuditDto.Update> subtotalAuditUpdateDtos);

    @POST
    @Path(SUBTOTAL_PREFIX + "/audit-delete")
    List<SubtotalAuditDto.Response> subtotalAuditForDelete(
            List<SubtotalAuditDto.Delete> subtotalAuditDeleteDtos);

    @POST
    @Path(SUBTOTAL_MAPPING_PREFIX + "/audit-create")
    List<SubtotalMappingAuditDto.Response> subtotalMappingAuditForCreate(
            List<SubtotalMappingAuditDto.Create> subtotalMappingAuditCreateDtos);

    @POST
    @Path(SUBTOTAL_MAPPING_PREFIX + "/audit-update")
    List<SubtotalMappingAuditDto.Response> subtotalMappingAuditForUpdate(
            List<SubtotalMappingAuditDto.Update> subtotalMappingAuditUpdateDtos);

    @POST
    @Path(SUBTOTAL_MAPPING_PREFIX + "/audit-delete")
    List<SubtotalMappingAuditDto.Response> subtotalMappingAuditForDelete(
            List<SubtotalMappingAuditDto.Delete> subtotalMappingAuditDeleteDtos);

    @POST
    @Path(EXTRACTION_HEADER_PREFIX + "/audit-create")
    List<ExtractedTableHeaderAuditDto.Response> extractedTableHeaderAuditForCreate(
            List<ExtractedTableHeaderAuditDto.Create> extractedTableHeaderCreateAuditDtos);

    @POST
    @Path(EXTRACTION_HEADER_PREFIX + "/audit-update")
    List<ExtractedTableHeaderAuditDto.Response> extractedTableHeaderAuditForUpdate(
            List<ExtractedTableHeaderAuditDto.Update> extractedTableHeaderUpdateAuditDtos);

    @POST
    @Path(EXTRACTION_HEADER_PREFIX + "/audit-delete")
    List<ExtractedTableHeaderAuditDto.Response> extractedTableHeaderAuditForDelete(
            List<ExtractedTableHeaderAuditDto.Delete> extractedTableHeaderDeleteAuditDtos);
}
