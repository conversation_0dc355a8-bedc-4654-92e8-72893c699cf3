package com.walnut.vegaspread.common.clients.filter;

import com.walnut.vegaspread.common.model.cloud.CloudPlatform;
import com.walnut.vegaspread.common.utils.ConfigKeys;
import jakarta.ws.rs.client.ClientRequestContext;
import jakarta.ws.rs.client.ClientRequestFilter;
import jakarta.ws.rs.core.MultivaluedMap;
import org.eclipse.microprofile.config.ConfigProvider;
import org.jboss.logging.Logger;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * A ClientRequestFilter that logs the URL and headers for REST client API calls
 * when the cloud provider is not GCP.
 */
public class RestClientLoggingFilter implements ClientRequestFilter {

    private static final Logger logger = Logger.getLogger(RestClientLoggingFilter.class);
    private static final String CLOUD_PROVIDER_TYPE = ConfigProvider.getConfig()
            .getOptionalValue(ConfigKeys.CLOUD_PROVIDER_TYPE, String.class)
            .orElse("");

    @Override
    public void filter(ClientRequestContext requestContext) throws IOException {
        // Only log details when the cloud provider is not GCP
        if (!CLOUD_PROVIDER_TYPE.equals(CloudPlatform.GCP.getProvider())) {
            // Log the URL
            logger.debug("REST Client Request URL: " + requestContext.getUri());

            // Log the headers
            MultivaluedMap<String, Object> headers = requestContext.getHeaders();
            logger.debug("REST Client Request Headers:");

            for (Map.Entry<String, List<Object>> header : headers.entrySet()) {
                String headerName = header.getKey();
                // Don't log full authorization token value for security
                if ("Authorization".equalsIgnoreCase(headerName) ||
                        "X-API-KEY".equalsIgnoreCase(headerName) ||
                        "token".equalsIgnoreCase(headerName)) {
                    logger.debug("  " + headerName + ": [PRESENT BUT NOT LOGGED]");
                } else {
                    logger.debug("  " + headerName + ": " + header.getValue());
                }
            }
        }
    }
}
