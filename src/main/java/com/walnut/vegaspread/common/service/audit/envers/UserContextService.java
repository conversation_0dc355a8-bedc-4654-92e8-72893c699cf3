package com.walnut.vegaspread.common.service.audit.envers;

import io.quarkus.arc.Unremovable;
import io.vertx.ext.web.RoutingContext;
import jakarta.enterprise.context.RequestScoped;
import org.eclipse.microprofile.jwt.JsonWebToken;

import static com.walnut.vegaspread.common.utils.Jwt.getUsername;

@RequestScoped
@Unremovable
public class UserContextService {

    private final JsonWebToken jwt;
    private final RoutingContext routingContext;

    public UserContextService(JsonWebToken jwt, RoutingContext routingContext) {
        this.jwt = jwt;
        this.routingContext = routingContext;
    }

    public String getCurrentUsername() {
        String requestURI = routingContext.request().uri();

        // Use developer for processor calls
        if (requestURI != null && requestURI.contains("/wise/processor")) {
            return "developer";
        }

        // Pull from JWT
        if (jwt != null && jwt.getClaimNames() != null && !jwt.getClaimNames().isEmpty()) {
            String username = getUsername(jwt);
            if (username != null) {
                return username;
            }
        }

        throw new IllegalStateException("Unable to determine current username");
    }
}
