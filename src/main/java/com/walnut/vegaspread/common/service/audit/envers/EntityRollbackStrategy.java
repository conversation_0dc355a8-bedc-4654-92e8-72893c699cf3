package com.walnut.vegaspread.common.service.audit.envers;

import org.hibernate.envers.AuditReader;
import org.hibernate.envers.RevisionType;

/**
 * Strategy interface for entity-specific rollback operations.
 * This allows different entities to customize their rollback behavior while
 * using the generic rollback infrastructure.
 *
 * @param <E>  The entity type
 * @param <ID> The entity ID type
 */
public interface EntityRollbackStrategy<E, ID> {

    /**
     * Get the entity class this strategy handles.
     */
    Class<E> getEntityClass();

    /**
     * Find an entity by its ID.
     */
    E findById(ID entityId);

    /**
     * Persist or update an entity.
     */
    void persist(E entity);

    /**
     * Delete an entity by ID.
     */
    void delete(ID entityId);

    /**
     * Extract the entity ID from an entity instance.
     */
    ID getEntityId(E entity);

    /**
     * Create a new instance of the entity.
     */
    E createNewEntity();

    /**
     * Copy audited fields from source entity to target entity.
     * This method should only copy fields that are @Audited and can be safely restored.
     * Fields marked with @NotAudited should be handled specially or skipped.
     *
     * @param source           The source entity (from audit history)
     * @param target           The target entity (current or new entity)
     * @param revisionType     The type of revision (ADD, MOD, DEL)
     * @param isEntityCreation True if we're creating a new entity, false if updating existing
     */
    void copyAuditedFields(E source, E target, RevisionType revisionType, boolean isEntityCreation);

    /**
     * Handle special cases for entity deletion rollback.
     * This is called when rolling back a DEL operation.
     *
     * @param entityId            The entity ID
     * @param stateBeforeDeletion The entity state before it was deleted
     * @param reader              The audit reader
     * @return The restored entity, or null if restoration failed
     */
    default E handleDeletionRollback(ID entityId, E stateBeforeDeletion, AuditReader reader) {
        // Default implementation: recreate entity with state before deletion
        E currentEntity = findById(entityId);

        if (currentEntity == null) {
            // Entity doesn't exist, create it
            E restoredEntity = createNewEntity();
            copyAuditedFields(stateBeforeDeletion, restoredEntity, RevisionType.ADD, true);
            persist(restoredEntity);
            return restoredEntity;
        } else {
            // Entity exists, restore it to state before deletion
            copyAuditedFields(stateBeforeDeletion, currentEntity, RevisionType.MOD, false);
            persist(currentEntity);
            return currentEntity;
        }
    }

    /**
     * Validate if rollback is allowed for this entity and revision type.
     * Override this to add custom business rules.
     */
    default boolean isRollbackAllowed(ID entityId, RevisionType revisionType) {
        return true;
    }

    /**
     * Get a human-readable name for this entity type (for logging).
     */
    default String getEntityTypeName() {
        return getEntityClass().getSimpleName();
    }
}
