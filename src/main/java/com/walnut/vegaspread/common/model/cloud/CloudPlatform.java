package com.walnut.vegaspread.common.model.cloud;

public enum CloudPlatform {
    GCP("gcp", "/workflow/"),
    AWS("aws", "/workflow/");

    private final String provider;
    private final String baseUrlPostfixServicePath;

    CloudPlatform(String provider, String baseUrlPostfixServicePath) {
        this.provider = provider;
        this.baseUrlPostfixServicePath = baseUrlPostfixServicePath;
    }

    public String getProvider() {
        return provider;
    }

    public String getBaseUrlPostfixServicePath() {
        return baseUrlPostfixServicePath;
    }
}
