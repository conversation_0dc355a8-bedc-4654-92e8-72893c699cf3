package com.walnut.vegaspread.common.model.audit.envers;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.metamodel.EntityType;
import jakarta.persistence.metamodel.Metamodel;
import jakarta.ws.rs.core.Response;

import java.util.concurrent.ConcurrentHashMap;

/**
 * Service for audit field validation.
 * Only handles the restricted set of allowed fields for filtering and sorting.
 * <p>
 * Filtering allowed on: audited columns, username, timestamp
 * Sorting allowed on: audited columns, timestamp
 */
@ApplicationScoped
public class AuditFieldDetector {

    // Entity property validation
    private final ConcurrentHashMap<String, Boolean> entityPropertyCache = new ConcurrentHashMap<>();

    /**
     * Check if a field is allowed for filtering.
     * Filtering is only allowed on: audited columns, username, timestamp
     */
    public boolean isFilteringAllowed(Class<?> entityClass, String fieldName, EntityManager entityManager) {
        // Allow filtering on username and timestamp (revision properties)
        if ("username".equalsIgnoreCase(fieldName) || "timestamp".equalsIgnoreCase(fieldName)) {
            return true;
        }

        // Allow filtering on audited entity columns (including ID)
        return isEntityProperty(entityClass, fieldName, entityManager);
    }

    /**
     * Check if a field is allowed for sorting.
     * Sorting is allowed on: all entity columns, timestamp
     */
    public boolean isSortingAllowed(Class<?> entityClass, String fieldName, EntityManager entityManager) {
        // Allow sorting on timestamp
        if ("timestamp".equalsIgnoreCase(fieldName)) {
            return true;
        }

        // Allow sorting on all entity columns (including ID)
        return isEntityProperty(entityClass, fieldName, entityManager);
    }

    /**
     * Detect filter type for fields.
     */
    public AuditFilterDto.FilterType detectFilterType(String fieldName) {
        // Simple type detection - no validation needed since service already validated
        if ("username".equalsIgnoreCase(fieldName) || "timestamp".equalsIgnoreCase(fieldName)) {
            return AuditFilterDto.FilterType.REVISION_PROPERTY;
        }

        // All other fields are entity properties (including ID)
        return AuditFilterDto.FilterType.ENTITY_PROPERTY;
    }

    /**
     * Detect sort type for fields.
     */
    public AuditSortDto.SortType detectSortType(String fieldName) {
        // Simple type detection - no validation needed since service already validated
        if ("timestamp".equalsIgnoreCase(fieldName)) {
            return AuditSortDto.SortType.REVISION_PROPERTY;
        }

        // All other fields are entity properties (including ID)
        return AuditSortDto.SortType.ENTITY_PROPERTY;
    }

    /**
     * Check if a field is an entity property using JPA metamodel.
     */
    private boolean isEntityProperty(Class<?> entityClass, String fieldName, EntityManager entityManager) {
        String cacheKey = entityClass.getName() + "." + fieldName;

        return entityPropertyCache.computeIfAbsent(cacheKey, key -> {
            try {
                Metamodel metamodel = entityManager.getMetamodel();
                EntityType<?> entityType = metamodel.entity(entityClass);

                // Check if the field exists in the entity metamodel
                entityType.getAttribute(fieldName);
                return true;
            } catch (IllegalArgumentException e) {
                ResponseException.throwResponseException(Response.Status.BAD_REQUEST,
                        "Invalid field name: " + fieldName);
                return false;
            } catch (Exception e) {
                ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR,
                        "Could not access metamodel for entity " + entityClass.getName() + ", field " + fieldName);
                return false;
            }
        });
    }
}
