package com.walnut.vegaspread.common.model.audit.envers;

import com.walnut.vegaspread.common.service.audit.envers.MetadataRevListener;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.DefaultRevisionEntity;
import org.hibernate.envers.RevisionEntity;

@Getter
@Setter
@Entity
@RevisionEntity(MetadataRevListener.class)
public class MetadataRevEntity extends DefaultRevisionEntity {
    private String username;
    private String traceId;
}
