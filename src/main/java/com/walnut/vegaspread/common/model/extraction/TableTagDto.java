package com.walnut.vegaspread.common.model.extraction;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

public interface TableTagDto {
    record Update(@NotNull Integer id, @NotBlank String tag) {
    }

    record Response(Integer id, String tag, String createdBy, LocalDateTime createdTime,
                    String lastModifiedBy, LocalDateTime lastModifiedTime) {
    }
}
