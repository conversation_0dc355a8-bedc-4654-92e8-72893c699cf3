package com.walnut.vegaspread.common.model.audit.workflow;

import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;

public interface DocumentAuditDto {

    record Create(@NotNull UUID docId, @NotNull String colName, String newValue) implements Serializable {
    }

    record UpdateOrDelete(@NotNull UUID docId, @NotNull String colName, String prevValue,
                          String newValue) implements Serializable {
    }

    record Response(@NotNull Integer id, @NotNull UUID docId, @NotNull String colName, String prevValue,
                    String newValue, @NotNull AuditStatus action, @NotNull LocalDateTime auditTime,
                    @NotNull String auditedBy, String auditedBYFullName) implements Serializable {
    }
}
