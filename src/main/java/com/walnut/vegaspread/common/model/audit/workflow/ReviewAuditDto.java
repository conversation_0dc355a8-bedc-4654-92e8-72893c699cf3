package com.walnut.vegaspread.common.model.audit.workflow;

import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.time.LocalDateTime;

public interface ReviewAuditDto {

    record Create(@NotNull Integer reviewId, @NotNull String colName, String newValue) implements Serializable {
    }

    record Update(@NotNull Integer reviewId, @NotNull String colName, String prevValue,
                  String newValue) implements Serializable {
    }

    record Response(@NotNull Integer id, @NotNull Integer reviewId, @NotNull String colName, String prevValue,
                    String newValue, @NotNull AuditStatus action, @NotNull LocalDateTime auditTime,
                    @NotNull String auditedBy, String auditedBYFullName) implements Serializable {
    }
}
