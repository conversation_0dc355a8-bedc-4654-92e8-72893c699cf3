package com.walnut.vegaspread.common.model.audit.extraction;

import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface ExtractedRowCoaDataAuditDto {
    record Create(@NotNull Integer tableId, @NotNull Integer rowId, @NotNull String colName,
                  String newValue) implements Serializable {
    }

    record Update(@NotNull Integer tableId, @NotNull Integer rowId, @NotNull String colName, String prevValue,
                  String newValue) implements Serializable {
    }

    record Delete(@NotNull Integer tableId, @NotNull Integer rowId, @NotNull String colName,
                  String prevValue) implements Serializable {
    }

    record Response(@NotNull Integer id, @NotNull Integer tableId, @NotNull Integer rowId, @NotNull String colName,
                    String prevValue, String newValue, @NotNull AuditStatus action, @NotNull LocalDateTime auditTime,
                    @NotNull String auditedBy, String auditedBYFullName) {
    }

    record ListForTables(@NotNull List<Integer> tableIds, @NotNull String colName, int pageNumber, int pageSize,
                         String filterUser, LocalDate filterDate) implements Serializable {
    }

    record ListResponse(int pageNumber, int pageSize, int totalPages, List<Response> extractedRowCoaDataAudits,
                        Map<String, String> allUsers, List<LocalDate> allDates) implements Serializable {
    }
}
