package com.walnut.vegaspread.common.model.audit.envers;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.hibernate.envers.RevisionType;

import java.util.Set;

public record EntityRevisionWithChanges<T>(
        @JsonProperty("entityState")
        T entity,
        @JsonProperty("revisionMetadata")
        MetadataRevEntity revisionEntity,
        @JsonProperty("modificationType")
        RevisionType revisionType,
        @JsonProperty("modifiedFields")
        Set<String> modifiedProperties
) {

    @SuppressWarnings("unchecked")
    public static <T> EntityRevisionWithChanges<T> fromArray(Object result, Class<T> entityClass) {
        Object[] array = (Object[]) result;
        return new EntityRevisionWithChanges<>(
                (T) array[0],
                (MetadataRevEntity) array[1],
                (RevisionType) array[2],
                (Set<String>) array[3]
        );
    }
}
