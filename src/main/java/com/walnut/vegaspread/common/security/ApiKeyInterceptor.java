package com.walnut.vegaspread.common.security;

import com.walnut.vegaspread.common.clients.ClientFactory;
import com.walnut.vegaspread.common.clients.IamApiClient;
import com.walnut.vegaspread.common.model.cloud.CloudPlatform;
import com.walnut.vegaspread.common.utils.ConfigKeys;
import jakarta.annotation.Priority;
import jakarta.interceptor.AroundInvoke;
import jakarta.interceptor.Interceptor;
import jakarta.interceptor.InvocationContext;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.logging.Logger;

import java.util.Optional;

@Priority(Interceptor.Priority.LIBRARY_BEFORE)
@Interceptor
@ApiKeyAuthenticate
public class ApiKeyInterceptor {
    private static final Logger logger = org.jboss.logging.Logger.getLogger(ApiKeyInterceptor.class);
    @Context
    ContainerRequestContext requestContext;

    @ConfigProperty(name = ConfigKeys.ENV_NAME_KEY)
    String envName;

    @ConfigProperty(name = ConfigKeys.CLOUD_PROVIDER_TYPE)
    String cloudProviderType;

    @ConfigProperty(name = ConfigKeys.AWS_GATEWAY_URL)
    Optional<String> awsGatewayUrl;

    @AroundInvoke
    public Object checkApiKey(InvocationContext context) throws Exception {
        IamApiClient iamApiClient;
        logger.debug("Cloud Provider Type for Api Key Interceptor: " + cloudProviderType);
        if (cloudProviderType.equals(CloudPlatform.AWS.getProvider())) {
            if (awsGatewayUrl.isEmpty()) {
                throw new IllegalArgumentException("AWS Gateway URL must be provided for AWS cloud provider");
            }
            iamApiClient = ClientFactory.createAWSClient(IamApiClient.class, envName, awsGatewayUrl.get());
        } else {
            iamApiClient = ClientFactory.createClient(IamApiClient.class, envName);
        }

        String client = requestContext.getHeaderString("X-CLIENT-ID");
        String apiKey = requestContext.getHeaderString("X-API-KEY");
        if (iamApiClient.validateApiKey(client, apiKey) == null) {
            return Response.status(Response.Status.UNAUTHORIZED).entity("Invalid/Missing API Key");
        } else {
            return context.proceed();
        }
    }
}
