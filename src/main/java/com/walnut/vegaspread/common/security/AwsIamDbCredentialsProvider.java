package com.walnut.vegaspread.common.security;

import io.quarkus.credentials.CredentialsProvider;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Named;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.rds.RdsUtilities;
import software.amazon.awssdk.services.rds.model.GenerateAuthenticationTokenRequest;

import java.util.Map;
import java.util.Optional;

@ApplicationScoped
@Named("aws-iam-provider")
public class AwsIamDbCredentialsProvider implements CredentialsProvider {
    @ConfigProperty(name = "quarkus.datasource.jdbc.url")
    Optional<String> jdbcUrlOpt;

    @ConfigProperty(name = "quarkus.datasource.username")
    Optional<String> dbUserOpt;

    @ConfigProperty(name = "vega.cloud.region")
    Optional<String> awsRegionOpt;

    /**
     * <PERSON><PERSON>rk<PERSON> will call this on each new connection (so tokens stay fresh).
     *
     * @param name the credentials‑provider name from quarkus.datasource.credentials-provider
     * @return a map containing "user" and "password" entries
     */
    @Override
    public Map<String, String> getCredentials(String name) {
        if (jdbcUrlOpt.isEmpty() || dbUserOpt.isEmpty()) {
            throw new IllegalStateException("JDBC URL or DB user is not configured.");
        }
        String jdbcUrl = jdbcUrlOpt.get();
        String dbUser = dbUserOpt.get();
        String awsRegion = awsRegionOpt.orElse("ap-southeast-1");
        String dbHost = jdbcUrl.split("//")[1].split(":")[0];
        String dbPort = jdbcUrl.split("//")[1].split(":")[1].split("/")[0];

        RdsUtilities utilities = RdsUtilities.builder()
                .region(Region.of(awsRegion))
                .credentialsProvider(DefaultCredentialsProvider.create())
                .build();

        // 2) Generate a short‑lived IAM auth token
        String authToken = utilities.generateAuthenticationToken(
                GenerateAuthenticationTokenRequest.builder()
                        .hostname(dbHost)
                        .port(Integer.parseInt(dbPort))
                        .username(dbUser)
                        .build()
        );

        // 3) Return the credentials map
        return Map.of(
                CredentialsProvider.USER_PROPERTY_NAME, dbUser,
                CredentialsProvider.PASSWORD_PROPERTY_NAME, authToken
        );
    }
}
