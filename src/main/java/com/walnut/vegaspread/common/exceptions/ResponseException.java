package com.walnut.vegaspread.common.exceptions;

import io.quarkus.runtime.annotations.RegisterForReflection;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

/**
 * Utility class for creating and throwing WebApplicationException with Response status and error messages.
 */
public class ResponseException {
    /**
     * Creates and throws a WebApplicationException with the specified status and error message.
     * This method never returns normally - it always throws an exception.
     *
     * @param status       The HTTP response status to use
     * @param errorMessage The error message to include in the response
     * @param <T>          The expected return type (for compilation purposes only)
     * @return Never returns - always throws WebApplicationException
     * @throws WebApplicationException Always throws this exception with the built response
     */
    public static <T> T throwResponseException(Response.Status status, String errorMessage) {
        Response.ResponseBuilder responseBuilder = Response.status(status)
                .entity(new ErrorResponse(errorMessage))
                .type(MediaType.APPLICATION_JSON);
        throw new WebApplicationException(responseBuilder.build());
    }

    @RegisterForReflection
    public record ErrorResponse(String message) {
    }
}
