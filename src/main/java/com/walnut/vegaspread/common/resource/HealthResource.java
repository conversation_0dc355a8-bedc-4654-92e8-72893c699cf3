package com.walnut.vegaspread.common.resource;

import com.walnut.vegaspread.common.security.ApiKeyAuthenticate;
import com.walnut.vegaspread.common.utils.ConfigKeys;
import io.quarkus.security.Authenticated;
import jakarta.enterprise.inject.Instance;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.UriInfo;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.resteasy.reactive.RestResponse;

import java.net.URI;
import java.util.Optional;

@Path("/health")
public class HealthResource {

    private final Instance<EntityManager> entityManager;
    @ConfigProperty(name = ConfigKeys.VEGA_APP_VERSION)
    Optional<String> vegaAppVersion;
    @ConfigProperty(name = ConfigKeys.APP_VERSION)
    String appVersion;

    public HealthResource(Instance<EntityManager> entityManager) {
        this.entityManager = entityManager;
    }

    @GET
    @Path("/ready")
    public String ready() {
        return "OK";
    }

    @GET
    @Path("/base-url")
    public URI baseUrl(@Context UriInfo uriInfo) {
        return uriInfo.getBaseUri();
    }

    @Authenticated
    @GET
    @Path("/authenticated")
    public String authenticated() {
        return "OK";
    }

    @ApiKeyAuthenticate
    @GET
    @Path("/api-key-authenticated")
    public String apiKeyAuthenticated() {
        return "OK";
    }

    @GET
    @Path("/app-version")
    public String appVersion() {
        return vegaAppVersion.orElse(appVersion);
    }

    @GET
    @Path("/db")
    public RestResponse<String> dbReady() {
        if (!entityManager.isResolvable()) {
            return RestResponse.status(RestResponse.Status.SERVICE_UNAVAILABLE, "EntityManager not available");
        }
        try {
            Query query = entityManager.get().createNativeQuery("SELECT 1");
            query.setHint("jakarta.persistence.query.timeout", 100); // in milliseconds
            query.getSingleResult();

            return RestResponse.ok("DB ready");
        } catch (Exception e) {
            return RestResponse.status(RestResponse.Status.INTERNAL_SERVER_ERROR, "DB not ready");
        }
    }
}
