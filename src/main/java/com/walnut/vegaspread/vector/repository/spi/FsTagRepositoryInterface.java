package com.walnut.vegaspread.vector.repository.spi;

import com.walnut.vegaspread.vector.entity.FsTagEntity;
import com.walnut.vegaspread.vector.model.FsTagDto;

import java.util.List;
import java.util.UUID;

public interface FsTagRepositoryInterface {
    List<FsTagEntity> findSimilar(FsTagDto.Query queryDto);

    long deleteByDocId(UUID docId);

    long deleteByBlockId(Integer blockId);

    void persist(List<FsTagEntity> fsTags);
}
