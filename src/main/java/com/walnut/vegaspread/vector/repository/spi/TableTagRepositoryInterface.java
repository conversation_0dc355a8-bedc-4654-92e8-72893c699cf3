package com.walnut.vegaspread.vector.repository.spi;

import com.walnut.vegaspread.vector.entity.TableTagEntity;
import com.walnut.vegaspread.vector.model.TableTagDto;

import java.util.List;
import java.util.UUID;

public interface TableTagRepositoryInterface {
    TableTagDto.QueryResponse findSimilar(TableTagDto.Query queryDto);

    long deleteByDocId(UUID docId);

    long deleteByBlockId(Integer blockId);

    void persist(List<TableTagEntity> tableTags);
}
