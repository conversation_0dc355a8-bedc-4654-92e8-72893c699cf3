package com.walnut.vegaspread.vector.repository.impl.aws;

import com.walnut.vegaspread.vector.entity.TableTagEntity;
import com.walnut.vegaspread.vector.model.TableTagDto;
import com.walnut.vegaspread.vector.model.TableTagMappingDto;
import com.walnut.vegaspread.vector.repository.spi.TableTagRepositoryInterface;
import com.walnut.vegaspread.vector.service.Utils;
import com.walnut.vegaspread.vector.utils.Config;
import io.quarkus.arc.profile.IfBuildProfile;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.transaction.Transactional;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@ApplicationScoped
@IfBuildProfile("aws")
public class AwsTableTagRepository implements TableTagRepositoryInterface {
    private final String schema;
    private final EntityManager em;

    @Inject
    public AwsTableTagRepository(
            @ConfigProperty(name = Config.DEFAULT_SCHEMA) Optional<String> schema,
            EntityManager em) {
        if (schema.isEmpty()) {
            throw new IllegalStateException("Schema is not configured");
        }
        this.schema = schema.get();
        this.em = em;
    }

    private String getQuery(TableTagDto.Query queryDto) {
        String internalQuery = """
                SELECT
                    %s AS block_id,
                    %s AS doc_id,
                    %s AS tag_id,
                    %s <=> CAST(:queryVector AS vector) AS dist
                FROM %s.%s
                """.formatted(TableTagEntity.BLOCK_ID_COL_NAME,
                TableTagEntity.DOC_ID_COL_NAME,
                TableTagEntity.TAG_ID_COL_NAME,
                TableTagEntity.EMBEDDING_COL_NAME,
                this.schema,
                TableTagEntity.TABLE_NAME);
        if (queryDto.tagId() != null) {
            internalQuery += " WHERE tag_id LIKE CONCAT('%', :tagId, '%') ";
        }

        return """
                WITH scored AS (
                  %s
                )
                SELECT
                  block_id,
                  doc_id,
                  tag_id,
                  1 - dist AS cos_score
                FROM scored
                WHERE 1 - dist > :scoreThreshold
                ORDER BY dist
                LIMIT :count
                """.formatted(internalQuery);
    }

    @Override
    public TableTagDto.QueryResponse findSimilar(TableTagDto.Query queryDto) {
        String queryString = getQuery(queryDto);

        Query query = em.createNativeQuery(queryString, "TableTagDtoMapping")
                .setParameter("queryVector", Utils.toFloatArray(queryDto.queryVector()))
                .setParameter("scoreThreshold", queryDto.scoreThreshold())
                .setParameter("count", queryDto.count());
        if (queryDto.tagId() != null) {
            query.setParameter("tagId", queryDto.tagId());
        }
        @SuppressWarnings("unchecked")
        List<TableTagMappingDto> results = query.getResultList();
        return new TableTagDto.QueryResponse(
                results.stream().map(result -> new TableTagDto.QueryResponseItem(result.blockId(), result.docId(),
                                result.tagId(), result.cosScore()))
                        .toList());
    }

    @Override
    @Transactional
    public long deleteByDocId(UUID docId) {

        String queryBuilder = String.format("DELETE FROM %s.%s WHERE doc_id = :docId",
                schema,
                TableTagEntity.TABLE_NAME);

        Query query = em.createNativeQuery(queryBuilder);
        query.setParameter("docId", docId);
        return query.executeUpdate();
    }

    @Override
    @Transactional
    public long deleteByBlockId(Integer blockId) {
        String queryBuilder = String.format("DELETE FROM %s.%s WHERE block_id = :blockId",
                schema,
                TableTagEntity.TABLE_NAME);
        Query query = em.createNativeQuery(queryBuilder);
        query.setParameter("blockId", blockId);
        return query.executeUpdate();
    }

    @Transactional
    public void persist(List<TableTagEntity> tableTags) {
        if (tableTags == null || tableTags.isEmpty()) {
            return;
        }

        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("INSERT INTO ")
                .append(schema)
                .append(".")
                .append(TableTagEntity.TABLE_NAME)
                .append(" (block_id, doc_id, last_modified_time, embedding, tag_id) VALUES ");

        // Build the VALUES part
        for (int i = 0; i < tableTags.size(); i++) {
            queryBuilder.append("(")
                    .append(":blockId").append(i).append(", ")
                    .append(":docId").append(i).append(", ")
                    .append("NOW(), ")
                    .append("vector(:embedding").append(i).append("), ")
                    .append(":tagId").append(i)
                    .append(")");

            if (i < tableTags.size() - 1) {
                queryBuilder.append(", ");
            }
        }

        Query query = em.createNativeQuery(queryBuilder.toString());

        // Set all parameters
        for (int i = 0; i < tableTags.size(); i++) {
            TableTagEntity tag = tableTags.get(i);
            query.setParameter("blockId" + i, tag.getBlockId());
            query.setParameter("docId" + i, tag.getDocId());
            query.setParameter("embedding" + i, Utils.toVectorString(tag.getEmbedding()));
            query.setParameter("tagId" + i, tag.getTagId());
        }

        query.executeUpdate();
    }
}
