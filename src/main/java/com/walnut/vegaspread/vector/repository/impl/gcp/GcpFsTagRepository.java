package com.walnut.vegaspread.vector.repository.impl.gcp;

import com.walnut.vegaspread.vector.entity.FsTagEntity;
import com.walnut.vegaspread.vector.model.FsTagDto;
import com.walnut.vegaspread.vector.model.FsTagMappingDto;
import com.walnut.vegaspread.vector.repository.spi.FsTagRepositoryInterface;
import com.walnut.vegaspread.vector.service.Utils;
import io.quarkus.arc.profile.UnlessBuildProfile;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.transaction.Transactional;

import java.util.List;
import java.util.UUID;

@ApplicationScoped
@UnlessBuildProfile("aws")
public class GcpFsTagRepository implements FsTagRepositoryInterface {
    private final EntityManager em;

    @Inject
    public GcpFsTagRepository(EntityManager em) {
        this.em = em;
    }

    @Override
    public List<FsTagEntity> findSimilar(FsTagDto.Query queryDto) {
        String query = """
                WITH scored AS (
                  SELECT
                    %1$s      AS block_id,
                    %2$s      AS doc_id,
                    %3$s      AS tag_id,
                    %4$s      AS entity_id,
                    %5$s      AS entity_name,
                    %6$s      AS spread_level,
                    %7$s      AS last_modified_time,
                    %8$s      AS embedding,
                    approx_distance(%8$s, string_to_vector(:queryVector), 'distance_measure=cosine') AS dist
                  FROM %9$s
                  WHERE %4$s = :entityId
                )
                SELECT
                  block_id,
                  doc_id,
                  tag_id,
                  vector_to_string(embedding) AS embedding,
                  entity_id,
                  entity_name,
                  spread_level,
                  last_modified_time,
                  1 - dist AS cos_score
                FROM scored
                WHERE 1 - dist > :scoreThreshold
                ORDER BY dist
                LIMIT :count
                """.formatted(
                FsTagEntity.BLOCK_ID_COL_NAME,
                FsTagEntity.DOC_ID_COL_NAME,
                FsTagEntity.TAG_ID_COL_NAME,
                FsTagEntity.ENTITY_ID_COL_NAME,
                FsTagEntity.ENTITY_NAME_COL_NAME,
                FsTagEntity.SPREAD_LEVEL_COL_NAME,
                FsTagEntity.LAST_MODIFIED_TIME_COL_NAME,
                FsTagEntity.EMBEDDING_COL_NAME,
                FsTagEntity.TABLE_NAME
        );

        @SuppressWarnings("unchecked")
        List<FsTagMappingDto> results = em.createNativeQuery(query, "FsTagDtoMapping")
                .setParameter("entityId", queryDto.entityId())
                .setParameter("queryVector", Utils.toVectorString(queryDto.queryVector()))
                .setParameter("scoreThreshold", queryDto.scoreThreshold())
                .setParameter("count", queryDto.count())
                .getResultList();
        return results.stream().map(result -> new FsTagEntity(result.blockId(), result.docId(),
                        Utils.fromVectorString(result.embedding()), result.tagId(), result.entityId(),
                        result.entityName(), result.spreadLevel(), result.lastModifiedTime()))
                .toList();
    }

    @Override
    @Transactional
    public void persist(List<FsTagEntity> fsTags) {
        if (fsTags == null || fsTags.isEmpty()) {
            return;
        }

        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("INSERT INTO ")
                .append(FsTagEntity.TABLE_NAME)
                .append(" (block_id, doc_id, last_modified_time, embedding, tag_id, entity_id, entity_name, " +
                        "spread_level) VALUES ");

        // Build the VALUES part
        for (int i = 0; i < fsTags.size(); i++) {
            queryBuilder.append("(")
                    .append(":blockId").append(i).append(", ")
                    .append("UNHEX(REPLACE(:docId").append(i).append(", '-', '')), ")
                    .append("NOW(), ")
                    .append("string_to_vector(:embedding").append(i).append("), ")
                    .append(":tagId").append(i).append(", ")
                    .append(":entityId").append(i).append(", ")
                    .append(":entityName").append(i).append(", ")
                    .append(":spreadLevel").append(i).append(")");

            if (i < fsTags.size() - 1) {
                queryBuilder.append(", ");
            }
        }

        Query query = em.createNativeQuery(queryBuilder.toString());

        // Set all parameters
        for (int i = 0; i < fsTags.size(); i++) {
            FsTagEntity tag = fsTags.get(i);
            query.setParameter("blockId" + i, tag.getBlockId());
            query.setParameter("docId" + i, tag.getDocId().toString());
            query.setParameter("embedding" + i, Utils.toVectorString(tag.getEmbedding()));
            query.setParameter("tagId" + i, tag.getTagId());
            query.setParameter("entityId" + i, tag.getEntityId());
            query.setParameter("entityName" + i, tag.getEntityName());
            query.setParameter("spreadLevel" + i, tag.getSpreadLevel());
        }

        query.executeUpdate();
    }

    @Override
    @Transactional
    public long deleteByDocId(UUID docId) {
        String queryBuilder = String.format("DELETE FROM %s WHERE doc_id = UNHEX(REPLACE(:docId, '-', ''))",
                FsTagEntity.TABLE_NAME);
        Query query = em.createNativeQuery(queryBuilder);
        query.setParameter("docId", docId.toString());
        return query.executeUpdate();
    }

    @Override
    @Transactional
    public long deleteByBlockId(Integer blockId) {
        String queryBuilder = String.format("DELETE FROM %s WHERE block_id = :blockId", FsTagEntity.TABLE_NAME);
        Query query = em.createNativeQuery(queryBuilder);
        query.setParameter("blockId", blockId);
        return query.executeUpdate();
    }
}
