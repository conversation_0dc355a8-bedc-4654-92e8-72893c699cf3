package com.walnut.vegaspread.vector.repository.impl.aws;

import com.walnut.vegaspread.vector.entity.FsTagEntity;
import com.walnut.vegaspread.vector.model.FsTagDto;
import com.walnut.vegaspread.vector.model.FsTagMappingDto;
import com.walnut.vegaspread.vector.repository.spi.FsTagRepositoryInterface;
import com.walnut.vegaspread.vector.service.Utils;
import com.walnut.vegaspread.vector.utils.Config;
import io.quarkus.arc.profile.IfBuildProfile;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.transaction.Transactional;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@ApplicationScoped
@IfBuildProfile("aws")
public class AwsFsTagRepository implements FsTagRepositoryInterface {
    private final String schema;
    private final EntityManager em;

    @Inject
    public AwsFsTagRepository(
            @ConfigProperty(name = Config.DEFAULT_SCHEMA) Optional<String> schema,
            EntityManager em) {
        if (schema.isEmpty()) {
            throw new IllegalStateException("Schema is not configured");
        }
        this.schema = schema.get();
        this.em = em;
    }

    @Override
    public List<FsTagEntity> findSimilar(FsTagDto.Query queryDto) {
        String query = """
                SELECT *
                FROM %s.%s
                WHERE %s = :entityId
                AND (1 - (%s <=> CAST(:queryVector AS vector))) > :scoreThreshold
                LIMIT :count
                """.formatted(
                this.schema,
                FsTagEntity.TABLE_NAME,
                FsTagEntity.ENTITY_ID_COL_NAME,
                FsTagEntity.EMBEDDING_COL_NAME
        );
        @SuppressWarnings("unchecked")
        List<FsTagMappingDto> results = em.createNativeQuery(query, "FsTagDtoMapping")
                .setParameter("entityId", queryDto.entityId())
                .setParameter("queryVector", Utils.toFloatArray(queryDto.queryVector()))
                .setParameter("scoreThreshold", queryDto.scoreThreshold())
                .setParameter("count", queryDto.count())
                .getResultList();
        return results.stream().map(result -> new FsTagEntity(result.blockId(), result.docId(),
                        Utils.fromVectorString(result.embedding()), result.tagId(), result.entityId(),
                        result.entityName(),
                        result.spreadLevel(), result.lastModifiedTime()))
                .toList();
    }

    @Override
    @Transactional
    public long deleteByDocId(UUID docId) {
        String queryBuilder = String.format("DELETE FROM %s.%s WHERE doc_id = :docId", schema, FsTagEntity.TABLE_NAME);
        Query query = em.createNativeQuery(queryBuilder);
        query.setParameter("docId", docId);
        return query.executeUpdate();
    }

    @Override
    @Transactional
    public long deleteByBlockId(Integer blockId) {
        String queryBuilder = String.format("DELETE FROM %s.%s WHERE block_id = :blockId", schema,
                FsTagEntity.TABLE_NAME);

        Query query = em.createNativeQuery(queryBuilder);
        query.setParameter("blockId", blockId);
        return query.executeUpdate();
    }

    @Override
    @Transactional
    public void persist(List<FsTagEntity> fsTags) {
        if (fsTags == null || fsTags.isEmpty()) {
            return;
        }

        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("INSERT INTO ")
                .append(schema)
                .append(".")
                .append(FsTagEntity.TABLE_NAME)
                .append(" (block_id, doc_id, last_modified_time, embedding, tag_id, entity_id, entity_name, " +
                        "spread_level) VALUES ");

        // Build the VALUES part
        for (int i = 0; i < fsTags.size(); i++) {
            queryBuilder.append("(")
                    .append(":blockId").append(i).append(", ")
                    .append(":docId").append(i).append(", ")
                    .append("NOW(), ")
                    .append("vector(:embedding").append(i).append("), ")
                    .append(":tagId").append(i).append(", ")
                    .append(":entityId").append(i).append(", ")
                    .append(":entityName").append(i).append(", ")
                    .append(":spreadLevel").append(i).append(")");

            if (i < fsTags.size() - 1) {
                queryBuilder.append(", ");
            }
        }

        Query query = em.createNativeQuery(queryBuilder.toString());

        // Set all parameters
        for (int i = 0; i < fsTags.size(); i++) {
            FsTagEntity tag = fsTags.get(i);
            query.setParameter("blockId" + i, tag.getBlockId());
            query.setParameter("docId" + i, tag.getDocId());
            query.setParameter("embedding" + i, Utils.toVectorString(tag.getEmbedding()));
            query.setParameter("tagId" + i, tag.getTagId());
            query.setParameter("entityId" + i, tag.getEntityId());
            query.setParameter("entityName" + i, tag.getEntityName());
            query.setParameter("spreadLevel" + i, tag.getSpreadLevel());
        }
        query.executeUpdate();
    }
}
