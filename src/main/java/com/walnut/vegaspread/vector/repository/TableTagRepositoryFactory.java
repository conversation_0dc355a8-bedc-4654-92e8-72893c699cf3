package com.walnut.vegaspread.vector.repository;

import com.walnut.vegaspread.common.utils.ConfigKeys;
import com.walnut.vegaspread.vector.repository.spi.TableTagRepositoryInterface;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Instance;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.logging.Logger;

@ApplicationScoped
public class TableTagRepositoryFactory {
    private static final Logger logger = Logger.getLogger(TableTagRepositoryFactory.class);
    @ConfigProperty(name = ConfigKeys.CLOUD_PROVIDER_TYPE)
    String cloudProviderType;
    Instance<TableTagRepositoryInterface> tableTagRepositories;

    public TableTagRepositoryFactory(Instance<TableTagRepositoryInterface> tableTagRepositories) {
        this.tableTagRepositories = tableTagRepositories;
    }

    public TableTagRepositoryInterface getTableTagRepository() {
        logger.debugf("CloudProviderType: " + cloudProviderType.toLowerCase());
        for (TableTagRepositoryInterface tableTagRepository : tableTagRepositories) {
            logger.debugf("FsTagRepository: " + tableTagRepository.getClass().getSimpleName());
            if (tableTagRepository.getClass()
                    .getSimpleName()
                    .toLowerCase()
                    .startsWith(cloudProviderType.toLowerCase())) {
                return tableTagRepository;
            }
        }

        throw new IllegalStateException("No cloud provider repository configured for type: " + cloudProviderType);
    }
}
