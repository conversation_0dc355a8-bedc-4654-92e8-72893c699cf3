package com.walnut.vegaspread.vector.service;

import java.util.List;

public class Utils {

    public static float[] toFloatArray(List<Float> list) {
        float[] floatArray = new float[list.size()];
        int i = 0;
        for (Float f : list) {
            floatArray[i++] = (f != null ? f : Float.NaN);
        }
        return floatArray;
    }

    public static String toVectorString(float[] embedding) {
        if (embedding == null || embedding.length != 768) {
            throw new IllegalArgumentException("Array must be non-null and contain exactly 768 elements.");
        }

        StringBuilder sb = new StringBuilder();
        sb.append('[');
        for (int i = 0; i < embedding.length; i++) {
            sb.append(String.format("%f", embedding[i]));
            if (i < embedding.length - 1) {
                sb.append(',');
            }
        }
        sb.append(']');
        return sb.toString();
    }

    public static String toVectorString(List<Float> embedding) {
        if (embedding == null || embedding.size() != 768) {
            throw new IllegalArgumentException("List must be non-null and contain exactly 768 elements.");
        }

        StringBuilder sb = new StringBuilder();
        sb.append('[');
        for (int i = 0; i < embedding.size(); i++) {
            sb.append(embedding.get(i)); // prints full float
            if (i < embedding.size() - 1) {
                sb.append(',');
            }
        }
        sb.append(']');
        return sb.toString();
    }

    public static float[] fromVectorString(String vectorString) {
        if (vectorString == null || vectorString.isBlank()) {
            throw new IllegalArgumentException("Input string is null or blank");
        }

        // Remove brackets and whitespace
        String trimmed = vectorString.trim();
        if (trimmed.startsWith("[")) trimmed = trimmed.substring(1);
        if (trimmed.endsWith("]")) trimmed = trimmed.substring(0, trimmed.length() - 1);

        String[] tokens = trimmed.split(",");
        if (tokens.length != 768) {
            throw new IllegalArgumentException("Input must contain exactly 768 comma-separated float values");
        }

        float[] result = new float[768];
        for (int i = 0; i < 768; i++) {
            result[i] = Float.parseFloat(tokens[i].trim());
        }

        return result;
    }
}

