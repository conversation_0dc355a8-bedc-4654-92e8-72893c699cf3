package com.walnut.vegaspread.vector.model;

import io.quarkus.runtime.annotations.RegisterForReflection;

import java.time.LocalDateTime;
import java.util.UUID;

@RegisterForReflection
public record FsTagMappingDto(
        Integer blockId,
        UUID docId,
        String embedding,
        Integer tagId,
        Integer entityId,
        String entityName,
        String spreadLevel,
        LocalDateTime lastModifiedTime
) {
}
