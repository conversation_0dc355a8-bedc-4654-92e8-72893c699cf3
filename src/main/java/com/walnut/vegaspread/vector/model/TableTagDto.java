package com.walnut.vegaspread.vector.model;

import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

public interface TableTagDto {
    record Create(Integer blockId, @NotNull UUID docId, @NotNull Integer tagId,
                  List<Float> embedding) implements Serializable {
    }

    record Query(List<Float> queryVector, Integer count, Double scoreThreshold, Integer tagId) implements Serializable {
    }

    record QueryResponse(List<QueryResponseItem> items) implements Serializable {
    }

    record QueryResponseItem(Integer blockId, UUID docId, Integer tagId,
                             Double cosScore) implements Serializable {
    }

    record DeleteDoc(@NotNull UUID docId) implements Serializable {
    }

    record DeleteBlock(@NotNull Integer blockId) implements Serializable {
    }
}
