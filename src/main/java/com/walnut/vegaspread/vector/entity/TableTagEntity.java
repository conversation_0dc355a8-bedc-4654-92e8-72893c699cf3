package com.walnut.vegaspread.vector.entity;

import com.walnut.vegaspread.vector.model.TableTagMappingDto;
import io.quarkus.runtime.annotations.RegisterForReflection;
import jakarta.persistence.Column;
import jakarta.persistence.ColumnResult;
import jakarta.persistence.ConstructorResult;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.SqlResultSetMapping;
import jakarta.persistence.Transient;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.UUID;

@RegisterForReflection
@SqlResultSetMapping(
        name = "TableTagDtoMapping",
        classes = @ConstructorResult(
                targetClass = TableTagMappingDto.class,
                columns = {
                        @ColumnResult(name = TableTagEntity.BLOCK_ID_COL_NAME, type = Integer.class),
                        @ColumnResult(name = TableTagEntity.DOC_ID_COL_NAME, type = UUID.class),
                        @ColumnResult(name = TableTagEntity.TAG_ID_COL_NAME, type = Integer.class),
                        @ColumnResult(name = "cos_score", type = Double.class)
                }
        )
)
@Entity
@Getter
@Setter
@NoArgsConstructor
public class TableTagEntity {
    public static final String TABLE_NAME = "table_tag";
    public static final String BLOCK_ID_COL_NAME = "block_id";
    public static final String DOC_ID_COL_NAME = "doc_id";
    public static final String LAST_MODIFIED_TIME_COL_NAME = "last_modified_time";
    public static final String TAG_ID_COL_NAME = "tag_id";
    public static final String EMBEDDING_COL_NAME = "embedding";

    @Id
    @Column(name = BLOCK_ID_COL_NAME, nullable = false)
    private Integer blockId;

    @NotNull
    @Column(name = DOC_ID_COL_NAME, nullable = false)
    private UUID docId;

    @NotNull
    @Column(name = LAST_MODIFIED_TIME_COL_NAME, nullable = false)
    private LocalDateTime lastModifiedTime;

    @NotNull
    @Column(name = TAG_ID_COL_NAME, nullable = false)
    private Integer tagId;

    /*Marked as transient since we are converting the embedding to/from string before persisting/fetching and entity
   does not support float[]. Since we are using native queries, we can persist the embedding as string.*/
    @Transient
    private float[] embedding;

    public TableTagEntity(
            Integer blockId,
            UUID docId,
            float[] embedding,
            Integer tagId,
            LocalDateTime lastModifiedTime

    ) {
        this.setBlockId(blockId);
        this.setDocId(docId);
        this.setLastModifiedTime(lastModifiedTime);
        this.setTagId(tagId);
        this.embedding = embedding;
    }
}
