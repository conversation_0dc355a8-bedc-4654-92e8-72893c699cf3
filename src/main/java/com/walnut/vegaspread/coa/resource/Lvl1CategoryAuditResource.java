package com.walnut.vegaspread.coa.resource;

import com.walnut.vegaspread.coa.audit.Lvl1CategoryEntityMapper;
import com.walnut.vegaspread.coa.model.Lvl1CategoryAuditDto;
import com.walnut.vegaspread.coa.service.Lvl1CategoryAuditService;
import com.walnut.vegaspread.common.model.audit.envers.AuditRequestDto;
import com.walnut.vegaspread.common.service.audit.envers.GenericAuditService;
import io.quarkus.security.Authenticated;
import jakarta.persistence.EntityManager;
import jakarta.validation.Valid;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

/**
 * REST resource for managing Lvl1CategoryEntity audit operations.
 * This resource provides endpoints for retrieving audit history, listing all audits, and rolling back to previous
 * states.
 */
@Path("lvl1-category/audit")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Authenticated
public class Lvl1CategoryAuditResource {

    private final Lvl1CategoryAuditService auditService;
    private final EntityManager entityManager;
    private final Lvl1CategoryEntityMapper lvl1CategoryEntityMapper;

    public Lvl1CategoryAuditResource(Lvl1CategoryAuditService auditService,
                                     EntityManager entityManager,
                                     Lvl1CategoryEntityMapper lvl1CategoryEntityMapper) {
        this.auditService = auditService;
        this.entityManager = entityManager;
        this.lvl1CategoryEntityMapper = lvl1CategoryEntityMapper;
    }

    @GET
    @Path("/{lvl1CategoryId}")
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<Lvl1CategoryAuditDto>> getAuditForLvl1CategoryId(
            @PathParam("lvl1CategoryId") Integer lvl1CategoryId) {
        return auditService.getAuditForLvl1CategoryId(lvl1CategoryId, lvl1CategoryEntityMapper, entityManager);
    }

    @POST
    @Path("/list")
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<Lvl1CategoryAuditDto>> listAll(
            @Valid AuditRequestDto request) {
        return auditService.getPaginatedAuditsAsDto(request, lvl1CategoryEntityMapper, entityManager);
    }

    @PUT
    @Path("/rollback/{traceId}")
    public void rollback(@PathParam("traceId") String traceId) {
        auditService.rollback(traceId, entityManager);
    }
}
