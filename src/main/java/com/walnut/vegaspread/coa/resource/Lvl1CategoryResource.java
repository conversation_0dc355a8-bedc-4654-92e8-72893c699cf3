package com.walnut.vegaspread.coa.resource;

import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.model.Lvl1CategoryDto;
import com.walnut.vegaspread.coa.service.Lvl1CategoryService;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.jwt.JsonWebToken;

import java.util.List;

import static com.walnut.vegaspread.common.utils.Jwt.getUsername;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/lvl1-category")
public class Lvl1CategoryResource {

    private final Lvl1CategoryService lvl1CategoryService;
    private final JsonWebToken jwt;

    public Lvl1CategoryResource(Lvl1CategoryService lvl1CategoryService, JsonWebToken jwt) {
        this.lvl1CategoryService = lvl1CategoryService;
        this.jwt = jwt;
    }

    @POST
    public List<Lvl1CategoryDto.Response> create(List<String> categories) {
        return Lvl1CategoryEntity.toDtoList(lvl1CategoryService.create(categories, getUsername(jwt)));
    }

    @PATCH
    public List<Lvl1CategoryDto.Response> update(List<Lvl1CategoryDto.Update> updateDtos) {
        return Lvl1CategoryEntity.toDtoList(lvl1CategoryService.update(updateDtos, getUsername(jwt)));
    }

    @DELETE
    public long delete(List<Integer> categoryIds) {
        return lvl1CategoryService.delete(categoryIds);
    }

    @GET
    @Path("/list")
    public List<Lvl1CategoryDto.Response> list() {
        return Lvl1CategoryEntity.toDtoList(lvl1CategoryService.getLvl1Categories());
    }

    @POST
    @Path("/list/ids")
    public List<Lvl1CategoryDto.Response> getLvl1CategoriesByIds(List<Integer> categoryIds) {
        return Lvl1CategoryEntity.toDtoList(lvl1CategoryService.getLvl1CategoriesByIds(categoryIds));
    }
}
