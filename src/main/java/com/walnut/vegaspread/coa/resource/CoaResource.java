package com.walnut.vegaspread.coa.resource;

import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.model.CoaEntityDto;
import com.walnut.vegaspread.coa.model.CoaListDto;
import com.walnut.vegaspread.coa.model.CoaUploadBean;
import com.walnut.vegaspread.coa.service.CoaService;
import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import com.walnut.vegaspread.common.roles.Roles;
import io.quarkus.security.Authenticated;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.jboss.resteasy.reactive.RestForm;
import org.jboss.resteasy.reactive.RestQuery;
import org.jboss.resteasy.reactive.multipart.FileUpload;

import java.io.IOException;
import java.util.List;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("")
@Authenticated
public class CoaResource {

    private final CoaService coaService;

    public CoaResource(CoaService coaService) {
        this.coaService = coaService;
    }

    @POST
    @RolesAllowed(Roles.CREATE_NEW_COA)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/create")
    public List<CoaEntity> create(List<CoaEntityDto.Create> coaEntityCreateDtos, @RestQuery String clientName)
            throws NullPointerException {
        return coaService.create(coaEntityCreateDtos, clientName);
    }

    @PATCH
    @RolesAllowed(Roles.SUPERADMIN)
    @Path("/update")
    public List<CoaEntity> update(@Valid List<CoaEntityDto.@Valid Update> coaEntityUpdateDtos) {
        return coaService.update(coaEntityUpdateDtos);
    }

    @Path("/list")
    @GET
    public List<CoaItemDto> list(@RestQuery String clientName,
                                 @DefaultValue("TRUE") @RestQuery Boolean onlyActive) throws NullPointerException {
        return CoaEntity.toDtoList(coaService.list(clientName, onlyActive));
    }

    @Path("/list")
    @POST
    public List<CoaItemDto> listWithSortAndSearch(CoaListDto.GetSortAndSearchCoaList sortAndSearchDto,
                                                  @RestQuery String clientName,
                                                  @DefaultValue("TRUE") @RestQuery Boolean onlyActive) {
        return CoaEntity.toDtoList(coaService.listWithSortAndSearch(sortAndSearchDto, clientName, onlyActive));
    }

    @Path("/{coaId}")
    @DELETE
    @RolesAllowed(Roles.ADMIN)
    public void delete(@PathParam("coaId") Integer coaId) {
        coaService.delete(coaId);
    }

    @Path("/upload")
    @PUT
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    public List<CoaUploadBean> uploadCsv(
            @RestForm("coa-file") @Schema(implementation = UploadItemSchema.class) FileUpload file) throws IOException {
        return coaService.uploadCsv(file);
    }

    @POST
    @Path("/get")
    public List<CoaItemDto> get(List<Integer> coaIds) {
        return coaService.get(coaIds);
    }

    @Path("/update/upload")
    @PUT
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    public List<CoaEntity> uploadUpdateCsv(
            @RestForm("coa-file") @Schema(implementation = UploadItemSchema.class) FileUpload file) throws IOException {
        return coaService.uploadUpdateCsv(file);
    }

    @Schema(type = SchemaType.STRING, format = "binary")
    public static class UploadItemSchema {
    }
}
