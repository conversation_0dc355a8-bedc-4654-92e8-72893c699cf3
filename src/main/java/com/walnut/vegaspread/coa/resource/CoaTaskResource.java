package com.walnut.vegaspread.coa.resource;

import com.walnut.vegaspread.coa.entity.CoaTaskEntity;
import com.walnut.vegaspread.coa.model.CoaTaskDto;
import com.walnut.vegaspread.coa.model.ListTaskDto;
import com.walnut.vegaspread.coa.model.TaskListResponseDto;
import com.walnut.vegaspread.coa.model.TaskOutputDto;
import com.walnut.vegaspread.coa.service.CoaTaskService;
import com.walnut.vegaspread.common.roles.Roles;
import io.quarkus.security.Authenticated;
import jakarta.annotation.security.RolesAllowed;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/task")
@Authenticated
public class CoaTaskResource {

    private final CoaTaskService coaTaskService;

    public CoaTaskResource(CoaTaskService coaTaskService) {
        this.coaTaskService = coaTaskService;
    }

    @POST
    @RolesAllowed(Roles.MAP_COA)
    public CoaTaskEntity create(CoaTaskDto.NewTask newTask) {
        return coaTaskService.create(newTask);
    }

    @PATCH
    @RolesAllowed(Roles.ADMIN)
    public TaskOutputDto update(CoaTaskDto.UpdateTask updateTask) {
        return coaTaskService.update(updateTask);
    }

    @Path("/{taskId}")
    @GET
    @RolesAllowed(Roles.ADMIN)
    public TaskOutputDto get(Integer taskId) {
        return coaTaskService.get(taskId);
    }

    @Path("/list")
    @POST
    @RolesAllowed(Roles.ADMIN)
    public TaskListResponseDto list(ListTaskDto.GetTaskList taskListDto) {
        return coaTaskService.list(taskListDto, false);
    }
}
