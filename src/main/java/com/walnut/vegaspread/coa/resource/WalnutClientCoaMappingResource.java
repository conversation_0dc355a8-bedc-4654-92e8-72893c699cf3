package com.walnut.vegaspread.coa.resource;

import com.walnut.vegaspread.coa.entity.WalnutClientCoaMappingEntity;
import com.walnut.vegaspread.coa.model.WalnutClientCoaMappingEntityDto;
import com.walnut.vegaspread.coa.service.WalnutClientCoaMappingService;
import com.walnut.vegaspread.common.roles.Roles;
import jakarta.annotation.security.RolesAllowed;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.jboss.resteasy.reactive.RestQuery;

import java.util.List;

/**
 * REST resource for managing walnut and client coa id mappings.
 */
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/walnut-client-mapping")
@RolesAllowed(Roles.SUPERADMIN)
public class WalnutClientCoaMappingResource {

    private final WalnutClientCoaMappingService walnutClientCoaMappingService;

    public WalnutClientCoaMappingResource(WalnutClientCoaMappingService walnutClientCoaMappingService) {
        this.walnutClientCoaMappingService = walnutClientCoaMappingService;
    }

    /**
     * Adds a list of new walnut coa and client coa mappings
     *
     * @param walnutClientCoaMappingEntities The id mappings for the coa.
     * @param clientName                     The name of the client for the mappings.
     * @return The list of newly created mappings.
     */
    @POST
    @Path("")
    @Transactional
    public List<WalnutClientCoaMappingEntity> create(
            List<WalnutClientCoaMappingEntityDto.CreateOrUpdate> walnutClientCoaMappingEntities,
            @RestQuery String clientName) {
        return walnutClientCoaMappingService.create(walnutClientCoaMappingEntities, clientName);
    }

    /**
     * Gets a list of walnut coa and client coa mappings for the given client name.
     *
     * @param clientName The name of the client for which the mappings are requested.
     * @return The list of mappings for the client.
     */
    @GET
    @Path("/{clientName}")
    public List<WalnutClientCoaMappingEntity> get(@PathParam("clientName") String clientName) {
        return walnutClientCoaMappingService.get(clientName);
    }
}
