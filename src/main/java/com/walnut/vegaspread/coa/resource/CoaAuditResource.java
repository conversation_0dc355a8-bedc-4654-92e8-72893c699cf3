package com.walnut.vegaspread.coa.resource;

import com.walnut.vegaspread.coa.audit.CoaEntityMapper;
import com.walnut.vegaspread.coa.service.CoaAuditService;
import com.walnut.vegaspread.common.model.audit.envers.AuditRequestDto;
import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import com.walnut.vegaspread.common.service.audit.envers.GenericAuditService;
import io.quarkus.security.Authenticated;
import jakarta.persistence.EntityManager;
import jakarta.validation.Valid;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

/**
 * CoA Audit Resource with unified endpoint for filtering, sorting, and pagination.
 * Returns DTOs instead of entities for consistency with other endpoints.
 */
@Path("/audit")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Authenticated
public class CoaAuditResource {

    private final CoaAuditService auditService;
    private final EntityManager entityManager;
    private final CoaEntityMapper coaEntityMapper;

    public CoaAuditResource(CoaAuditService auditService,
                            EntityManager entityManager,
                            CoaEntityMapper coaEntityMapper) {
        this.auditService = auditService;
        this.entityManager = entityManager;
        this.coaEntityMapper = coaEntityMapper;
    }

    @GET
    @Path("/{coaId}")
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> getAuditForCoaId(
            @PathParam("coaId") Integer coaId) {
        return auditService.getAuditForCoaId(coaId, coaEntityMapper, entityManager);
    }

    @POST
    @Path("/list")
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> listAll(
            @Valid AuditRequestDto request) {
        return auditService.getPaginatedAuditsAsDto(request, coaEntityMapper, entityManager);
    }

    @PUT
    @Path("/rollback/{traceId}")
    public void rollback(@PathParam("traceId") String traceId) {
        auditService.rollback(traceId, entityManager);
    }
}
