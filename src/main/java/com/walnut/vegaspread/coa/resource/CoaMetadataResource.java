package com.walnut.vegaspread.coa.resource;

import com.walnut.vegaspread.coa.service.CoaMetadataService;
import com.walnut.vegaspread.common.roles.Roles;
import io.quarkus.security.Authenticated;
import jakarta.annotation.security.RolesAllowed;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/metadata")
@Authenticated
public class CoaMetadataResource {

    private final CoaMetadataService coaMetadataService;

    public CoaMetadataResource(CoaMetadataService coaMetadataService) {
        this.coaMetadataService = coaMetadataService;
    }

    @Path("/client/list")
    @GET
    @RolesAllowed({Roles.SUPERADMIN, Roles.CHECKER_LVL_2, Roles.CHECKER_LVL_1})
    public List<String> getClientList() {
        return coaMetadataService.getClientList();
    }

    @Path("/{clientName}/lvl1Category/list")
    @GET
    public List<String> getLvl1CategoriesforClient(@PathParam("clientName") String clientName) {
        return coaMetadataService.getLvl1CategoriesforClient(clientName);
    }
}
