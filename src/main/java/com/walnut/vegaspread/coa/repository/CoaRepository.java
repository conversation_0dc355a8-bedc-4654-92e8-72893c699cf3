package com.walnut.vegaspread.coa.repository;

import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.model.CoaListDto.GetSortAndSearchCoaList;
import com.walnut.vegaspread.coa.model.SortDto;
import com.walnut.vegaspread.coa.utils.QueryBuilder;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

@ApplicationScoped
public class CoaRepository implements PanacheRepositoryBase<CoaEntity, Integer> {
    public static final SortDto DEFAULT_COA_LIST_SORT = new SortDto("clientName", "ASC");

    private String strToLike(String str) {
        return "%" + str + "%";
    }

    public List<CoaEntity> listActiveForClient(String clientName) {
        return list("isActive = ?1 AND clientName LIKE ?2", Boolean.TRUE, strToLike(clientName));
    }

    public List<CoaEntity> listAllForClient(String clientName) {
        return list("clientName LIKE ?1", strToLike(clientName));
    }

    public List<CoaEntity> listActiveForClient(String clientName, GetSortAndSearchCoaList sortAndSearchDto) {
        return list("isActive = ?1 AND clientName LIKE ?2 AND (coaText LIKE ?3 OR coaDescription LIKE ?3)",
                QueryBuilder.sort(sortAndSearchDto.sort(), DEFAULT_COA_LIST_SORT), Boolean.TRUE, strToLike(clientName),
                strToLike(sortAndSearchDto.search()));
    }

    public List<CoaEntity> listAllForClient(String clientName, GetSortAndSearchCoaList sortAndSearchDto) {
        return list("clientName LIKE ?1 AND (coaText LIKE ?2 OR coaDescription LIKE ?2)",
                QueryBuilder.sort(sortAndSearchDto.sort(), DEFAULT_COA_LIST_SORT), strToLike(clientName),
                strToLike(sortAndSearchDto.search()));
    }

    public List<CoaEntity> findByIds(List<Integer> coaIds) {
        return list("coaId in ?1", coaIds);
    }
}
