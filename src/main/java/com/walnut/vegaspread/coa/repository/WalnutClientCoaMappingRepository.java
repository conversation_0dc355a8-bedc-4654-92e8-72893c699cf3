package com.walnut.vegaspread.coa.repository;

import com.walnut.vegaspread.coa.entity.WalnutClientCoaMappingEntity;
import com.walnut.vegaspread.coa.primarykey.WalnutClientCoaMappingPk;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

@ApplicationScoped
public class WalnutClientCoaMappingRepository implements PanacheRepositoryBase<WalnutClientCoaMappingEntity,
        WalnutClientCoaMappingPk> {

    public void deleteByWalnutCoaIdAndClientName(List<Integer> walnutCoaId, String clientName) {
        delete("mapping.walnutCoaId in ?1 and mapping.clientName = ?2", walnutCoaId, clientName);
    }

    public List<WalnutClientCoaMappingEntity> findByClientName(String clientName) {
        return find("mapping.clientName = ?1", clientName).list();
    }
}
