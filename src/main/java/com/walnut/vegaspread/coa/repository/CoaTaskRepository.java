package com.walnut.vegaspread.coa.repository;

import com.walnut.vegaspread.coa.entity.CoaTaskEntity;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

@ApplicationScoped
public class CoaTaskRepository implements PanacheRepositoryBase<CoaTaskEntity, Integer> {
    public List<CoaTaskEntity> findByIds(List<Integer> taskIds, Sort sort) {
        return find("id in ?1", sort, taskIds).list();
    }

    public List<CoaTaskEntity> findBySpreadIds(List<Integer> spreadIds) {
        return find("spreadId in ?1", spreadIds).list();
    }
}
