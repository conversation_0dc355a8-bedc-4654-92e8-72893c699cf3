package com.walnut.vegaspread.coa.repository;

import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

@ApplicationScoped
public class Lvl1CategoryRepository implements PanacheRepositoryBase<Lvl1CategoryEntity, Integer> {
    public List<Lvl1CategoryEntity> findByIds(List<Integer> categoryIds) {
        return list("id in ?1", categoryIds);
    }

    public long deleteByIds(List<Integer> categoryIds) {
        return delete("id in ?1", categoryIds);
    }

    public List<Lvl1CategoryEntity> findByCategories(List<String> categories) {
        return list("category in ?1", categories);
    }
}
