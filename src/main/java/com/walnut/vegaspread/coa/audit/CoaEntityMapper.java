package com.walnut.vegaspread.coa.audit;

import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.common.model.audit.envers.BaseEntityMapper;
import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(componentModel = "cdi")
public interface CoaEntityMapper extends BaseEntityMapper<CoaEntity, CoaItemDto> {

    @Named("mapLvl1CategoryId")
    static Integer mapLvl1CategoryId(Lvl1CategoryEntity lvl1Category) {
        return lvl1Category == null ? null : lvl1Category.getId();
    }

    @Named("mapLvl1CategoryName")
    static String mapLvl1CategoryName(Lvl1CategoryEntity lvl1Category) {
        return lvl1Category == null ? null : lvl1Category.getCategory();
    }

    @Override
    @Mapping(target = "lvl1CategoryId", source = "lvl1Category", qualifiedByName = "mapLvl1CategoryId")
    @Mapping(target = "lvl1CategoryName", source = "lvl1Category", qualifiedByName = "mapLvl1CategoryName")
    CoaItemDto toDto(CoaEntity entity);
}