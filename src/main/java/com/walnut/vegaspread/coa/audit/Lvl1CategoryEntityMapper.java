package com.walnut.vegaspread.coa.audit;

import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.model.Lvl1CategoryAuditDto;
import com.walnut.vegaspread.common.model.audit.envers.BaseEntityMapper;
import org.mapstruct.Mapper;

@Mapper(componentModel = "cdi")
public interface Lvl1CategoryEntityMapper extends BaseEntityMapper<Lvl1CategoryEntity, Lvl1CategoryAuditDto> {

    @Override
    Lvl1CategoryAuditDto toDto(Lvl1CategoryEntity entity);
}