package com.walnut.vegaspread.coa.primarykey;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
@ToString
@Embeddable
public class WalnutClientCoaMappingPk implements Serializable {

    public static final String WALNUT_COA_ID_COL_NAME = "walnut_coa_id";
    public static final String CLIENT_COA_ID_COL_NAME = "client_coa_id";
    public static final String CLIENT_NAME_COL_NAME = "client_name";

    @Column(name = WALNUT_COA_ID_COL_NAME, nullable = false)
    public Integer walnutCoaId;
    @Column(name = CLIENT_COA_ID_COL_NAME, nullable = false)
    public Integer clientCoaId;
    @Column(name = CLIENT_NAME_COL_NAME, nullable = false)
    public String clientName;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof WalnutClientCoaMappingPk that)) return false;
        return Objects.equals(walnutCoaId, that.walnutCoaId) && Objects.equals(clientCoaId,
                that.clientCoaId) && Objects.equals(clientName, that.clientName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(walnutCoaId, clientCoaId, clientName);
    }
}
