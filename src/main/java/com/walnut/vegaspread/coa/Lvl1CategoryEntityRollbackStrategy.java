package com.walnut.vegaspread.coa;

import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.repository.Lvl1CategoryRepository;
import com.walnut.vegaspread.coa.service.CoaService;
import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.common.service.audit.envers.EntityRollbackStrategy;
import com.walnut.vegaspread.common.service.audit.envers.UserContextService;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.core.Response;
import org.hibernate.envers.RevisionType;

import java.time.LocalDateTime;

/**
 * Lvl1CategoryEntity-specific rollback strategy implementation.
 * This handles the specific business rules and field mapping for Lvl1CategoryEntity rollback operations.
 */
@ApplicationScoped
public class Lvl1CategoryEntityRollbackStrategy implements EntityRollbackStrategy<Lvl1CategoryEntity, Integer> {

    private final Lvl1CategoryRepository lvl1CategoryRepository;
    private final UserContextService userContextService;
    private final CoaService coaService;

    public Lvl1CategoryEntityRollbackStrategy(Lvl1CategoryRepository lvl1CategoryRepository,
                                              UserContextService userContextService, CoaService coaService) {
        this.lvl1CategoryRepository = lvl1CategoryRepository;
        this.userContextService = userContextService;
        this.coaService = coaService;
    }

    @Override
    public Class<Lvl1CategoryEntity> getEntityClass() {
        return Lvl1CategoryEntity.class;
    }

    @Override
    public Lvl1CategoryEntity findById(Integer entityId) {
        return lvl1CategoryRepository.findById(entityId);
    }

    @Override
    public void persist(Lvl1CategoryEntity entity) {
        lvl1CategoryRepository.persist(entity);
    }

    @Override
    public void delete(Integer entityId) {
        Lvl1CategoryEntity entity = lvl1CategoryRepository.findById(entityId);
        if (entity != null) {
            lvl1CategoryRepository.delete(entity);
        }
    }

    @Override
    public Integer getEntityId(Lvl1CategoryEntity entity) {
        return entity.getId();
    }

    @Override
    public Lvl1CategoryEntity createNewEntity() {
        return new Lvl1CategoryEntity();
    }

    @Override
    public void copyAuditedFields(Lvl1CategoryEntity source, Lvl1CategoryEntity target, RevisionType revisionType,
                                  boolean isEntityCreation) {
        // Copy @Audited fields
        target.setCategory(source.getCategory());
        target.setLastModifiedBy(source.getLastModifiedBy());

        // Handle @NotAudited fields specially
        String currentUsername = userContextService.getCurrentUsername();
        LocalDateTime now = LocalDateTime.now();
        if (isEntityCreation) {
            target.setCreatedBy(currentUsername);
            target.setCreatedTime(now);
        }
        target.setLastModifiedTime(now);
        target.setLastModifiedBy(currentUsername);
    }

    @Override
    public boolean isRollbackAllowed(Integer entityId, RevisionType revisionType) {
        if (revisionType == RevisionType.DEL) {
            coaService.findWithLvl1CategoryId(entityId).forEach(coaEntity -> {
                if (coaEntity.getIsActive()) {
                    ResponseException.throwResponseException(Response.Status.CONFLICT,
                            "Cannot rollback deletion of active Lvl1CategoryEntity with id " + entityId);
                }
            });
        }
        return true;
    }

    @Override
    public String getEntityTypeName() {
        return "Lvl1CategoryEntity";
    }
}
