package com.walnut.vegaspread.coa.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class CoaUploadBean {
    private static final String COA_TEXT_CSV_COL_NAME = "coaText";
    private static final String COA_DESCRIPTION_CSV_ROW_NAME = "coaDescription";
    private static final String CLIENT_NAME_CSV_COL_NAME = "clientName";
    private static final String IS_ACTIVE_CSV_COL_NAME = "isActive";
    private static final String LVL1_CATEGORY_CSV_COL_NAME = "lvl1Category";
    private static final String SIGN_CSV_COL_NAME = "sign";

    @JsonProperty(COA_TEXT_CSV_COL_NAME)
    private String coaText;

    @JsonProperty(COA_DESCRIPTION_CSV_ROW_NAME)
    private String coaDescription;

    @JsonProperty(CLIENT_NAME_CSV_COL_NAME)
    private String clientName;

    @JsonProperty(LVL1_CATEGORY_CSV_COL_NAME)
    private String lvl1Category;

    @JsonProperty(IS_ACTIVE_CSV_COL_NAME)
    private Boolean isActive;

    @JsonProperty(SIGN_CSV_COL_NAME)
    private Boolean sign;
}
