package com.walnut.vegaspread.coa.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@AllArgsConstructor
@Getter
public enum FilterByEnum {
    CREATED_BY("createdBy"),
    CREATED_TIME("createdTime"),
    REVIEWED_BY("reviewedBy"),
    REVIEWED_TIME("reviewedTime"),
    REGION_NAME("regionName");

    private final String value;

    public static FilterByEnum fromString(String value) {
        return Arrays.stream(FilterByEnum.values())
                .filter(column -> column.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("No FilterColumnEnum with value " + value));
    }

}
