package com.walnut.vegaspread.coa.model;

import java.io.Serializable;
import java.time.LocalDateTime;

public interface Lvl1CategoryDto {
    record Response(Integer id, String category, String createdBy, LocalDateTime createdTime,
                    String lastModifiedBy, LocalDateTime lastModifiedTime) implements Serializable {
    }

    record Update(Integer id, String category) implements Serializable {
    }
}
