package com.walnut.vegaspread.coa.model;

import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.util.Optional;

/**
 * DTO for {@link com.walnut.vegaspread.coa.entity.CoaEntity}
 */
public interface CoaEntityDto {

    record Create(@NotNull String coaText, @NotNull String coaDescription, @NotNull Integer lvl1CategoryId,
                  @NotNull Boolean isActive, @NotNull Boolean sign) implements Serializable {
    }

    record Update(@NotNull int coaId, Optional<String> coaText, Optional<String> coaDescription,
                  Optional<Integer> lvl1CategoryId, Optional<Boolean> isActive,
                  Optional<Boolean> sign) implements Serializable {
    }
}
