package com.walnut.vegaspread.coa.model;

import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.util.List;

public interface ListTaskDto {
    record SortDto(@NotNull String sortBy, @NotNull String sortType) {
    }

    record FilterDto(@NotNull String filterBy, @NotNull String filterValue) implements Serializable {
    }

    record GetTaskList(int pageNumber, int pageSize, @NotNull List<FilterDto> filter, @NotNull List<SortDto> sort,
                       @NotNull String search) {
    }
}
