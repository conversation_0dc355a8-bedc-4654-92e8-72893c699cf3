package com.walnut.vegaspread.coa.entity;

import com.walnut.vegaspread.coa.primarykey.WalnutClientCoaMappingPk;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Entity
@Table(name = WalnutClientCoaMappingEntity.WALNUT_CLIENT_COA_MAPPING_TABLE_NAME, uniqueConstraints =
        {@UniqueConstraint(columnNames = {WalnutClientCoaMappingPk.WALNUT_COA_ID_COL_NAME,
                WalnutClientCoaMappingPk.CLIENT_NAME_COL_NAME})})
public class WalnutClientCoaMappingEntity {

    public static final String WALNUT_CLIENT_COA_MAPPING_TABLE_NAME = "walnut_client_coa_mapping";

    @EmbeddedId
    private WalnutClientCoaMappingPk mapping;
}
