package com.walnut.vegaspread.coa.entity;

import com.walnut.vegaspread.coa.model.Lvl1CategoryDto;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = Lvl1CategoryEntity.TABLE_NAME, uniqueConstraints = {
        @UniqueConstraint(name = "uk_lvl1_category_category", columnNames = {Lvl1CategoryEntity.CATEGORY_COL_NAME})
})
@Audited
public class Lvl1CategoryEntity {
    public static final String TABLE_NAME = "lvl1_category";
    public static final String ID_COL_NAME = "id";
    public static final String CATEGORY_COL_NAME = "category";
    public static final String CREATED_BY_COL_NAME = "created_by";
    public static final String CREATED_TIME_COL_NAME = "created_time";
    public static final String LAST_MODIFIED_BY_COL_NAME = "last_modified_by";
    public static final String LAST_MODIFIED_TIME_COL_NAME = "last_modified_time";
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = ID_COL_NAME, nullable = false)
    private Integer id;

    @Size(max = 255)
    @NotNull
    @Column(name = CATEGORY_COL_NAME, nullable = false)
    @Audited(withModifiedFlag = true)
    private String category;

    @Size(max = 255)
    @NotNull
    @Column(name = CREATED_BY_COL_NAME, nullable = false)
    @NotAudited
    private String createdBy;

    @NotNull
    @Column(name = CREATED_TIME_COL_NAME, nullable = false)
    @NotAudited
    private LocalDateTime createdTime;

    @Size(max = 255)
    @NotNull
    @Column(name = LAST_MODIFIED_BY_COL_NAME, nullable = false)
    @Audited(withModifiedFlag = true)
    private String lastModifiedBy;

    @NotNull
    @Column(name = LAST_MODIFIED_TIME_COL_NAME, nullable = false)
    @NotAudited
    private LocalDateTime lastModifiedTime;

    public static List<Lvl1CategoryDto.Response> toDtoList(List<Lvl1CategoryEntity> lvl1CategoryEntities) {
        return lvl1CategoryEntities.stream()
                .map(Lvl1CategoryEntity::toDto)
                .toList();
    }

    public Lvl1CategoryDto.Response toDto() {
        return new Lvl1CategoryDto.Response(this.id, this.category, this.createdBy, this.createdTime,
                this.lastModifiedBy, this.lastModifiedTime);
    }
}