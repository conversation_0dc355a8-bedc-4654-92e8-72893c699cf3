package com.walnut.vegaspread.coa.service;

import com.walnut.vegaspread.coa.entity.CoaTaskEntity;
import com.walnut.vegaspread.coa.model.CoaMappingDto;
import com.walnut.vegaspread.coa.repository.CoaTaskRepository;
import com.walnut.vegaspread.common.model.extraction.TableTagDto;
import io.quarkus.hibernate.orm.panache.PanacheQuery;
import jakarta.enterprise.context.ApplicationScoped;
import org.eclipse.microprofile.jwt.JsonWebToken;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.walnut.vegaspread.common.utils.Jwt.getClientName;

@ApplicationScoped
public class CoaMappingService {

    private final JsonWebToken accessToken;
    private final CoaTaskRepository coaTaskRepository;
    private final ExchangeService exchangeService;

    public CoaMappingService(JsonWebToken accessToken, CoaTaskRepository coaTaskRepository,
                             ExchangeService exchangeService) {
        this.accessToken = accessToken;
        this.coaTaskRepository = coaTaskRepository;
        this.exchangeService = exchangeService;
    }

    public List<CoaMappingDto> getCoaMapping(String clientName) {
        String finalClientName = clientName == null ? getClientName(accessToken) : clientName;
        PanacheQuery<CoaTaskEntity> taskQuery = coaTaskRepository.find("clientName LIKE ?1",
                "%" + finalClientName + "%");
        List<Integer> tableTypeIds = taskQuery.stream().map(CoaTaskEntity::getTableTypeId).distinct().toList();
        List<TableTagDto.Response> tableTypes = exchangeService.getTableTypes(tableTypeIds);
        Map<Integer, String> tableTypeMap = tableTypes.stream()
                .collect(Collectors.toMap(TableTagDto.Response::id, TableTagDto.Response::tag));

        return taskQuery.stream()
                .map(coaTaskEntity -> new CoaMappingDto(coaTaskEntity.tableTypeId,
                        tableTypeMap.get(coaTaskEntity.tableTypeId),
                        coaTaskEntity.text,
                        coaTaskEntity.fsText, coaTaskEntity.coa.coaId))
                .toList();
    }
}
