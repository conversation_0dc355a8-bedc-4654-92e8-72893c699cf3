package com.walnut.vegaspread.coa.service;

import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.model.Lvl1CategoryDto;
import com.walnut.vegaspread.coa.repository.Lvl1CategoryRepository;
import com.walnut.vegaspread.common.exceptions.ResponseException;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.core.Response;
import org.jboss.logging.Logger;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@ApplicationScoped
public class Lvl1CategoryService {

    private static final Logger logger = Logger.getLogger(Lvl1CategoryService.class);
    private final Lvl1CategoryRepository lvlCategoryRepository;

    public Lvl1CategoryService(Lvl1CategoryRepository lvlCategoryRepository) {
        this.lvlCategoryRepository = lvlCategoryRepository;
    }

    @Transactional
    public List<Lvl1CategoryEntity> create(List<String> categories, String createdBy) {
        if (categories == null || categories.isEmpty()) {
            return Collections.emptyList();
        }
        if (categories.stream().anyMatch(String::isBlank) || categories.stream().anyMatch(Objects::isNull)) {
            logger.errorf("Category cannot be null or blank");
            return ResponseException.throwResponseException(Response.Status.BAD_REQUEST,
                    "Category cannot be null or blank");
        }
        if (createdBy == null) {
            logger.errorf("Created by cannot be null");
            return ResponseException.throwResponseException(Response.Status.BAD_REQUEST,
                    "Created by cannot be null");
        }
        List<Lvl1CategoryEntity> lvl1CategoryEntities = categories.stream().map(category -> Lvl1CategoryEntity.builder()
                .category(category)
                .createdBy(createdBy)
                .createdTime(LocalDateTime.now())
                .lastModifiedBy(createdBy)
                .lastModifiedTime(LocalDateTime.now())
                .build()).toList();
        lvlCategoryRepository.persist(lvl1CategoryEntities);
        return lvl1CategoryEntities;
    }

    @Transactional
    public List<Lvl1CategoryEntity> update(List<Lvl1CategoryDto.Update> updateDtos, String username) {
        if (updateDtos == null || updateDtos.isEmpty()) {
            return Collections.emptyList();
        }
        List<Lvl1CategoryEntity> lvl1CategoryEntities = new ArrayList<>();
        for (Lvl1CategoryDto.Update updateDto : updateDtos) {
            Lvl1CategoryEntity lvl1CategoryEntity = lvlCategoryRepository.findById(updateDto.id());
            if (lvl1CategoryEntity == null) {
                logger.errorf("Lvl1 category with id %s not found", updateDto.id());
                continue;
            }
            lvl1CategoryEntity.setCategory(updateDto.category());
            lvl1CategoryEntity.setLastModifiedBy(username);
            lvl1CategoryEntity.setLastModifiedTime(LocalDateTime.now());
            lvl1CategoryEntities.add(lvl1CategoryEntity);
        }
        lvlCategoryRepository.persist(lvl1CategoryEntities);
        return lvl1CategoryEntities;
    }

    public List<Lvl1CategoryEntity> getLvl1Categories() {
        return lvlCategoryRepository.listAll();
    }

    public List<Lvl1CategoryEntity> getLvl1CategoriesByIds(List<Integer> categoryIds) {
        if (categoryIds == null || categoryIds.isEmpty()) {
            return Collections.emptyList();
        }
        if (categoryIds.stream().anyMatch(Objects::isNull)) {
            logger.errorf("Category ids cannot be null for fetching");
            return ResponseException.throwResponseException(Response.Status.BAD_REQUEST,
                    "Category ids cannot be null for fetching");
        }
        return lvlCategoryRepository.findByIds(categoryIds);
    }

    @Transactional
    public long delete(List<Integer> categoryIds) {
        if (categoryIds == null || categoryIds.isEmpty()) {
            return 0;
        }
        if (categoryIds.stream().anyMatch(Objects::isNull)) {
            logger.errorf("Category ids cannot be null for deletion");
            return ResponseException.throwResponseException(Response.Status.BAD_REQUEST,
                    "Category ids cannot be null for deletion");
        }
        return lvlCategoryRepository.deleteByIds(categoryIds);
    }

    public List<Lvl1CategoryEntity> findByCategories(List<String> categories) {
        if (categories == null || categories.isEmpty()) {
            return Collections.emptyList();
        }
        return lvlCategoryRepository.findByCategories(categories);
    }

    public Lvl1CategoryEntity findById(@NotNull Integer id) {
        return lvlCategoryRepository.findById(id);
    }
}
