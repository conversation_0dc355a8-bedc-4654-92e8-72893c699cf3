package com.walnut.vegaspread.coa.service;

import com.walnut.vegaspread.coa.Lvl1CategoryEntityRollbackStrategy;
import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.model.Lvl1CategoryAuditDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditFilterDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditRequestDto;
import com.walnut.vegaspread.common.model.audit.envers.BaseEntityMapper;
import com.walnut.vegaspread.common.service.audit.envers.GenericAuditService;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;

import java.util.List;

/**
 * Lvl1CategoryEntity-specific audit service that provides convenient methods for Lvl1CategoryEntity audit operations.
 * This service delegates to the generic audit service for most operations and provides Lvl1CategoryEntity-specific
 * rollback.
 */
@ApplicationScoped
public class Lvl1CategoryAuditService {

    private final GenericAuditService genericAuditService;
    private final Lvl1CategoryEntityRollbackStrategy lvl1CategoryEntityRollbackStrategy;

    public Lvl1CategoryAuditService(GenericAuditService genericAuditService,
                                    Lvl1CategoryEntityRollbackStrategy lvl1CategoryEntityRollbackStrategy) {
        this.genericAuditService = genericAuditService;
        this.lvl1CategoryEntityRollbackStrategy = lvl1CategoryEntityRollbackStrategy;
    }

    /**
     * Lvl1CategoryEntity-specific rollback method for backward compatibility.
     *
     * @param traceId       The trace ID to rollback to
     * @param entityManager The entity manager
     */
    @Transactional
    public void rollback(String traceId, EntityManager entityManager) {
        genericAuditService.rollback(traceId, lvl1CategoryEntityRollbackStrategy, entityManager);
    }

    /**
     * Get paginated audit data for Lvl1CategoryEntity with DTO mapping.
     *
     * @param request       The audit request
     * @param mapper        The Lvl1CategoryEntity to Lvl1CategoryAuditDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<Lvl1CategoryAuditDto>> getPaginatedAuditsAsDto(
            AuditRequestDto request, BaseEntityMapper<Lvl1CategoryEntity, Lvl1CategoryAuditDto> mapper,
            EntityManager entityManager) {
        return genericAuditService.getPaginatedAuditsAsDto(Lvl1CategoryEntity.class, request, mapper, entityManager);
    }

    /**
     * Convenience method to get paginated audits for a specific Lvl1CategoryEntity ID.
     *
     * @param lvl1CategoryId The Lvl1CategoryEntity ID to get audits for
     * @param mapper         The Lvl1CategoryEntity to Lvl1CategoryAuditDto mapper
     * @param entityManager  The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<Lvl1CategoryAuditDto>> getAuditForLvl1CategoryId(
            Integer lvl1CategoryId, BaseEntityMapper<Lvl1CategoryEntity, Lvl1CategoryAuditDto> mapper,
            EntityManager entityManager) {

        // Create a request to filter by lvl1CategoryId
        var filterDto = new AuditFilterDto(
                "id",
                lvl1CategoryId,
                AuditFilterDto.FilterOperation.EQUALS
        );

        var request = new AuditRequestDto(
                List.of(filterDto),
                null,
                1,
                100
        );

        return getPaginatedAuditsAsDto(request, mapper, entityManager);
    }
}
