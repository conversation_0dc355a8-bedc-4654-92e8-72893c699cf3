package com.walnut.vegaspread.coa.service;

import com.walnut.vegaspread.common.clients.AuditClient;
import com.walnut.vegaspread.common.clients.ClientFactory;
import com.walnut.vegaspread.common.clients.ExtractionClient;
import com.walnut.vegaspread.common.clients.IamClient;
import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.common.model.audit.coa.CoaItemAuditDto;
import com.walnut.vegaspread.common.model.cloud.CloudPlatform;
import com.walnut.vegaspread.common.model.extraction.TableTagDto;
import com.walnut.vegaspread.common.utils.ConfigKeys;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;

@ApplicationScoped
public class ExchangeService {

    private final AuditClient auditClient;
    private final ExtractionClient extractionClient;
    private final IamClient iamClient;

    public ExchangeService(@ConfigProperty(name = ConfigKeys.ENV_NAME_KEY) String envName,
                           @ConfigProperty(name = ConfigKeys.CLOUD_PROVIDER_TYPE) String cloudProviderType,
                           @ConfigProperty(name = ConfigKeys.AWS_GATEWAY_URL) Optional<String> awsGatewayUrlOptional) {
        if (cloudProviderType.equals(CloudPlatform.GCP.getProvider())) {
            this.auditClient = ClientFactory.createClient(AuditClient.class, envName);
            this.extractionClient = ClientFactory.createClient(ExtractionClient.class, envName);
            this.iamClient = ClientFactory.createClient(IamClient.class, envName);
        } else {
            if (awsGatewayUrlOptional.isEmpty()) {
                ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR,
                        "AWS Gateway URL is not configured");
            }
            String awsGatewayUrl = awsGatewayUrlOptional.get();
            this.auditClient = ClientFactory.createAWSClient(AuditClient.class, envName, awsGatewayUrl);
            this.extractionClient = ClientFactory.createAWSClient(ExtractionClient.class, envName, awsGatewayUrl);
            this.iamClient = ClientFactory.createAWSClient(IamClient.class, envName, awsGatewayUrl);
        }
    }

    public List<CoaItemAuditDto.Response> coaItemAuditForCreate(List<CoaItemAuditDto.Create> coaItemCreateAudits) {
        return auditClient.coaItemAuditForCreate(coaItemCreateAudits);
    }

    public List<CoaItemAuditDto.Response> coaItemAuditForUpdate(List<CoaItemAuditDto.Update> coaItemRequests) {
        return auditClient.coaItemAuditForUpdate(coaItemRequests);
    }

    public List<CoaItemAuditDto.Response> coaItemAuditForDelete(List<CoaItemAuditDto.Delete> coaItemRequests) {
        return auditClient.coaItemAuditForDelete(coaItemRequests);
    }

    public String getTableType(Integer tableTypeId) {
        return extractionClient.getTableTagsByIds(List.of(tableTypeId)).get(0).tag();
    }

    public List<TableTagDto.Response> getTableTypes(List<Integer> tableTypeIds) {

        return extractionClient.getTableTagsByIds(tableTypeIds);
    }

    public List<TableTagDto.Response> getTableTypesForProcessor(List<Integer> tableTypeIds) {
        return extractionClient.getTableTagsByIdsForProcessor(tableTypeIds);
    }

    public Map<String, String> getUsernameMapping(Stream<String> usernames) {
        return iamClient.getNamesFromUsernames(usernames.distinct().toList());
    }
}
