package com.walnut.vegaspread.coa.service;

import com.walnut.vegaspread.coa.audit.CoaEntityRollbackStrategy;
import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.common.model.audit.envers.AuditFilterDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditRequestDto;
import com.walnut.vegaspread.common.model.audit.envers.BaseEntityMapper;
import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import com.walnut.vegaspread.common.service.audit.envers.GenericAuditService;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;

/**
 * CoaEntity-specific audit service that provides convenient methods for CoaEntity audit operations.
 * This service delegates to the generic audit service for most operations and provides CoaEntity-specific rollback.
 */
@ApplicationScoped
public class CoaAuditService {

    private final GenericAuditService genericAuditService;
    private final CoaEntityRollbackStrategy coaEntityRollbackStrategy;

    public CoaAuditService(GenericAuditService genericAuditService,
                           CoaEntityRollbackStrategy coaEntityRollbackStrategy) {
        this.genericAuditService = genericAuditService;
        this.coaEntityRollbackStrategy = coaEntityRollbackStrategy;
    }

    /**
     * CoaEntity-specific rollback method for backward compatibility.
     *
     * @param traceId       The trace ID to rollback to
     * @param entityManager The entity manager
     */
    @Transactional
    public void rollback(String traceId, EntityManager entityManager) {
        genericAuditService.rollback(traceId, coaEntityRollbackStrategy, entityManager);
    }

    /**
     * Get paginated audit data for CoaEntity with DTO mapping.
     *
     * @param request       The audit request
     * @param mapper        The CoaEntity to CoaItemDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> getPaginatedAuditsAsDto(
            AuditRequestDto request, BaseEntityMapper<CoaEntity, CoaItemDto> mapper, EntityManager entityManager) {
        return genericAuditService.getPaginatedAuditsAsDto(CoaEntity.class, request, mapper, entityManager);
    }

    /**
     * Convenience method to get paginated audits for a specific CoaEntity ID.
     *
     * @param coaId         The CoaEntity ID to get audits for
     * @param mapper        The CoaEntity to CoaItemDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> getAuditForCoaId(
            Integer coaId, BaseEntityMapper<CoaEntity, CoaItemDto> mapper, EntityManager entityManager) {

        // Create a request to filter by coaId
        var filterDto = new AuditFilterDto(
                "coaId",
                coaId,
                AuditFilterDto.FilterOperation.EQUALS
        );

        var request = new AuditRequestDto(
                java.util.List.of(filterDto),
                null,
                1,
                100
        );

        return getPaginatedAuditsAsDto(request, mapper, entityManager);
    }
}
