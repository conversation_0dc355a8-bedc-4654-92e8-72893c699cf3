package com.walnut.vegaspread.coa.service;

import com.walnut.vegaspread.coa.entity.WalnutClientCoaMappingEntity;
import com.walnut.vegaspread.coa.model.WalnutClientCoaMappingEntityDto;
import com.walnut.vegaspread.coa.primarykey.WalnutClientCoaMappingPk;
import com.walnut.vegaspread.coa.repository.WalnutClientCoaMappingRepository;
import com.walnut.vegaspread.common.exceptions.ResponseException;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response;
import org.jboss.logging.Logger;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@ApplicationScoped
public class WalnutClientCoaMappingService {

    private static final Logger logger = Logger.getLogger(WalnutClientCoaMappingService.class);
    private final WalnutClientCoaMappingRepository walnutClientCoaMappingRepository;

    public WalnutClientCoaMappingService(WalnutClientCoaMappingRepository walnutClientCoaMappingRepository) {
        this.walnutClientCoaMappingRepository = walnutClientCoaMappingRepository;
    }

    /**
     * Adds a list of new walnut coa and client coa mappings
     *
     * @param walnutClientCoaMappingEntities The id mappings for the coa.
     * @param clientName                     The name of the client for the mappings.
     * @return The list of newly created mappings.
     */
    @Transactional
    public List<WalnutClientCoaMappingEntity> create(
            List<WalnutClientCoaMappingEntityDto.CreateOrUpdate> walnutClientCoaMappingEntities, String clientName) {
        if (walnutClientCoaMappingEntities == null || walnutClientCoaMappingEntities.isEmpty()) {
            return Collections.emptyList();
        }

        Set<String> seenWalnutCoaIds = new HashSet<>();
        for (WalnutClientCoaMappingEntityDto.CreateOrUpdate dto : walnutClientCoaMappingEntities) {
            if (dto.walnutCoaId() == null || dto.clientCoaId() == null) {
                logger.errorf("Invalid walnutCoaId or clientCoaId in mapping for client '%s': %s", clientName, dto);
                return ResponseException.throwResponseException(Response.Status.BAD_REQUEST,
                        "Invalid walnutCoaId or clientCoaId in mapping for client: " + clientName);
            }

            String key = dto.walnutCoaId() + ":" + clientName;
            if (!seenWalnutCoaIds.add(key)) {
                logger.errorf("Duplicate walnutCoaId for client '%s': %s during creation", clientName,
                        dto.walnutCoaId());
                return ResponseException.throwResponseException(Response.Status.CONFLICT,
                        "Duplicate walnutCoaId " + dto.walnutCoaId() + " for client: " + clientName);
            }
        }
        deleteExistingMappings(walnutClientCoaMappingEntities.stream()
                .map(WalnutClientCoaMappingEntityDto.CreateOrUpdate::walnutCoaId)
                .distinct()
                .toList(), clientName);

        List<WalnutClientCoaMappingEntity> mappings = walnutClientCoaMappingEntities.stream()
                .map(walnutClientCoaMappingEntity -> WalnutClientCoaMappingEntity.builder()
                        .mapping(new WalnutClientCoaMappingPk(walnutClientCoaMappingEntity.walnutCoaId(),
                                walnutClientCoaMappingEntity.clientCoaId(), clientName))
                        .build())
                .toList();
        walnutClientCoaMappingRepository.persist(mappings);
        return mappings;
    }

    /**
     * Delete existing mapping using walnut coa id and client name;
     *
     * @param walnutCoaIds List of walnut coa id's for deleting mappings for the client.
     * @param clientName   The name of the client for which to delete the mapping.
     */
    private void deleteExistingMappings(List<Integer> walnutCoaIds, String clientName) {
        walnutClientCoaMappingRepository.deleteByWalnutCoaIdAndClientName(walnutCoaIds, clientName);
        walnutClientCoaMappingRepository.flush();
    }

    /**
     * Gets a list of walnut coa and client coa mappings for the given client name.
     *
     * @param clientName The name of the client for which the mappings are requested.
     * @return The list of mappings for the client.
     */
    public List<WalnutClientCoaMappingEntity> get(String clientName) {
        if (clientName == null || clientName.isBlank()) {
            return Collections.emptyList();
        }
        return walnutClientCoaMappingRepository.findByClientName(clientName);
    }
}
