package com.walnut.vegaspread.iam.service;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.iam.model.KeycloakConfig;
import com.walnut.vegaspread.iam.model.UpdateProfileDto;
import com.walnut.vegaspread.iam.model.UserProfile;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.core.Response;
import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.Logger;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.admin.client.resource.RolesResource;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.admin.client.resource.UsersResource;
import org.keycloak.representations.idm.CredentialRepresentation;
import org.keycloak.representations.idm.GroupRepresentation;
import org.keycloak.representations.idm.UserRepresentation;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Service class to interact with Keycloak Admin API.
 */
@ApplicationScoped
public class KeycloakAdminService {

    public static final String CLIENT_NAME_ATTRIBUTE_KEY = "client_name";
    public static final String DEFAULT_CLIENT_NAME = "walnut";
    private final Keycloak keycloak;
    private final KeycloakConfig keycloakConfig;
    private final Logger logger = Logger.getLogger(KeycloakAdminService.class);

    public KeycloakAdminService(Keycloak keycloak, KeycloakConfig keycloakConfig) {
        this.keycloak = keycloak;
        this.keycloakConfig = keycloakConfig;
    }

    /**
     * Method to get the current realm.
     *
     * @return The current realm as a {@link RealmResource}
     */
    private RealmResource realm() {
        return keycloak.realm(keycloakConfig.realm());
    }

    private String getName(UserRepresentation user) {
        return user.getFirstName() + " " + Optional.ofNullable(user.getLastName()).orElse(StringUtils.EMPTY);
    }

    /**
     * Updates the user profile.
     *
     * @param userId           The ID of the user.
     * @param updateProfileDto The user profile with the update information.
     */
    public void updateUserProfile(String userId, UpdateProfileDto updateProfileDto) {
        if (userId != null && !userId.isBlank()) {

            UserResource userResource = realm().users().get(userId);

            UserRepresentation userRepresentation = new UserRepresentation();
            userRepresentation.setFirstName(updateProfileDto.firstName());
            userRepresentation.setLastName(updateProfileDto.lastName());
            userRepresentation.setEmail(updateProfileDto.email());
            userRepresentation.setEnabled(updateProfileDto.enabled());

            // Set client name.
            Map<String, List<String>> attributes = new HashMap<>();
            attributes.put(CLIENT_NAME_ATTRIBUTE_KEY, Collections.singletonList(updateProfileDto.clientName()));
            userRepresentation.setAttributes(attributes);
            userResource.update(userRepresentation);

            List<String> currentGroups = userResource.groups().stream().map(GroupRepresentation::getName).toList();

            //Create list from all the groups the user is already a part of.
            List<String> groupsToRemove = new ArrayList<>(currentGroups);
            //Remove groups from the existing list that the user has requested to be added as a part of the update.
            groupsToRemove.removeAll(updateProfileDto.groups());

            //Create list from the groups the user has requested to add.
            List<String> groupsToAdd = new ArrayList<>(updateProfileDto.groups());
            //Remove groups in the update list that are already assigned to the user.
            groupsToAdd.removeAll(currentGroups);

            removeUserFromGroups(userResource, groupsToRemove);
            addUserToGroups(userResource, groupsToAdd);
        } else {
            logger.error("Keycloak user profile update failed. No user id found");
        }
    }

    /**
     * Resets the password of a user.
     *
     * @param userId The ID of the user.
     */
    public void resetUserPassword(String userId) {
        UsersResource usersResource = realm().users();
        usersResource.get(userId).executeActionsEmail(Collections.singletonList("UPDATE_PASSWORD"));
    }

    /**
     * Adds a new user to the current realm in keycloak.
     *
     * @param userProfile The {@link UserProfile} for the new user.
     * @return The user id of the new user.Null, if the user creation fails
     */
    public Response addUser(UserProfile userProfile) {
        UserRepresentation user = new UserRepresentation();
        user.setUsername(userProfile.getUsername());
        user.setFirstName(userProfile.getFirstName());
        user.setLastName(userProfile.getLastName());
        user.setEmail(userProfile.getEmail());
        user.setEnabled(true);

        Map<String, List<String>> attributes = new HashMap<>();
        attributes.put(CLIENT_NAME_ATTRIBUTE_KEY, Collections.singletonList(userProfile.getClientName()));
        user.setAttributes(attributes);

        CredentialRepresentation credential = new CredentialRepresentation();
        credential.setType(CredentialRepresentation.PASSWORD);
        credential.setValue(generateRandomPassword(10));
        user.setCredentials(List.of(credential));

        Response response = realm().users().create(user);
        if (response.getStatusInfo().equals(Response.Status.CREATED)) {
            // Retrieve the ID of the newly created user from the Location header
            String location = response.getHeaderString("Location");
            String userId = location.substring(location.lastIndexOf("/") + 1);
            addUserToGroups(realm().users().get(userId), userProfile.getGroups());

            realm().users().get(userId).executeActionsEmail(Collections.singletonList("UPDATE_PASSWORD"));
            return Response.status(Response.Status.CREATED).entity(userId).build();
        }
        logger.errorf("User creation failed for %s", userProfile);
        return response;
    }

    /**
     * Retrieves all users in current realm.
     *
     * @return List of user profiles.
     */
    public List<UserProfile> getAllUsers() {
        List<UserRepresentation> userRepresentations = realm().users().list();
        return userRepresentations.stream()
                .map(this::convertToUserProfile)
                .toList();
    }

    /**
     * Converts a {@link UserRepresentation} to a {@link UserProfile}
     *
     * @param userRepresentation The user representation to be converted.
     * @return The user profile for the user representation.
     */
    private UserProfile convertToUserProfile(UserRepresentation userRepresentation) {
        UserProfile userProfile = new UserProfile();
        userProfile.setId(userRepresentation.getId());
        userProfile.setUsername(userRepresentation.getUsername());
        userProfile.setFirstName(userRepresentation.getFirstName());
        userProfile.setLastName(userRepresentation.getLastName());
        userProfile.setEmail(userRepresentation.getEmail());
        userProfile.setEnabled(userRepresentation.isEnabled());
        if (userRepresentation.getAttributes() != null && userRepresentation.getAttributes()
                .containsKey(CLIENT_NAME_ATTRIBUTE_KEY)) {
            userProfile.setClientName(userRepresentation.getAttributes().get(CLIENT_NAME_ATTRIBUTE_KEY).get(0));
        } else {
            logger.warnf("User %s does not have %s attribute. Defaulting to '%s'.",
                    userRepresentation.getUsername(), CLIENT_NAME_ATTRIBUTE_KEY, DEFAULT_CLIENT_NAME);
            userProfile.setClientName(DEFAULT_CLIENT_NAME);
        }

        List<GroupRepresentation> groups = realm().users().get(userRepresentation.getId()).groups();
        userProfile.setGroups(groups.stream().map(GroupRepresentation::getName).toList());
        return userProfile;
    }

    /**
     * Retrieves all groups in Keycloak.
     *
     * @return List of group names.
     */
    public List<String> getAllGroups() {
        List<GroupRepresentation> groups = realm().groups().groups();
        return groups.stream().map(GroupRepresentation::getName).toList();
    }

    /**
     * Generates a secure random password.
     *
     * @param length the length of the password to generate
     * @return the generated password
     * @throws RuntimeException if SHA-256 algorithm is not available
     */
    private String generateRandomPassword(int length) {
        byte[] randomBytes = new byte[length];
        new SecureRandom().nextBytes(randomBytes);

        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashedBytes = digest.digest(randomBytes);
            return Base64.getEncoder().encodeToString(hashedBytes);
        } catch (NoSuchAlgorithmException e) {
            logger.error(e);
            ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR,
                    "SHA-256 algorithm not available");
            return null;
        }
    }

    /**
     * Get group in the form of {@link GroupRepresentation} for the list of groups names.
     *
     * @param groupNames The name of the groups.
     * @return List of group representations for the groups found. Note: The group names with no matching groups will
     * be omitted.
     */
    private List<GroupRepresentation> getGroupsByNames(List<String> groupNames) {
        if (groupNames == null || groupNames.isEmpty()) {
            return Collections.emptyList();
        }

        List<GroupRepresentation> realmGroups = realm().groups().groups();
        List<GroupRepresentation> groups = new ArrayList<>();
        Map<String, GroupRepresentation> realmGroupMap = realmGroups.stream()
                .collect(Collectors.toMap(GroupRepresentation::getName, realmGroup -> realmGroup));

        for (String groupName : groupNames) {
            GroupRepresentation realmGroup = realmGroupMap.get(groupName);
            if (realmGroup == null) {
                logger.errorf("No group found with group name %s", groupName);
            } else {
                groups.add(realmGroup);
            }
        }
        return groups;
    }

    /**
     * Adds user to groups.
     *
     * @param userResource The {@link UserResource} for the user.
     * @param groupNames   The list for the groups the user needs to be added to.
     */
    private void addUserToGroups(UserResource userResource, List<String> groupNames) {
        List<GroupRepresentation> groups = getGroupsByNames(groupNames);
        logger.infof("User %s added to groups %s", userResource.toRepresentation().getUsername(), groupNames);
        groups.forEach(group -> userResource.joinGroup(group.getId()));
    }

    /**
     * Remove user from groups.
     *
     * @param userResource The {@link UserResource} for the user.
     * @param groupNames   The list for the groups the user need to be removed from.
     */
    private void removeUserFromGroups(UserResource userResource, List<String> groupNames) {
        List<GroupRepresentation> groups = getGroupsByNames(groupNames);
        logger.infof("User %s leaving groups %s", userResource.toRepresentation().getUsername(), groupNames);
        groups.forEach(group -> userResource.leaveGroup(group.getId()));
    }

    /**
     * Enable/Disable a user in the current realm.
     *
     * @param userId  The user id for the user.
     * @param enabled The boolean value to indicate status for the user. True, to enable user, else false.
     * @return Response indicating the success of the operation.
     */
    public Response enableUser(String userId, boolean enabled) {
        if (userId != null && !userId.isBlank()) {
            UserResource resource = realm().users().get(userId);
            UserRepresentation userRepresentation = resource.toRepresentation();
            userRepresentation.setEnabled(enabled);
            resource.update(userRepresentation);
            return Response.status(Response.Status.OK).build();
        } else {
            logger.errorf("Failed to %s user.User id empty or null!", enabled ? "enable" : "disable");
            return Response.status(Response.Status.BAD_REQUEST).entity("User id null or empty").build();
        }
    }

    /**
     * Get full name for username.
     *
     * @param username The username for the user in keycloak.
     * @return The full name for the username.Empty string, is username is not found.
     */
    public String getNameFromUsername(String username) {
        if (username == null || username.isEmpty())
            return StringUtils.EMPTY;
        List<UserRepresentation> userRepresentation = realm().users().searchByUsername(username, true);
        return userRepresentation.isEmpty() ? StringUtils.EMPTY : getName(userRepresentation.get(0));
    }

    /**
     * Get full names for usernames.
     *
     * @param usernames The username for the user in keycloak.
     * @return {@link Map} of username and name mapping.
     */
    public Map<String, String> getNamesFromUsernames(List<String> usernames) {

        if (usernames == null || usernames.isEmpty())
            return Collections.emptyMap();
        List<UserRepresentation> userRepresentations = realm().users().list();

        Map<String, String> usersMap = userRepresentations.stream()
                .collect(Collectors.toMap(UserRepresentation::getUsername, this::getName));

        return usernames.stream().distinct().filter(Objects::nonNull)
                .collect(Collectors.toMap(Function.identity(), user -> usersMap.getOrDefault(user, StringUtils.EMPTY)));
    }

    /**
     * Get a list of users belonging to a group from keycloak.
     *
     * @param group The name of the group.
     * @return {@link Map} of username and name for users of the group. If the group does not exist or if there are
     * no users in the group, returns empty list.
     */
    public Map<String, String> getUsersInGroup(String group, String clientName) {
        List<GroupRepresentation> groupRepresentations = getGroupsByNames(List.of(group.toUpperCase()));
        if (!groupRepresentations.isEmpty()) {
            return realm().groups()
                    .group(groupRepresentations.get(0).getId())
                    .members()
                    .stream()
                    .filter(user -> {

                        Map<String, List<String>> attributes = user.getAttributes();
                        String userClientName;
                        if (attributes == null || !attributes.containsKey(CLIENT_NAME_ATTRIBUTE_KEY)) {
                            logger.warnf("User %s does not have %s attribute. Defaulting to '%s'.",
                                    user.getUsername(), CLIENT_NAME_ATTRIBUTE_KEY, DEFAULT_CLIENT_NAME);
                            userClientName = DEFAULT_CLIENT_NAME;
                        } else {
                            userClientName = attributes.get(CLIENT_NAME_ATTRIBUTE_KEY).get(0);
                        }
                        return clientName.equals(userClientName);
                    })
                    .collect(Collectors.toMap(UserRepresentation::getUsername, this::getName));
        } else {
            return Collections.emptyMap();
        }
    }

    public String getUserEmail(String username) {
        if (username == null || username.isEmpty())
            return StringUtils.EMPTY;
        List<UserRepresentation> userRepresentation = realm().users().searchByUsername(username, true);
        return userRepresentation.isEmpty() ? StringUtils.EMPTY : userRepresentation.get(0).getEmail();
    }

    public Map<String, String> getUsersWithRole(String roleName) {
        RolesResource rolesResource = realm().roles();
        boolean roleExists = rolesResource.list()
                .stream()
                .anyMatch(r -> r.getName().equalsIgnoreCase(roleName));
        if (!roleExists) {
            ResponseException.throwResponseException(Response.Status.BAD_REQUEST, "Role " + roleName + " not found");
            return Collections.emptyMap();
        }
        Map<String, String> usernameToNameMap = new HashMap<>();
        rolesResource.get(roleName)
                .getUserMembers()
                .forEach(user -> usernameToNameMap.put(user.getUsername(), getName(user)));
        Set<GroupRepresentation> groups = rolesResource.get(roleName).getRoleGroupMembers();
        if (groups == null || groups.isEmpty()) {
            return usernameToNameMap;
        }

        groups.forEach(group -> realm().groups()
                .group(group.getId())
                .members()
                .forEach(user -> usernameToNameMap.put(user.getUsername(), getName(user))));
        return usernameToNameMap;
    }
}
