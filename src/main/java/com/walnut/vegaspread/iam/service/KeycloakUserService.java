package com.walnut.vegaspread.iam.service;

import com.walnut.vegaspread.iam.model.KeycloakConfig;
import com.walnut.vegaspread.iam.model.UserProfile;
import jakarta.enterprise.context.ApplicationScoped;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.representations.idm.UserRepresentation;

import static com.walnut.vegaspread.iam.utils.Jwt.getKeycloakUserId;

@ApplicationScoped
public class KeycloakUserService {

    private final Logger logger = LogManager.getLogger(KeycloakUserService.class);
    private final Keycloak keycloak;
    private final KeycloakConfig keycloakConfig;
    private final JsonWebToken accessToken;

    public KeycloakUserService(Keycloak keycloak, KeycloakConfig keycloakConfig, JsonWebToken accessToken) {
        this.keycloak = keycloak;
        this.keycloakConfig = keycloakConfig;
        this.accessToken = accessToken;
    }

    /**
     * Method for user to update their user profile.
     */
    public void updateUserProfile(UserProfile userProfile) {
        String userId = getKeycloakUserId(accessToken);
        if (userId != null && !userId.isBlank()) {

            RealmResource realmResource = keycloak.realm(keycloakConfig.realm());
            UserResource userResource = realmResource.users().get(userId);

            UserRepresentation userRepresentation = new UserRepresentation();
            userRepresentation.setFirstName(userProfile.getFirstName());
            userRepresentation.setLastName(userProfile.getLastName());

            userResource.update(userRepresentation);
        } else {
            logger.error("Keycloak user profile update failed. No user id found");
        }
    }
}
