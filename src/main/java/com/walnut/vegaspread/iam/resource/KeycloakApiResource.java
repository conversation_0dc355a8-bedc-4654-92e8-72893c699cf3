package com.walnut.vegaspread.iam.resource;

import com.walnut.vegaspread.iam.model.AuthDto;
import com.walnut.vegaspread.iam.model.TokenDto;
import com.walnut.vegaspread.iam.security.ApiKeyAuthenticate;
import com.walnut.vegaspread.iam.service.KeycloakAdminService;
import com.walnut.vegaspread.iam.service.KeycloakTokenService;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.enums.ParameterIn;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.jboss.resteasy.reactive.RestResponse;

@Path("/api")
@ApiKeyAuthenticate
@Produces(MediaType.TEXT_PLAIN)
public class KeycloakApiResource {
    private final KeycloakTokenService keycloakTokenService;
    private final KeycloakAdminService keycloakAdminService;

    @HeaderParam("X-CLIENT-ID")
    @Parameter(name = "X-CLIENT-ID", in = ParameterIn.HEADER, required = true, description = "Client ID")
    private String clientId;

    @HeaderParam("X-API-KEY")
    @Parameter(name = "X-API-KEY", in = ParameterIn.HEADER, required = true, description = "API Key")
    private String apiKey;

    public KeycloakApiResource(KeycloakTokenService keycloakTokenService, KeycloakAdminService keycloakAdminService) {
        this.keycloakTokenService = keycloakTokenService;
        this.keycloakAdminService = keycloakAdminService;
    }

    @POST
    @Path("/validate-api-key")
    public RestResponse<String> validateApiKey() {
        return RestResponse.ok("API key is valid");
    }

    @GET
    @Path("/user/{username}/email")
    public RestResponse<String> getUserEmail(@PathParam("username") String username) {
        return RestResponse.ok(keycloakAdminService.getUserEmail(username));
    }

    @POST
    @Path("/generate-token")
    public RestResponse<String> generateToken() {
        TokenDto tokenDto = keycloakTokenService.getToken(new AuthDto(clientId, apiKey.trim()));
        return RestResponse.ok(tokenDto.accessToken());
    }
}
