package com.walnut.vegaspread.iam.resource;

import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.common.utils.Jwt;
import com.walnut.vegaspread.iam.model.UpdateProfileDto;
import com.walnut.vegaspread.iam.model.UserProfile;
import com.walnut.vegaspread.iam.service.KeycloakAdminService;
import io.quarkus.security.Authenticated;
import jakarta.annotation.security.RolesAllowed;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.jboss.resteasy.reactive.RestQuery;
import org.jboss.resteasy.reactive.RestResponse;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * REST resource for managing user profiles and groups in Keycloak.
 */
@Path("/admin")
@Authenticated
public class KeycloakAdminResource {

    private final KeycloakAdminService keycloakAdminService;
    private final JsonWebToken jwt;

    public KeycloakAdminResource(KeycloakAdminService keycloakAdminService, JsonWebToken jwt) {
        this.keycloakAdminService = keycloakAdminService;
        this.jwt = jwt;
    }

    /**
     * Add new user to the current realm.
     *
     * @param userProfile The profile information for the new user.
     * @return The id for the new created user.
     */
    @POST
    @RolesAllowed(Roles.SUPERADMIN)
    @Path("/user")
    @Consumes(MediaType.APPLICATION_JSON)
    @Transactional
    public Response addUser(UserProfile userProfile) {
        return keycloakAdminService.addUser(userProfile);
    }

    /**
     * Updates the user profile.
     *
     * @param userId           The ID of the user.
     * @param updateProfileDto The user profile dto as {link @{@link UpdateProfileDto}} with the new information.
     * @return Response indicating the success of the operation.
     */
    @PATCH
    @RolesAllowed(Roles.SUPERADMIN)
    @Path("/{userId}/profile")
    @Consumes(MediaType.APPLICATION_JSON)
    public Response updateUserProfile(@PathParam("userId") String userId, UpdateProfileDto updateProfileDto) {
        keycloakAdminService.updateUserProfile(userId, updateProfileDto);
        return Response.ok().build();
    }

    /**
     * Resets the password of a user.
     *
     * @param userId The ID of the user.
     * @return Response indicating the success of the operation.
     */
    @PATCH
    @RolesAllowed(Roles.SUPERADMIN)
    @Path("/{userId}/reset-password")
    @Consumes(MediaType.APPLICATION_JSON)
    public Response resetUserPassword(@PathParam("userId") String userId) {
        keycloakAdminService.resetUserPassword(userId);
        return Response.ok().build();
    }

    /**
     * Retrieves all users in the current realm.
     *
     * @return List of user profiles.
     */
    @GET
    @RolesAllowed(Roles.SUPERADMIN)
    @Path("/users")
    @Produces(MediaType.APPLICATION_JSON)
    public List<UserProfile> getAllUsers() {
        return keycloakAdminService.getAllUsers();
    }

    /**
     * Retrieves all groups in the current realm.
     *
     * @return List of group names.
     */
    @GET
    @RolesAllowed(Roles.SUPERADMIN)
    @Path("/groups")
    @Produces(MediaType.APPLICATION_JSON)
    public List<String> getAllGroups() {
        return keycloakAdminService.getAllGroups();
    }

    /**
     * Enable/Disable user.
     *
     * @param userId  The id for the user to be enabled/disabled.
     * @param enabled The boolean value to indicate status for the user. True, to enable user, else false.
     * @return Response indicating the success of the operation.
     */
    @PATCH
    @RolesAllowed(Roles.SUPERADMIN)
    @Path("/{userId}/enable")
    @Consumes(MediaType.APPLICATION_JSON)
    public Response enableUser(@PathParam("userId") String userId, @RestQuery("enabled") boolean enabled) {
        return keycloakAdminService.enableUser(userId, enabled);
    }

    /**
     * Provides full names for the usernames.
     *
     * @param usernames The list of usernames to get full names.
     * @return {@link Map} of username and full name.
     */
    @POST
    @Path("/names-from-usernames")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Map<String, String> getNamesFromUsernames(List<String> usernames) {
        return keycloakAdminService.getNamesFromUsernames(usernames);
    }

    /**
     * Provides full name for the username.
     *
     * @param username The username to get the full name.
     * @return The full name for the given username.
     */
    @POST
    @Path("/name-from-username")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.TEXT_PLAIN)
    public String getNameFromUsername(String username) {
        return keycloakAdminService.getNameFromUsername(username);
    }

    /**
     * Provides a list of users belonging to a group.
     *
     * @param group The name of the keycloak group.
     * @return {@link Map} of username and full name of users belonging to the group.
     */
    @GET
    @Path("/{group}")
    @Produces(MediaType.APPLICATION_JSON)
    public Map<String, String> getUsersInGroup(@PathParam("group") String group) {
        String clientName = Jwt.getClientName(jwt);
        if (!List.of(Roles.CHECKER_LVL_1, Roles.CHECKER_LVL_2).contains(group)) {
            return Collections.emptyMap();
        }
        return keycloakAdminService.getUsersInGroup(group, clientName);
    }

    @GET
    @Path("/role/{roleName}/users")
    public RestResponse<Map<String, String>> getUsersWithRole(@PathParam("roleName") String roleName) {
        return RestResponse.ok(keycloakAdminService.getUsersWithRole(roleName));
    }
}
