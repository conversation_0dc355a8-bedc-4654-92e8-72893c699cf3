package com.walnut.vegaspread.iam.resource;

import com.walnut.vegaspread.iam.auth.EzeeAuthClient;
import io.quarkus.arc.profile.IfBuildProfile;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.MultivaluedMap;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import java.util.HashMap;
import java.util.Map;

@IfBuildProfile("ezee")
@Path("/token")
public class TokenResource {

    private final EzeeAuthClient client;

    public TokenResource(@RestClient EzeeAuthClient client) {
        this.client = client;
    }

    @POST
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/introspect")
    public Response introspect(MultivaluedMap<String, String> form) {
        Map<String, Object> raw = client.introspect(form.getFirst("token"), form.getFirst("client_id"));
        Integer statusCode = (Integer) raw.get("code");

        Map<String, Object> result = new HashMap<>();
        if (statusCode != 200) {
            result.put("active", false);
        } else {
            Map<String, Object> data = (Map<String, Object>) raw.get("data");
            result.put("active", true);
            result.putAll(data);
        }
        return Response.ok(result).build();
    }
}
