package com.walnut.vegaspread.iam.resource;

import com.walnut.vegaspread.iam.model.UserProfile;
import com.walnut.vegaspread.iam.service.KeycloakUserService;
import io.quarkus.security.Authenticated;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

@Path("/user")
@Authenticated
public class KeycloakUserResource {

    private final KeycloakUserService keycloakUserService;

    public KeycloakUserResource(KeycloakUserService keycloakUserService) {
        this.keycloakUserService = keycloakUserService;
    }


    /**
     * Enpoint for user to update their keycloak user profile.
     *
     * @param userProfile
     * @return
     * @apiNote Only the first name and last name of the user is updated.
     */
    @PUT
    @Path("/profile")
    @Consumes(MediaType.APPLICATION_JSON)
    public Response updateUserProfile(UserProfile userProfile) {
        keycloakUserService.updateUserProfile(userProfile);
        return Response.ok().build();
    }

}