package com.walnut.vegaspread.iam.utils;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.jwt.JsonWebToken;

public class Jwt {

    private Jwt() {
        ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR, "Utility class");
    }

    public static String getKeycloakUserId(JsonWebToken accessToken) {
        return accessToken.getClaim("sub");
    }
}
