package com.walnut.vegaspread.iam.security;

import jakarta.interceptor.AroundInvoke;
import jakarta.interceptor.Interceptor;
import jakarta.interceptor.InvocationContext;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.resteasy.reactive.RestResponse;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.KeycloakBuilder;

@Interceptor
@ApiKeyAuthenticate
public class ApiKeyInterceptor {
    private final Logger logger = LogManager.getLogger(ApiKeyInterceptor.class);
    @Context
    ContainerRequestContext requestContext;
    @ConfigProperty(name = "quarkus.keycloak.admin-client.server-url")
    String keycloakServerUrl;
    @ConfigProperty(name = "quarkus.keycloak.api-key-auth.realm")
    String apiKeyAuthRealm;

    @AroundInvoke
    public Object checkApiKey(InvocationContext context) throws Exception {
        String client = requestContext.getHeaderString("X-CLIENT-ID");
        String apiKey = requestContext.getHeaderString("X-API-KEY");

        if (client == null || client.isBlank()) {
            return RestResponse.status(Response.Status.BAD_REQUEST, "Client is missing or empty");
        }
        if (apiKey == null || apiKey.isBlank()) {
            return RestResponse.status(Response.Status.BAD_REQUEST, "API key is missing or empty");
        }

        try (Keycloak authRealmKeycloak = KeycloakBuilder.builder()
                .serverUrl(keycloakServerUrl)
                .realm(apiKeyAuthRealm)
                .clientId(client)
                .clientSecret(apiKey)
                .grantType("client_credentials")
                .build()) {
            try {
                authRealmKeycloak.tokenManager().grantToken();
                return context.proceed();
            } catch (Exception e) {
                logger.debug(e.getMessage());
                return RestResponse.status(Response.Status.UNAUTHORIZED, "Invalid API key.");
            }
        } catch (Exception e) {
            logger.debug("Failed to create keycloak instance.");
            e.printStackTrace();
            return RestResponse.status(Response.Status.INTERNAL_SERVER_ERROR);
        }
    }
}
