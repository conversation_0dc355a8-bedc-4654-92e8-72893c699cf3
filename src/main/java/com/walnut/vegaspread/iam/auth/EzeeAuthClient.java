package com.walnut.vegaspread.iam.auth;

import io.quarkus.arc.profile.IfBuildProfile;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import java.util.Map;

@IfBuildProfile("ezee")
@Produces(MediaType.APPLICATION_JSON)
@Path("/")
@RegisterRestClient(configKey = "ezee-auth-client")
public interface EzeeAuthClient {

    @GET
    @Path("/user/authenticate")
    Map<String, Object> introspect(@HeaderParam("token") String token, @HeaderParam("e-id") String eId);
}
