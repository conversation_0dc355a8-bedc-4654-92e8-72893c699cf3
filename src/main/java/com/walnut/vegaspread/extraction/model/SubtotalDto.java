package com.walnut.vegaspread.extraction.model;

import jakarta.validation.constraints.NotNull;

import java.io.Serializable;

public interface SubtotalDto {
    record CreateOrUpdate(String subtotalName, @NotNull Integer excelRowNumber) implements Serializable {
    }

    record Response(@NotNull Integer id, @NotNull String coaClient, @NotNull String client,
                    @NotNull String subtotalName, @NotNull Integer excelRowNumber) implements Serializable {
    }
}
