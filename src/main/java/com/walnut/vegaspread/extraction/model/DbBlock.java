package com.walnut.vegaspread.extraction.model;

import io.quarkus.runtime.annotations.RegisterForReflection;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

import java.util.UUID;

public interface DbBlock {

    @Builder
    @Data
    class BlockDto {
        private UUID docId;
        private int blockId;
        private Integer pageNum;
        private BlockTypeEnum blockType;
        private Integer xMin;
        private Integer xMax;
        private Integer yMin;
        private Integer yMax;
        private Integer score;
        private Integer tagId;
        private String comment;
        private Integer tagExplainabilityId;
    }

    record CreateBlockDto(@NotNull Integer pageNum, @NotNull BlockTypeEnum blockType, @NotNull Integer xMin,
                          @NotNull Integer xMax, @NotNull Integer yMin, @NotNull Integer yMax, @NotNull Integer score,
                          @NotNull Integer tagId, @NotNull String comment) {
    }

    record CreateTableBlockDto(int xMin, int xMax, int yMin, int yMax) {
    }

    record UpdateBlockTagDto(int blockId, @NotNull Integer tagId) {
    }

    record UpdateBlockTagByProcessorDto(int blockId, @NotNull Integer tagId, Integer tagExplainabilityId) {
    }

    record UpdateBlockCommentDto(int blockId, @NotNull String comment) {
    }

    record UpdateBlockBboxDto(int xMin, int xMax, int yMin, int yMax) {
    }

    @Getter
    @AllArgsConstructor
    @RegisterForReflection
    class BlockTagOnly {
        Integer blockId;
        Integer tagId;
        String tag;
        Short pageNum;
    }
}
