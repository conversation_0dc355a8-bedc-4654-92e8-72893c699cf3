package com.walnut.vegaspread.extraction.model;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

public interface DbHeader {

    @Builder
    @Data
    class HeaderDto {
        private int tableId;
        private int headerId;
        private Integer pos;
        private String text;
        private Integer xMin;
        private Integer xMax;
        private Integer yMin;
        private Integer yMax;
        private Integer score;
    }

    record InsertHeaderDtoForProcessor(int tableId, int headerId, @NotNull String text, int xMin, int xMax,
                                       int yMin, int yMax, int score) {
    }

    record InsertHeaderDto(int tableId, int pos, @NotNull String text) {
    }

    record DeleteHeaderDto(int tableId, int headerId) {
    }

    record UpdateHeaderTextDto(int tableId, int headerId, @NotNull String text) {
    }
}
