package com.walnut.vegaspread.extraction.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.quarkus.runtime.annotations.RegisterForReflection;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@RegisterForReflection
public interface JSWSummaryOutput {

    record FinancialsDto(List<FinancialDto> financials) {
    }

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    record FinancialDto(LocalDate year, LocalDate statedOn,
                        StatementOfAssetsAndLiabilities statementOfAssetsAndLiabilities,
                        StatementOfIncomeAndExpenditure statementOfIncomeAndExpenditure,
                        Map<String, Object> certifiers) {
    }

    record StatementOfAssetsAndLiabilities(
            Assets assets,
            SubTotals subTotals,
            Liabilities liabilities,
            Metadata metadata
    ) {
    }

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    record Assets(
            double grossFixedAssets,
            double depreciationAndAmortization,
            double investments,
            double loansAndAdvances,
            double inventories,
            double tradeReceivables,
            double cashAndCashEquivalents,
            double otherAssets,
            double netFixedAssets
    ) {
    }

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    record SubTotals(
            double givenAssetsTotal,
            double givenLiabilitiesTotal
    ) {
    }

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    record Liabilities(
            double contributionReceived,
            double reservesAndSurplus,
            double securedLoan,
            double unsecuredLoan,
            double shortTermBorrowing,
            double tradePayables,
            double otherLiabilities,
            double provisionsForTaxation,
            double provisionsForContingencies,
            double provisionsForInsurance,
            double otherProvisions
    ) {
    }

    record Metadata(
            String docId
    ) {
    }

    record StatementOfIncomeAndExpenditure(
            LineItems lineItems,
            @JsonProperty("revenue_breakup")
            RevenueBreakup revenueBreakup,
            @JsonProperty("depreciation_breakup")
            DepreciationBreakup depreciationBreakup,
            Metadata metadata
    ) {
    }

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    record LineItems(
            double netRevenue,
            double operatingCost,
            double totalCostOfMaterialsConsumed,
            double totalPurchasesOfStockInTrade,
            double totalChangesInInventoriesOrFinishedGoods,
            double totalEmployeeBenefitExpense,
            double totalOtherExpenses,
            double operatingProfit,
            double otherIncome,
            double depreciation,
            double profitBeforeInterestAndTax,
            double interest,
            double profitBeforeTaxAndExceptionalItemsBeforeTax,
            double exceptionalItemsBeforeTax,
            double profitBeforeTax,
            double incomeTax,
            double profitForPeriodFromContinuingOperations,
            double profitFromDiscontinuingOperationAfterTax,
            double minorityInterestAndProfitFromAssociatesAndJointVentures,
            double profitAfterTax
    ) {
    }

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    record RevenueBreakup(
            double saleOfGoodsManufacturedDomestic,
            double saleOfGoodsTradedDomestic,
            double saleOrSupplyOfServicesDomestic,
            double saleOfGoodsManufacturedExport,
            double saleOfGoodsTradedExport,
            double saleOrSupplyOfServicesExport
    ) {
    }

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    record DepreciationBreakup(double depreciationAndAmortization) {
    }
}
