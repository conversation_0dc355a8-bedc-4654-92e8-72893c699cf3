package com.walnut.vegaspread.extraction.model.audit;

import jakarta.validation.constraints.NotNull;

import java.io.Serializable;

/**
 * Audit DTO for TableTagEntity
 */
public interface TableTagAuditDto {

    record Response(
            @NotNull Integer id,
            @NotNull String tag
    ) implements Serializable {
    }

    record Create(
            @NotNull String tag
    ) implements Serializable {
    }

    record Update(
            @NotNull Integer id,
            String tag
    ) implements Serializable {
    }
}
