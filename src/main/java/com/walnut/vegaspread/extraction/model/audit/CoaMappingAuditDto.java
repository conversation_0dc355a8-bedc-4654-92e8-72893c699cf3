package com.walnut.vegaspread.extraction.model.audit;

import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.util.UUID;

/**
 * Audit DTO for CoaMappingEntity
 */
public interface CoaMappingAuditDto {

    record Response(
            @NotNull Integer id,
            @NotNull Integer tableId,
            @NotNull Short rowId,
            @NotNull UUID docId,
            @NotNull Integer tableTypeId,
            @NotNull String rowParent,
            @NotNull String text,
            String fsHeader,
            String fsText,
            Integer coaId,
            Boolean isApproved
    ) implements Serializable {
    }

    record Create(
            @NotNull Integer tableId,
            @NotNull Short rowId,
            @NotNull UUID docId,
            @NotNull Integer tableTypeId,
            @NotNull String rowParent,
            @NotNull String text,
            String fsHeader,
            String fsText,
            Integer coaId,
            Boolean isApproved
    ) implements Serializable {
    }

    record Update(
            @NotNull Integer id,
            Integer tableId,
            Short rowId,
            UUID docId,
            Integer tableTypeId,
            String rowParent,
            String text,
            String fsHeader,
            String fsText,
            Integer coaId,
            Boolean isApproved
    ) implements Serializable {
    }
}
