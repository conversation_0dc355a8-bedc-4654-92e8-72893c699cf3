package com.walnut.vegaspread.extraction.model;

import java.util.List;
import java.util.UUID;

public interface ResponseDto {
    /**
     * DTO for {@link ResponseDto}
     */
    record LayoutBlock(Integer blockId, UUID docId, Short pageNum, BlockTypeEnum blockType, Integer tagId, String tag,
                       String comment,
                       Short xMin, Short xMax, Short yMin, Short yMax, Byte score, Integer tagExplainabilityId,
                       Integer tagExplainabilityPageNum, UUID tagExplainabilityDocId, List<TableHeader> tableHeaders,
                       List<TableRow> tableRows) {
    }

    /**
     * DTO for {@link com.walnut.vegaspread.extraction.entity.TableHeaderEntity}
     */
    record TableHeader(Integer blockId, Byte headerId, String text, Short xMin, Short xMax, Short yMin,
                       Short yMax, Byte score) {
    }

    /**
     * DTO for {@link com.walnut.vegaspread.extraction.entity.TableRowEntity}
     */
    record TableRow(Integer blockId, Short rowId, Integer coaId, Boolean useCoa, Byte coaScore, String parentText,
                    List<Integer> headerIds, List<String> cellsText, Byte score, String comment, Short xMin,
                    Short xMax, Short yMin, Short yMax, LayoutBlock ntaTable, Integer explainabilityId,
                    Integer subtotalId, String subtotalName) {
    }
}
