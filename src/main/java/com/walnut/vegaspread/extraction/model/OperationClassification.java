package com.walnut.vegaspread.extraction.model;

import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;

import java.util.List;
import java.util.Map;

/**
 * Record to hold operation classification for extracted row data
 */
public record OperationClassification(
        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> createOperations,
        Map<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate, ExtractedTableRowCoaDataJoinEntity> updateOperations,
        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> deleteOperations
) {
}