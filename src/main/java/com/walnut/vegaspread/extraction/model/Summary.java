package com.walnut.vegaspread.extraction.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

public interface Summary {

    @Builder
    @Getter
    @Setter
    @ToString
    class SummaryRow {
        private Integer rowId;
        private Integer coaId;
        @JsonIgnore
        private String coaText;
        private Integer coaScore;
        private ArrayList<String> cellsText;
        @JsonIgnore
        private String category;
        private Integer tableId;
        @JsonIgnore
        private String tableTag;
        private Integer pageNum;
        @JsonProperty("xMin")
        private Integer xMin;
        @JsonProperty("xMax")
        private Integer xMax;
        @JsonProperty("yMin")
        private Integer yMin;
        @JsonProperty("yMax")
        private Integer yMax;
        private ArrayList<SummaryRow> children;
        private Boolean isValid;
        private Boolean sign;
    }

    record SummaryHeader(Integer headerId, String text, Boolean isNote) {
    }

    record SummaryOutput(List<SummaryHeader> headers, List<SummaryRow> rows) {
    }
}
