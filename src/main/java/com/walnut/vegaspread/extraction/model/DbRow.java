package com.walnut.vegaspread.extraction.model;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface DbRow {
    @Builder
    @Data
    class RowDto {
        private int tableId;
        private int rowId;
        private int pos;
        private List<String> cellsText;
        private Integer xMin;
        private Integer xMax;
        private Integer yMin;
        private Integer yMax;
        private Integer score;
        private String comment;
        private String parentText;
        private Integer ntaTableId;
        private Integer coaId;
        private Boolean useCoa;
        private Integer coaScore;
        private Integer explainabilityId;
        private List<Integer> headerIds;
    }

    record InsertRowDtoForProcessor(int tableId, int rowId, @NotNull List<String> cellsText, int xMin, int xMax,
                                    int yMin,
                                    int yMax, int score, String comment, String parentText) {
    }

    record InsertRowDto(int tableId, String text, int pos) {
    }

    record UpdateTextDto(int tableId, int rowId, @NotNull List<String> cellsText) {
    }

    record UpdateFyHeaderDto(int tableId, int rowId, @NotNull List<Integer> headerIds) {
    }

    record UpdateCoaDto(int tableId, int rowId, int coaId, boolean useCoa) {
    }

    record UpdateCommentDto(int tableId, int rowId, @NotNull String comment) {
    }

    record UpdateNtaLinkDto(int fsTableId, int rowId, int ntaTableId) {
    }

    record DeleteRowDto(int tableId, int rowId) {
    }
}
