package com.walnut.vegaspread.extraction.model.audit;

import com.walnut.vegaspread.extraction.entity.Bbox;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;

/**
 * Audit DTO for TableHeaderEntity
 */
public interface TableHeaderAuditDto {

    record Response(
            @NotNull Integer tableId,
            @NotNull Short headerId,
            @NotNull String text,
            Bbox bbox,
            @NotNull Byte score,
            @NotNull Integer pos
    ) implements Serializable {
    }

    record Create(
            @NotNull Integer tableId,
            @NotNull Short headerId,
            @NotNull String text,
            Bbox bbox,
            @NotNull Byte score,
            @NotNull Integer pos
    ) implements Serializable {
    }

    record Update(
            @NotNull Integer tableId,
            @NotNull Short headerId,
            String text,
            Bbox bbox,
            Byte score,
            Integer pos
    ) implements Serializable {
    }
}
