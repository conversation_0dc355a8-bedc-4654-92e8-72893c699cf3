package com.walnut.vegaspread.extraction.model.audit;

import com.walnut.vegaspread.extraction.entity.Bbox;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.util.List;

/**
 * Audit DTO for TableRowEntity
 */
public interface TableRowAuditDto {

    record Response(
            @NotNull Integer tableId,
            @NotNull Short rowId,
            String parentText,
            List<Integer> headerIds,
            @NotNull List<String> cellsText,
            @NotNull Byte score,
            String comment,
            Bbox bbox,
            Integer coaDataId,
            @NotNull Integer pos
    ) implements Serializable {
    }

    record Create(
            @NotNull Integer tableId,
            @NotNull Short rowId,
            String parentText,
            List<Integer> headerIds,
            @NotNull List<String> cellsText,
            @NotNull Byte score,
            String comment,
            Bbox bbox,
            Integer coaDataId,
            @NotNull Integer pos
    ) implements Serializable {
    }

    record Update(
            @NotNull Integer tableId,
            @NotNull Short rowId,
            String parentText,
            List<Integer> headerIds,
            List<String> cellsText,
            Byte score,
            String comment,
            Bbox bbox,
            Integer coaDataId,
            Integer pos
    ) implements Serializable {
    }
}
