package com.walnut.vegaspread.extraction.model.audit;

import com.walnut.vegaspread.extraction.entity.Bbox;
import com.walnut.vegaspread.extraction.model.BlockTypeEnum;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.util.UUID;

/**
 * Audit DTO for LayoutBlockEntity
 */
public interface LayoutBlockAuditDto {

    record Response(
            @NotNull Integer blockId,
            @NotNull UUID docId,
            @NotNull Short pageNum,
            @NotNull BlockTypeEnum blockType,
            String tag,
            @NotNull String comment,
            Bbox bbox,
            @NotNull Byte score,
            Integer tagExplainabilityId
    ) implements Serializable {
    }

    record Create(
            @NotNull UUID docId,
            @NotNull Short pageNum,
            @NotNull BlockTypeEnum blockType,
            String tag,
            @NotNull String comment,
            Bbox bbox,
            @NotNull Byte score,
            Integer tagExplainabilityId
    ) implements Serializable {
    }

    record Update(
            @NotNull Integer blockId,
            UUID docId,
            Short pageNum,
            BlockTypeEnum blockType,
            String tag,
            String comment,
            Bbox bbox,
            Byte score,
            Integer tagExplainabilityId
    ) implements Serializable {
    }
}
