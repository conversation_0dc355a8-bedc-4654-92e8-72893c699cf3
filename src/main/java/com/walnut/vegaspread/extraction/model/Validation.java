package com.walnut.vegaspread.extraction.model;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import jakarta.ws.rs.core.Response;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public interface Validation {

    @Getter
    enum ValidationError {

        TABLE_NOT_STANDARDIZED("Table must be standardized"),
        LINKED_FS_ROW_MAPPED("Row with NTA link must not be mapped to COA"),
        FS_ROW_INCORRECT_HEAD("FS Row must have a valid header"),
        NTA_TABLE_NO_MAPPING("NTA Table must have at least one COA mapping"),
        NTA_TABLE_NO_FS_LINK("NTA Table must be linked to a BS/IS row"),
        INVALID_FS_ROW_HEADER_FOR_LINKED_TABLE_TAG("NTA Table Tag must match linked row header"),
        INVALID_TABLE_FOR_LINKED_TABLE_TAG(
                "Table tagged BS.NTA or IS.NTA must be linked to a row in corresponding FS table"),
        COA_MAPPED_ROW_NO_TABLE_TAG("Row mapped to a COA must belong to a tagged table "),
        LVL1_CATEGORY_TABLE_CATEGORY_MISMATCH("COA mapping category must match table tag category"),
        LVL1_CATEGORY_HEAD_MISMATCH("COA mapping category must match row header");
        private final String message;

        ValidationError(String message) {
            this.message = message;
        }
    }

    @Getter
    enum RowCategory {
        CURRENT_ASSETS(List.of("currentassets", "currentasset")),
        NON_CURRENT_ASSETS(List.of("noncurrentassets", "noncurrentasset")),
        CURRENT_LIABILITIES(List.of("currentliabilities")),
        NON_CURRENT_LIABILITIES(List.of("noncurrentliabilities")),
        EQUITY(List.of("equity", "networth", "changesinequity")),
        ASSETS(List.of("assets", "asset")),
        LIABILITIES(List.of("liabilities")),
        INCOME_STATEMENT(List.of("incomestatement")),
        NA(List.of("NA"));

        private final List<String> messages;

        RowCategory(List<String> messages) {
            this.messages = messages;
        }

        public static List<String> getValues() {
            return Arrays.stream(RowCategory.values())
                    .flatMap(rowCategory -> rowCategory.getMessages().stream())
                    .collect(Collectors.toList());
        }

        public static RowCategory fromValue(String value) {
            for (RowCategory category : RowCategory.values()) {
                if (category.messages.stream().anyMatch(message -> message.equalsIgnoreCase(value))) {
                    return category;
                }
            }
            ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR,
                    "Unknown value: " + value);
            return null;
        }
    }

    record ResponseDto(int tableId, int rowId, int pageNum, String tag, String rowText, String coaText, String msg) {
    }
}
