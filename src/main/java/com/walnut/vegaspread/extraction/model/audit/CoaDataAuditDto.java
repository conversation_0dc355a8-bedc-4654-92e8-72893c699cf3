package com.walnut.vegaspread.extraction.model.audit;

import jakarta.validation.constraints.NotNull;

import java.io.Serializable;

/**
 * Audit DTO for CoaDataEntity
 */
public interface CoaDataAuditDto {

    record Response(
            @NotNull Integer id,
            Integer coaId,
            Byte coaScore,
            Boolean useCoa
    ) implements Serializable {
    }

    record Create(
            Integer coaId,
            Byte coaScore,
            Boolean useCoa
    ) implements Serializable {
    }

    record Update(
            @NotNull Integer id,
            Integer coaId,
            Byte coaScore,
            Boolean useCoa
    ) implements Serializable {
    }
}
