package com.walnut.vegaspread.extraction.model;

import jakarta.validation.constraints.NotNull;

public interface ExtractedTableRowCoaDataJoinDto {

    record CreateOrUpdate(@NotNull Integer tableId, @NotNull Integer rowId, CoaDataDto.Create coaData,
                          Integer explainabilityId) {
    }

    record Response(@NotNull Integer tableId, @NotNull Integer rowId, CoaDataDto.Response coaData,
                    MappedRowDto explainability) {
    }
}