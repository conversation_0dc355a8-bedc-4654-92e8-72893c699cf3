package com.walnut.vegaspread.extraction.audit.rollback;

import com.walnut.vegaspread.extraction.entity.CoaDataEntity;
import com.walnut.vegaspread.extraction.repository.CoaDataRepository;
import com.walnut.vegaspread.common.service.audit.envers.EntityRollbackStrategy;
import jakarta.enterprise.context.ApplicationScoped;
import org.hibernate.envers.RevisionType;

@ApplicationScoped
public class CoaDataEntityRollbackStrategy implements EntityRollbackStrategy<CoaDataEntity, Integer> {

    private final CoaDataRepository coaDataRepository;

    public CoaDataEntityRollbackStrategy(CoaDataRepository coaDataRepository) {
        this.coaDataRepository = coaDataRepository;
    }

    @Override
    public Class<CoaDataEntity> getEntityClass() {
        return CoaDataEntity.class;
    }

    @Override
    public CoaDataEntity findById(Integer id) {
        return coaDataRepository.findById(id);
    }

    @Override
    public void persist(CoaDataEntity entity) {
        coaDataRepository.persist(entity);
    }

    @Override
    public void delete(Integer id) {
        CoaDataEntity entity = findById(id);
        if (entity != null) {
            coaDataRepository.delete(entity);
        }
    }

    @Override
    public Integer getEntityId(CoaDataEntity entity) {
        return entity.getId();
    }

    @Override
    public CoaDataEntity createNewEntity() {
        return new CoaDataEntity();
    }

    @Override
    public void copyAuditedFields(CoaDataEntity source, CoaDataEntity target, RevisionType revisionType, boolean isEntityCreation) {
        // Copy all audited fields from source to target
        target.setId(source.getId());
        target.setCoaId(source.getCoaId());
        target.setCoaScore(source.getCoaScore());
        target.setUseCoa(source.getUseCoa());
    }
}
