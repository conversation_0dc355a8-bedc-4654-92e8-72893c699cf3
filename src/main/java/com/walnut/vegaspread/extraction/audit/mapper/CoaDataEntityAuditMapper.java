package com.walnut.vegaspread.extraction.audit.mapper;

import com.walnut.vegaspread.extraction.entity.CoaDataEntity;
import com.walnut.vegaspread.extraction.model.audit.CoaDataAuditDto;
import com.walnut.vegaspread.common.model.audit.envers.BaseEntityMapper;
import org.mapstruct.Mapper;

@Mapper(componentModel = "cdi")
public interface CoaDataEntityAuditMapper extends BaseEntityMapper<CoaDataEntity, CoaDataAuditDto.Response> {

    @Override
    CoaDataAuditDto.Response toDto(CoaDataEntity entity);
}
