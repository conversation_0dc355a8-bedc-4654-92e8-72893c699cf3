package com.walnut.vegaspread.extraction.audit.service;

import com.walnut.vegaspread.extraction.audit.rollback.CoaMappingEntityRollbackStrategy;
import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import com.walnut.vegaspread.extraction.audit.mapper.CoaMappingEntityAuditMapper;
import com.walnut.vegaspread.extraction.model.audit.CoaMappingAuditDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditFilterDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditRequestDto;
import com.walnut.vegaspread.common.service.audit.envers.GenericAuditService;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;

import java.util.List;
import java.util.UUID;

@ApplicationScoped
public class CoaMappingAuditService {

    private final GenericAuditService genericAuditService;
    private final CoaMappingEntityRollbackStrategy rollbackStrategy;

    public CoaMappingAuditService(GenericAuditService genericAuditService, 
                                 CoaMappingEntityRollbackStrategy rollbackStrategy) {
        this.genericAuditService = genericAuditService;
        this.rollbackStrategy = rollbackStrategy;
    }

    /**
     * Get paginated audit data for CoaMappingEntity with DTO mapping.
     *
     * @param request       The audit request
     * @param mapper        The CoaMappingEntity to CoaMappingAuditDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaMappingAuditDto.Response>> getPaginatedAuditsAsDto(
            AuditRequestDto request, CoaMappingEntityAuditMapper mapper, EntityManager entityManager) {
        return genericAuditService.getPaginatedAuditsAsDto(CoaMappingEntity.class, request, mapper, entityManager);
    }

    /**
     * Convenience method to get paginated audits for a specific CoaMapping ID.
     *
     * @param coaMappingId  The CoaMapping ID to get audits for
     * @param mapper        The CoaMappingEntity to CoaMappingAuditDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaMappingAuditDto.Response>> getAuditForCoaMappingId(
            Integer coaMappingId, CoaMappingEntityAuditMapper mapper, EntityManager entityManager) {

        // Create a request to filter by coaMappingId
        var filterDto = new AuditFilterDto(
                "id",
                coaMappingId,
                AuditFilterDto.FilterOperation.EQUALS
        );

        var request = new AuditRequestDto(
                List.of(filterDto),
                null,
                1,
                100
        );

        return getPaginatedAuditsAsDto(request, mapper, entityManager);
    }

    /**
     * Convenience method to get paginated audits for a specific document.
     *
     * @param docId         The document ID to get audits for
     * @param mapper        The CoaMappingEntity to CoaMappingAuditDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaMappingAuditDto.Response>> getAuditForDocId(
            UUID docId, CoaMappingEntityAuditMapper mapper, EntityManager entityManager) {

        // Create a request to filter by docId
        var filterDto = new AuditFilterDto(
                "docId",
                docId,
                AuditFilterDto.FilterOperation.EQUALS
        );

        var request = new AuditRequestDto(
                List.of(filterDto),
                null,
                1,
                100
        );

        return getPaginatedAuditsAsDto(request, mapper, entityManager);
    }

    /**
     * Convenience method to get paginated audits for a specific table row.
     *
     * @param tableId       The table ID to get audits for
     * @param rowId         The row ID to get audits for
     * @param mapper        The CoaMappingEntity to CoaMappingAuditDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaMappingAuditDto.Response>> getAuditForTableRow(
            Integer tableId, Short rowId, CoaMappingEntityAuditMapper mapper, EntityManager entityManager) {

        // Create filters for table and row
        var tableIdFilter = new AuditFilterDto(
                "tableId",
                tableId,
                AuditFilterDto.FilterOperation.EQUALS
        );
        
        var rowIdFilter = new AuditFilterDto(
                "rowId",
                rowId,
                AuditFilterDto.FilterOperation.EQUALS
        );

        var request = new AuditRequestDto(
                List.of(tableIdFilter, rowIdFilter),
                null,
                1,
                100
        );

        return getPaginatedAuditsAsDto(request, mapper, entityManager);
    }

    /**
     * Rollback CoaMappingEntity to a specific traceId state.
     *
     * @param traceId       The traceId to rollback to
     * @param entityManager The entity manager
     */
    @Transactional
    public void rollback(String traceId, EntityManager entityManager) {
        genericAuditService.rollback(traceId, rollbackStrategy, entityManager);
    }
}
