package com.walnut.vegaspread.extraction.audit.service;

import com.walnut.vegaspread.extraction.audit.rollback.CoaDataEntityRollbackStrategy;
import com.walnut.vegaspread.extraction.entity.CoaDataEntity;
import com.walnut.vegaspread.extraction.audit.mapper.CoaDataEntityAuditMapper;
import com.walnut.vegaspread.extraction.model.audit.CoaDataAuditDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditFilterDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditRequestDto;
import com.walnut.vegaspread.common.service.audit.envers.GenericAuditService;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;

import java.util.List;

@ApplicationScoped
public class CoaDataAuditService {

    private final GenericAuditService genericAuditService;
    private final CoaDataEntityRollbackStrategy rollbackStrategy;

    public CoaDataAuditService(GenericAuditService genericAuditService, 
                              CoaDataEntityRollbackStrategy rollbackStrategy) {
        this.genericAuditService = genericAuditService;
        this.rollbackStrategy = rollbackStrategy;
    }

    /**
     * Get paginated audit data for CoaDataEntity with DTO mapping.
     *
     * @param request       The audit request
     * @param mapper        The CoaDataEntity to CoaDataAuditDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaDataAuditDto.Response>> getPaginatedAuditsAsDto(
            AuditRequestDto request, CoaDataEntityAuditMapper mapper, EntityManager entityManager) {
        return genericAuditService.getPaginatedAuditsAsDto(CoaDataEntity.class, request, mapper, entityManager);
    }

    /**
     * Convenience method to get paginated audits for a specific CoaData ID.
     *
     * @param coaDataId     The CoaData ID to get audits for
     * @param mapper        The CoaDataEntity to CoaDataAuditDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaDataAuditDto.Response>> getAuditForCoaDataId(
            Integer coaDataId, CoaDataEntityAuditMapper mapper, EntityManager entityManager) {

        // Create a request to filter by coaDataId
        var filterDto = new AuditFilterDto(
                "id",
                coaDataId,
                AuditFilterDto.FilterOperation.EQUALS
        );

        var request = new AuditRequestDto(
                List.of(filterDto),
                null,
                1,
                100
        );

        return getPaginatedAuditsAsDto(request, mapper, entityManager);
    }

    /**
     * Convenience method to get paginated audits for a specific COA ID.
     *
     * @param coaId         The COA ID to get audits for
     * @param mapper        The CoaDataEntity to CoaDataAuditDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaDataAuditDto.Response>> getAuditForCoaId(
            Integer coaId, CoaDataEntityAuditMapper mapper, EntityManager entityManager) {

        // Create a request to filter by coaId
        var filterDto = new AuditFilterDto(
                "coaId",
                coaId,
                AuditFilterDto.FilterOperation.EQUALS
        );

        var request = new AuditRequestDto(
                List.of(filterDto),
                null,
                1,
                100
        );

        return getPaginatedAuditsAsDto(request, mapper, entityManager);
    }

    /**
     * Rollback CoaDataEntity to a specific traceId state.
     *
     * @param traceId       The traceId to rollback to
     * @param entityManager The entity manager
     */
    @Transactional
    public void rollback(String traceId, EntityManager entityManager) {
        genericAuditService.rollback(traceId, rollbackStrategy, entityManager);
    }
}
