package com.walnut.vegaspread.extraction.audit.rollback;

import com.walnut.vegaspread.extraction.entity.TableHeaderEntity;
import com.walnut.vegaspread.extraction.primarykey.TableHeaderPkId;
import com.walnut.vegaspread.extraction.repository.TableHeaderRepository;
import com.walnut.vegaspread.common.service.audit.envers.EntityRollbackStrategy;
import jakarta.enterprise.context.ApplicationScoped;
import org.hibernate.envers.RevisionType;

@ApplicationScoped
public class TableHeaderEntityRollbackStrategy implements EntityRollbackStrategy<TableHeaderEntity, Object[]> {

    private final TableHeaderRepository tableHeaderRepository;

    public TableHeaderEntityRollbackStrategy(TableHeaderRepository tableHeaderRepository) {
        this.tableHeaderRepository = tableHeaderRepository;
    }

    @Override
    public Class<TableHeaderEntity> getEntityClass() {
        return TableHeaderEntity.class;
    }

    @Override
    public TableHeaderEntity findById(Object[] compositeKey) {
        Integer tableId = (Integer) compositeKey[0];
        Short headerId = (Short) compositeKey[1];

        TableHeaderPkId pkId = new TableHeaderPkId(tableId, headerId.intValue());
        return tableHeaderRepository.findById(pkId);
    }

    @Override
    public void persist(TableHeaderEntity entity) {
        tableHeaderRepository.persist(entity);
    }

    @Override
    public void delete(Object[] compositeKey) {
        TableHeaderEntity entity = findById(compositeKey);
        if (entity != null) {
            tableHeaderRepository.delete(entity);
        }
    }

    @Override
    public Object[] getEntityId(TableHeaderEntity entity) {
        return new Object[]{
            entity.getTableHeaderPkId().getTableId(),
            entity.getTableHeaderPkId().getHeaderId()
        };
    }

    @Override
    public TableHeaderEntity createNewEntity() {
        return new TableHeaderEntity();
    }

    @Override
    public void copyAuditedFields(TableHeaderEntity source, TableHeaderEntity target, RevisionType revisionType, boolean isEntityCreation) {
        // Copy all audited fields from source to target
        target.setTableHeaderPkId(source.getTableHeaderPkId());
        target.setText(source.getText());
        target.setBbox(source.getBbox());
        target.setScore(source.getScore());
        target.setPos(source.getPos());
        target.setLayoutBlock(source.getLayoutBlock());
    }
}
