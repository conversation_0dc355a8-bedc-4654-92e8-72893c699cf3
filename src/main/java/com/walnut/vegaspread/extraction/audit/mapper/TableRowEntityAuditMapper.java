package com.walnut.vegaspread.extraction.audit.mapper;

import com.walnut.vegaspread.extraction.entity.CoaDataEntity;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.model.audit.TableRowAuditDto;
import com.walnut.vegaspread.common.model.audit.envers.BaseEntityMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(componentModel = "cdi")
public interface TableRowEntityAuditMapper extends BaseEntityMapper<TableRowEntity, TableRowAuditDto.Response> {

    @Named("mapCoaDataId")
    static Integer mapCoaDataId(CoaDataEntity coaData) {
        return coaData == null ? null : coaData.getId();
    }

    @Override
    @Mapping(target = "tableId", source = "tableRowPkId.tableId")
    @Mapping(target = "rowId", source = "tableRowPkId.rowId")
    @Mapping(target = "coaDataId", source = "coaData", qualifiedByName = "mapCoaDataId")
    TableRowAuditDto.Response toDto(TableRowEntity entity);
}
