package com.walnut.vegaspread.extraction.audit.rollback;

import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import com.walnut.vegaspread.extraction.repository.CoaMappingRepository;
import com.walnut.vegaspread.common.service.audit.envers.EntityRollbackStrategy;
import jakarta.enterprise.context.ApplicationScoped;
import org.hibernate.envers.RevisionType;

@ApplicationScoped
public class CoaMappingEntityRollbackStrategy implements EntityRollbackStrategy<CoaMappingEntity, Integer> {

    private final CoaMappingRepository coaMappingRepository;

    public CoaMappingEntityRollbackStrategy(CoaMappingRepository coaMappingRepository) {
        this.coaMappingRepository = coaMappingRepository;
    }

    @Override
    public Class<CoaMappingEntity> getEntityClass() {
        return CoaMappingEntity.class;
    }

    @Override
    public CoaMappingEntity findById(Integer id) {
        return coaMappingRepository.findById(id);
    }

    @Override
    public void persist(CoaMappingEntity entity) {
        coaMappingRepository.persist(entity);
    }

    @Override
    public void delete(Integer id) {
        CoaMappingEntity entity = findById(id);
        if (entity != null) {
            coaMappingRepository.delete(entity);
        }
    }

    @Override
    public Integer getEntityId(CoaMappingEntity entity) {
        return entity.getId();
    }

    @Override
    public CoaMappingEntity createNewEntity() {
        return new CoaMappingEntity();
    }

    @Override
    public void copyAuditedFields(CoaMappingEntity source, CoaMappingEntity target, RevisionType revisionType, boolean isEntityCreation) {
        // Copy all audited fields from source to target
        target.setId(source.getId());
        target.setTableId(source.getTableId());
        target.setRowId(source.getRowId());
        target.setDocId(source.getDocId());
        target.setTableTypeId(source.getTableTypeId());
        target.setRowParent(source.getRowParent());
        target.setText(source.getText());
        target.setFsHeader(source.getFsHeader());
        target.setFsText(source.getFsText());
        target.setCoaId(source.getCoaId());
        target.setIsApproved(source.getIsApproved());
    }
}
