package com.walnut.vegaspread.extraction.audit.mapper;

import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.entity.TableTagEntity;
import com.walnut.vegaspread.extraction.model.audit.LayoutBlockAuditDto;
import com.walnut.vegaspread.common.model.audit.envers.BaseEntityMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(componentModel = "cdi")
public interface LayoutBlockEntityAuditMapper extends BaseEntityMapper<LayoutBlockEntity, LayoutBlockAuditDto.Response> {

    @Named("mapTagToString")
    static String mapTagToString(TableTagEntity tag) {
        return tag == null ? null : tag.getTag();
    }

    @Override
    @Mapping(target = "tag", source = "tag", qualifiedByName = "mapTagToString")
    LayoutBlockAuditDto.Response toDto(LayoutBlockEntity entity);
}
