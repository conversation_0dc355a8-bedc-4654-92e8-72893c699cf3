package com.walnut.vegaspread.extraction.audit.mapper;

import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import com.walnut.vegaspread.extraction.model.audit.CoaMappingAuditDto;
import com.walnut.vegaspread.common.model.audit.envers.BaseEntityMapper;
import org.mapstruct.Mapper;

@Mapper(componentModel = "cdi")
public interface CoaMappingEntityAuditMapper extends BaseEntityMapper<CoaMappingEntity, CoaMappingAuditDto.Response> {

    @Override
    CoaMappingAuditDto.Response toDto(CoaMappingEntity entity);
}
