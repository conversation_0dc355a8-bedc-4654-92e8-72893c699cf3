package com.walnut.vegaspread.extraction.audit.mapper;

import com.walnut.vegaspread.common.model.audit.envers.BaseEntityMapper;
import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.model.audit.ExtractedTableRowCoaDataJoinAuditDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(componentModel = "cdi")
public interface ExtractedTableRowCoaDataJoinEntityAuditMapper extends BaseEntityMapper<ExtractedTableRowCoaDataJoinEntity, ExtractedTableRowCoaDataJoinAuditDto.Response> {

    @Named("mapExplainabilityId")
    static Integer mapExplainabilityId(CoaMappingEntity explainability) {
        return explainability == null ? null : explainability.getId();
    }

    @Override
    @Mapping(target = "tableId", source = "extractedTableRowCoaDataPkId.tableRowPkId.tableId")
    @Mapping(target = "rowId", source = "extractedTableRowCoaDataPkId.tableRowPkId.rowId")
    @Mapping(target = "coaDataId", source = "extractedTableRowCoaDataPkId.coaDataId")
    @Mapping(target = "explainabilityId", source = "explainability", qualifiedByName = "mapExplainabilityId")
    ExtractedTableRowCoaDataJoinAuditDto.Response toDto(ExtractedTableRowCoaDataJoinEntity entity);
}
