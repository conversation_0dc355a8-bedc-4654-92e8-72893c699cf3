package com.walnut.vegaspread.extraction.audit.controller;

import com.walnut.vegaspread.common.model.audit.envers.AuditRequestDto;
import com.walnut.vegaspread.extraction.audit.mapper.CoaDataEntityAuditMapper;
import com.walnut.vegaspread.extraction.audit.mapper.ExtractedTableRowCoaDataJoinEntityAuditMapper;
import com.walnut.vegaspread.extraction.audit.mapper.LayoutBlockEntityAuditMapper;
import com.walnut.vegaspread.extraction.audit.mapper.TableHeaderEntityAuditMapper;
import com.walnut.vegaspread.extraction.audit.mapper.TableRowEntityAuditMapper;
import com.walnut.vegaspread.extraction.audit.mapper.TableTagEntityAuditMapper;
import com.walnut.vegaspread.extraction.audit.mapper.CoaMappingEntityAuditMapper;
import com.walnut.vegaspread.extraction.audit.service.CoaDataAuditService;
import com.walnut.vegaspread.extraction.audit.service.CoaMappingAuditService;
import com.walnut.vegaspread.extraction.audit.service.ExtractedTableRowCoaDataJoinAuditService;
import com.walnut.vegaspread.extraction.audit.service.LayoutBlockAuditService;
import com.walnut.vegaspread.extraction.audit.service.TableHeaderAuditService;
import com.walnut.vegaspread.extraction.audit.service.TableRowAuditService;
import com.walnut.vegaspread.extraction.audit.service.TableTagAuditService;
import jakarta.persistence.EntityManager;
import jakarta.validation.Valid;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.Operation;

@Path("/audit")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ExtractionAuditController {

    private final EntityManager entityManager;
    private final TableRowAuditService tableRowAuditService;
    private final TableRowEntityAuditMapper tableRowMapper;
    private final TableHeaderAuditService tableHeaderAuditService;
    private final TableHeaderEntityAuditMapper tableHeaderMapper;
    private final TableTagAuditService tableTagAuditService;
    private final TableTagEntityAuditMapper tableTagMapper;
    private final LayoutBlockAuditService layoutBlockAuditService;
    private final LayoutBlockEntityAuditMapper layoutBlockMapper;
    private final CoaDataAuditService coaDataAuditService;
    private final CoaDataEntityAuditMapper coaDataMapper;
    private final ExtractedTableRowCoaDataJoinAuditService extractedJoinAuditService;
    private final ExtractedTableRowCoaDataJoinEntityAuditMapper extractedJoinMapper;
    private final CoaMappingAuditService coaMappingAuditService;
    private final CoaMappingEntityAuditMapper coaMappingMapper;

    public ExtractionAuditController(EntityManager entityManager,
                                     TableRowAuditService tableRowAuditService,
                                     TableRowEntityAuditMapper tableRowMapper,
                                     TableHeaderAuditService tableHeaderAuditService,
                                     TableHeaderEntityAuditMapper tableHeaderMapper,
                                     TableTagAuditService tableTagAuditService,
                                     TableTagEntityAuditMapper tableTagMapper,
                                     LayoutBlockAuditService layoutBlockAuditService,
                                     LayoutBlockEntityAuditMapper layoutBlockMapper,
                                     CoaDataAuditService coaDataAuditService,
                                     CoaDataEntityAuditMapper coaDataMapper,
                                     ExtractedTableRowCoaDataJoinAuditService extractedJoinAuditService,
                                     ExtractedTableRowCoaDataJoinEntityAuditMapper extractedJoinMapper,
                                     CoaMappingAuditService coaMappingAuditService,
                                     CoaMappingEntityAuditMapper coaMappingMapper) {
        this.entityManager = entityManager;
        this.tableRowAuditService = tableRowAuditService;
        this.tableRowMapper = tableRowMapper;
        this.tableHeaderAuditService = tableHeaderAuditService;
        this.tableHeaderMapper = tableHeaderMapper;
        this.tableTagAuditService = tableTagAuditService;
        this.tableTagMapper = tableTagMapper;
        this.layoutBlockAuditService = layoutBlockAuditService;
        this.layoutBlockMapper = layoutBlockMapper;
        this.coaDataAuditService = coaDataAuditService;
        this.coaDataMapper = coaDataMapper;
        this.extractedJoinAuditService = extractedJoinAuditService;
        this.extractedJoinMapper = extractedJoinMapper;
        this.coaMappingAuditService = coaMappingAuditService;
        this.coaMappingMapper = coaMappingMapper;
    }

    // TableRow Audit Endpoints
    @POST
    @Path("/table-row")
    @Operation(summary = "Get table row audit history")
    public Response getTableRowAudits(@Valid AuditRequestDto request) {
        var result = tableRowAuditService.getPaginatedAuditsAsDto(request, tableRowMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/table-row/{tableId}/{rowId}")
    @Operation(summary = "Get audit history for specific table row")
    public Response getTableRowAudit(@PathParam("tableId") Integer tableId, @PathParam("rowId") Short rowId) {
        var result = tableRowAuditService.getAuditForTableRowId(tableId, rowId, tableRowMapper, entityManager);
        return Response.ok(result).build();
    }

    @POST
    @Path("/table-row/rollback/{traceId}")
    @Operation(summary = "Rollback table row to specific trace ID")
    public Response rollbackTableRow(@PathParam("traceId") String traceId) {
        tableRowAuditService.rollback(traceId, entityManager);
        return Response.ok().build();
    }

    // TableHeader Audit Endpoints
    @POST
    @Path("/table-header")
    @Operation(summary = "Get table header audit history")
    public Response getTableHeaderAudits(@Valid AuditRequestDto request) {
        var result = tableHeaderAuditService.getPaginatedAuditsAsDto(request, tableHeaderMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/table-header/{tableId}/{headerId}")
    @Operation(summary = "Get audit history for specific table header")
    public Response getTableHeaderAudit(@PathParam("tableId") Integer tableId, @PathParam("headerId") Short headerId) {
        var result = tableHeaderAuditService.getAuditForTableHeaderId(tableId, headerId, tableHeaderMapper,
                entityManager);
        return Response.ok(result).build();
    }

    @POST
    @Path("/table-header/rollback/{traceId}")
    @Operation(summary = "Rollback table header to specific trace ID")
    public Response rollbackTableHeader(@PathParam("traceId") String traceId) {
        tableHeaderAuditService.rollback(traceId, entityManager);
        return Response.ok().build();
    }

    // TableTag Audit Endpoints
    @POST
    @Path("/table-tag")
    @Operation(summary = "Get table tag audit history")
    public Response getTableTagAudits(@Valid AuditRequestDto request) {
        var result = tableTagAuditService.getPaginatedAuditsAsDto(request, tableTagMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/table-tag/{tagId}")
    @Operation(summary = "Get audit history for specific table tag")
    public Response getTableTagAudit(@PathParam("tagId") Integer tagId) {
        var result = tableTagAuditService.getAuditForTableTagId(tagId, tableTagMapper, entityManager);
        return Response.ok(result).build();
    }

    @POST
    @Path("/table-tag/rollback/{traceId}")
    @Operation(summary = "Rollback table tag to specific trace ID")
    public Response rollbackTableTag(@PathParam("traceId") String traceId) {
        tableTagAuditService.rollback(traceId, entityManager);
        return Response.ok().build();
    }

    // LayoutBlock Audit Endpoints
    @POST
    @Path("/layout-block")
    @Operation(summary = "Get layout block audit history")
    public Response getLayoutBlockAudits(@Valid AuditRequestDto request) {
        var result = layoutBlockAuditService.getPaginatedAuditsAsDto(request, layoutBlockMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/layout-block/{blockId}")
    @Operation(summary = "Get audit history for specific layout block")
    public Response getLayoutBlockAudit(@PathParam("blockId") Integer blockId) {
        var result = layoutBlockAuditService.getAuditForLayoutBlockId(blockId, layoutBlockMapper, entityManager);
        return Response.ok(result).build();
    }

    @POST
    @Path("/layout-block/rollback/{traceId}")
    @Operation(summary = "Rollback layout block to specific trace ID")
    public Response rollbackLayoutBlock(@PathParam("traceId") String traceId) {
        layoutBlockAuditService.rollback(traceId, entityManager);
        return Response.ok().build();
    }

    // CoaData Audit Endpoints
    @POST
    @Path("/coa-data")
    @Operation(summary = "Get COA data audit history")
    public Response getCoaDataAudits(@Valid AuditRequestDto request) {
        var result = coaDataAuditService.getPaginatedAuditsAsDto(request, coaDataMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/coa-data/{coaDataId}")
    @Operation(summary = "Get audit history for specific COA data")
    public Response getCoaDataAudit(@PathParam("coaDataId") Integer coaDataId) {
        var result = coaDataAuditService.getAuditForCoaDataId(coaDataId, coaDataMapper, entityManager);
        return Response.ok(result).build();
    }

    @POST
    @Path("/coa-data/rollback/{traceId}")
    @Operation(summary = "Rollback COA data to specific trace ID")
    public Response rollbackCoaData(@PathParam("traceId") String traceId) {
        coaDataAuditService.rollback(traceId, entityManager);
        return Response.ok().build();
    }

    // ExtractedTableRowCoaDataJoin Audit Endpoints
    @POST
    @Path("/extracted-join")
    @Operation(summary = "Get extracted table row COA data join audit history")
    public Response getExtractedJoinAudits(@Valid AuditRequestDto request) {
        var result = extractedJoinAuditService.getPaginatedAuditsAsDto(request, extractedJoinMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/extracted-join/{tableId}/{rowId}/{coaDataId}")
    @Operation(summary = "Get audit history for specific extracted join entity")
    public Response getExtractedJoinAudit(@PathParam("tableId") Integer tableId,
                                          @PathParam("rowId") Short rowId,
                                          @PathParam("coaDataId") Integer coaDataId) {
        var result = extractedJoinAuditService.getAuditForJoinEntity(tableId, rowId, coaDataId, extractedJoinMapper,
                entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/extracted-join/table-row/{tableId}/{rowId}")
    @Operation(summary = "Get audit history for all join entities of a table row")
    public Response getExtractedJoinAuditForTableRow(@PathParam("tableId") Integer tableId,
                                                     @PathParam("rowId") Short rowId) {
        var result = extractedJoinAuditService.getAuditForTableRow(tableId, rowId, extractedJoinMapper, entityManager);
        return Response.ok(result).build();
    }

    @POST
    @Path("/extracted-join/rollback/{traceId}")
    @Operation(summary = "Rollback extracted join entity to specific trace ID")
    public Response rollbackExtractedJoin(@PathParam("traceId") String traceId) {
        extractedJoinAuditService.rollback(traceId, entityManager);
        return Response.ok().build();
    }

    // CoaMapping Audit Endpoints
    @POST
    @Path("/coa-mapping")
    @Operation(summary = "Get COA mapping audit history")
    public Response getCoaMappingAudits(@Valid AuditRequestDto request) {
        var result = coaMappingAuditService.getPaginatedAuditsAsDto(request, coaMappingMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/coa-mapping/{coaMappingId}")
    @Operation(summary = "Get audit history for specific COA mapping")
    public Response getCoaMappingAudit(@PathParam("coaMappingId") Integer coaMappingId) {
        var result = coaMappingAuditService.getAuditForCoaMappingId(coaMappingId, coaMappingMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/coa-mapping/doc/{docId}")
    @Operation(summary = "Get audit history for COA mappings by document ID")
    public Response getCoaMappingAuditByDocId(@PathParam("docId") java.util.UUID docId) {
        var result = coaMappingAuditService.getAuditForDocId(docId, coaMappingMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/coa-mapping/table-row/{tableId}/{rowId}")
    @Operation(summary = "Get audit history for COA mappings by table row")
    public Response getCoaMappingAuditByTableRow(@PathParam("tableId") Integer tableId, @PathParam("rowId") Short rowId) {
        var result = coaMappingAuditService.getAuditForTableRow(tableId, rowId, coaMappingMapper, entityManager);
        return Response.ok(result).build();
    }

    @POST
    @Path("/coa-mapping/rollback/{traceId}")
    @Operation(summary = "Rollback COA mapping to specific trace ID")
    public Response rollbackCoaMapping(@PathParam("traceId") String traceId) {
        coaMappingAuditService.rollback(traceId, entityManager);
        return Response.ok().build();
    }
}
