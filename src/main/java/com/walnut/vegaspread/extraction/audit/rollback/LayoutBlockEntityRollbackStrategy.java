package com.walnut.vegaspread.extraction.audit.rollback;

import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.repository.LayoutBlockRepository;
import com.walnut.vegaspread.common.service.audit.envers.EntityRollbackStrategy;
import jakarta.enterprise.context.ApplicationScoped;
import org.hibernate.envers.RevisionType;

@ApplicationScoped
public class LayoutBlockEntityRollbackStrategy implements EntityRollbackStrategy<LayoutBlockEntity, Integer> {

    private final LayoutBlockRepository layoutBlockRepository;

    public LayoutBlockEntityRollbackStrategy(LayoutBlockRepository layoutBlockRepository) {
        this.layoutBlockRepository = layoutBlockRepository;
    }

    @Override
    public Class<LayoutBlockEntity> getEntityClass() {
        return LayoutBlockEntity.class;
    }

    @Override
    public LayoutBlockEntity findById(Integer blockId) {
        return layoutBlockRepository.findById(blockId);
    }

    @Override
    public void persist(LayoutBlockEntity entity) {
        layoutBlockRepository.persist(entity);
    }

    @Override
    public void delete(Integer blockId) {
        LayoutBlockEntity entity = findById(blockId);
        if (entity != null) {
            layoutBlockRepository.delete(entity);
        }
    }

    @Override
    public Integer getEntityId(LayoutBlockEntity entity) {
        return entity.getBlockId();
    }

    @Override
    public LayoutBlockEntity createNewEntity() {
        return new LayoutBlockEntity();
    }

    @Override
    public void copyAuditedFields(LayoutBlockEntity source, LayoutBlockEntity target, RevisionType revisionType, boolean isEntityCreation) {
        // Copy all audited fields from source to target
        target.setBlockId(source.getBlockId());
        target.setDocId(source.getDocId());
        target.setPageNum(source.getPageNum());
        target.setBlockType(source.getBlockType());
        target.setTag(source.getTag());
        target.setComment(source.getComment());
        target.setBbox(source.getBbox());
        target.setScore(source.getScore());
        target.setTagExplainabilityId(source.getTagExplainabilityId());

        // Note: tableHeaders and tableRows are @NotAudited to avoid cycles
        // They will need to be handled separately if needed
    }
}
