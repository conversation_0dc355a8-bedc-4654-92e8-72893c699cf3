package com.walnut.vegaspread.extraction.audit.service;

import com.walnut.vegaspread.extraction.audit.rollback.TableRowEntityRollbackStrategy;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.audit.mapper.TableRowEntityAuditMapper;
import com.walnut.vegaspread.extraction.model.audit.TableRowAuditDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditFilterDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditRequestDto;
import com.walnut.vegaspread.common.service.audit.envers.GenericAuditService;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;

import java.util.List;

@ApplicationScoped
public class TableRowAuditService {

    private final GenericAuditService genericAuditService;
    private final TableRowEntityRollbackStrategy rollbackStrategy;

    public TableRowAuditService(GenericAuditService genericAuditService, 
                               TableRowEntityRollbackStrategy rollbackStrategy) {
        this.genericAuditService = genericAuditService;
        this.rollbackStrategy = rollbackStrategy;
    }

    /**
     * Get paginated audit data for TableRowEntity with DTO mapping.
     *
     * @param request       The audit request
     * @param mapper        The TableRowEntity to TableRowAuditDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<TableRowAuditDto.Response>> getPaginatedAuditsAsDto(
            AuditRequestDto request, TableRowEntityAuditMapper mapper, EntityManager entityManager) {
        return genericAuditService.getPaginatedAuditsAsDto(TableRowEntity.class, request, mapper, entityManager);
    }

    /**
     * Convenience method to get paginated audits for a specific TableRow ID.
     *
     * @param tableId       The table ID to get audits for
     * @param rowId         The row ID to get audits for
     * @param mapper        The TableRowEntity to TableRowAuditDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<TableRowAuditDto.Response>> getAuditForTableRowId(
            Integer tableId, Short rowId, TableRowEntityAuditMapper mapper, EntityManager entityManager) {

        // Create filters for composite key
        var tableIdFilter = new AuditFilterDto(
                "tableRowPkId.tableId",
                tableId,
                AuditFilterDto.FilterOperation.EQUALS
        );
        
        var rowIdFilter = new AuditFilterDto(
                "tableRowPkId.rowId",
                rowId,
                AuditFilterDto.FilterOperation.EQUALS
        );

        var request = new AuditRequestDto(
                List.of(tableIdFilter, rowIdFilter),
                null,
                1,
                100
        );

        return getPaginatedAuditsAsDto(request, mapper, entityManager);
    }

    /**
     * Rollback TableRowEntity to a specific traceId state.
     *
     * @param traceId       The traceId to rollback to
     * @param entityManager The entity manager
     */
    @Transactional
    public void rollback(String traceId, EntityManager entityManager) {
        genericAuditService.rollback(traceId, rollbackStrategy, entityManager);
    }
}
