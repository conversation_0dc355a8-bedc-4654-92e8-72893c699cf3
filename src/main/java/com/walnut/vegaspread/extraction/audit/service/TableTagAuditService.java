package com.walnut.vegaspread.extraction.audit.service;

import com.walnut.vegaspread.extraction.audit.rollback.TableTagEntityRollbackStrategy;
import com.walnut.vegaspread.extraction.entity.TableTagEntity;
import com.walnut.vegaspread.extraction.audit.mapper.TableTagEntityAuditMapper;
import com.walnut.vegaspread.extraction.model.audit.TableTagAuditDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditFilterDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditRequestDto;
import com.walnut.vegaspread.common.service.audit.envers.GenericAuditService;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;

import java.util.List;

@ApplicationScoped
public class TableTagAuditService {

    private final GenericAuditService genericAuditService;
    private final TableTagEntityRollbackStrategy rollbackStrategy;

    public TableTagAuditService(GenericAuditService genericAuditService, 
                               TableTagEntityRollbackStrategy rollbackStrategy) {
        this.genericAuditService = genericAuditService;
        this.rollbackStrategy = rollbackStrategy;
    }

    /**
     * Get paginated audit data for TableTagEntity with DTO mapping.
     *
     * @param request       The audit request
     * @param mapper        The TableTagEntity to TableTagAuditDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<TableTagAuditDto.Response>> getPaginatedAuditsAsDto(
            AuditRequestDto request, TableTagEntityAuditMapper mapper, EntityManager entityManager) {
        return genericAuditService.getPaginatedAuditsAsDto(TableTagEntity.class, request, mapper, entityManager);
    }

    /**
     * Convenience method to get paginated audits for a specific TableTag ID.
     *
     * @param tagId         The TableTag ID to get audits for
     * @param mapper        The TableTagEntity to TableTagAuditDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<TableTagAuditDto.Response>> getAuditForTableTagId(
            Integer tagId, TableTagEntityAuditMapper mapper, EntityManager entityManager) {

        // Create a request to filter by tagId
        var filterDto = new AuditFilterDto(
                "id",
                tagId,
                AuditFilterDto.FilterOperation.EQUALS
        );

        var request = new AuditRequestDto(
                List.of(filterDto),
                null,
                1,
                100
        );

        return getPaginatedAuditsAsDto(request, mapper, entityManager);
    }

    /**
     * Rollback TableTagEntity to a specific traceId state.
     *
     * @param traceId       The traceId to rollback to
     * @param entityManager The entity manager
     */
    @Transactional
    public void rollback(String traceId, EntityManager entityManager) {
        genericAuditService.rollback(traceId, rollbackStrategy, entityManager);
    }
}
