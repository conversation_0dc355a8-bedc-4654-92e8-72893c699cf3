package com.walnut.vegaspread.extraction.audit.rollback;

import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.primarykey.ExtractedTableRowCoaDataPkId;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import com.walnut.vegaspread.extraction.repository.ExtractedRowCoaDataRepository;
import com.walnut.vegaspread.common.service.audit.envers.EntityRollbackStrategy;
import jakarta.enterprise.context.ApplicationScoped;
import org.hibernate.envers.RevisionType;

@ApplicationScoped
public class ExtractedTableRowCoaDataJoinEntityRollbackStrategy implements EntityRollbackStrategy<ExtractedTableRowCoaDataJoinEntity, Object[]> {

    private final ExtractedRowCoaDataRepository extractedRowCoaDataRepository;

    public ExtractedTableRowCoaDataJoinEntityRollbackStrategy(ExtractedRowCoaDataRepository extractedRowCoaDataRepository) {
        this.extractedRowCoaDataRepository = extractedRowCoaDataRepository;
    }

    @Override
    public Class<ExtractedTableRowCoaDataJoinEntity> getEntityClass() {
        return ExtractedTableRowCoaDataJoinEntity.class;
    }

    @Override
    public ExtractedTableRowCoaDataJoinEntity findById(Object[] compositeKey) {
        Integer tableId = (Integer) compositeKey[0];
        Short rowId = (Short) compositeKey[1];
        Integer coaDataId = (Integer) compositeKey[2];

        TableRowPkId tableRowPkId = new TableRowPkId(tableId, rowId.intValue());
        ExtractedTableRowCoaDataPkId pkId = new ExtractedTableRowCoaDataPkId(tableRowPkId, coaDataId);

        return extractedRowCoaDataRepository.findById(pkId);
    }

    @Override
    public void persist(ExtractedTableRowCoaDataJoinEntity entity) {
        extractedRowCoaDataRepository.persist(entity);
    }

    @Override
    public void delete(Object[] compositeKey) {
        ExtractedTableRowCoaDataJoinEntity entity = findById(compositeKey);
        if (entity != null) {
            extractedRowCoaDataRepository.delete(entity);
        }
    }

    @Override
    public Object[] getEntityId(ExtractedTableRowCoaDataJoinEntity entity) {
        return new Object[]{
            entity.getExtractedTableRowCoaDataPkId().getTableRowPkId().getTableId(),
            entity.getExtractedTableRowCoaDataPkId().getTableRowPkId().getRowId(),
            entity.getExtractedTableRowCoaDataPkId().getCoaDataId()
        };
    }

    @Override
    public ExtractedTableRowCoaDataJoinEntity createNewEntity() {
        return new ExtractedTableRowCoaDataJoinEntity();
    }

    @Override
    public void copyAuditedFields(ExtractedTableRowCoaDataJoinEntity source, ExtractedTableRowCoaDataJoinEntity target, RevisionType revisionType, boolean isEntityCreation) {
        // Copy all audited fields from source to target
        target.setExtractedTableRowCoaDataPkId(source.getExtractedTableRowCoaDataPkId());
        target.setExplainability(source.getExplainability());
    }
}
