package com.walnut.vegaspread.extraction.audit.rollback;

import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import com.walnut.vegaspread.extraction.repository.TableRowRepository;
import com.walnut.vegaspread.common.service.audit.envers.EntityRollbackStrategy;
import jakarta.enterprise.context.ApplicationScoped;
import org.hibernate.envers.RevisionType;

@ApplicationScoped
public class TableRowEntityRollbackStrategy implements EntityRollbackStrategy<TableRowEntity, Object[]> {

    private final TableRowRepository tableRowRepository;

    public TableRowEntityRollbackStrategy(TableRowRepository tableRowRepository) {
        this.tableRowRepository = tableRowRepository;
    }

    @Override
    public Class<TableRowEntity> getEntityClass() {
        return TableRowEntity.class;
    }

    @Override
    public TableRowEntity findById(Object[] compositeKey) {
        Integer tableId = (Integer) compositeKey[0];
        Short rowId = (Short) compositeKey[1];

        TableRowPkId pkId = new TableRowPkId(tableId, rowId.intValue());
        return tableRowRepository.findById(pkId);
    }

    @Override
    public void persist(TableRowEntity entity) {
        tableRowRepository.persist(entity);
    }

    @Override
    public void delete(Object[] compositeKey) {
        TableRowEntity entity = findById(compositeKey);
        if (entity != null) {
            tableRowRepository.delete(entity);
        }
    }

    @Override
    public Object[] getEntityId(TableRowEntity entity) {
        return new Object[]{
            entity.getTableRowPkId().getTableId(),
            entity.getTableRowPkId().getRowId()
        };
    }

    @Override
    public TableRowEntity createNewEntity() {
        return new TableRowEntity();
    }

    @Override
    public void copyAuditedFields(TableRowEntity source, TableRowEntity target, RevisionType revisionType, boolean isEntityCreation) {
        // Copy all audited fields from source to target
        target.setTableRowPkId(source.getTableRowPkId());
        target.setNtaTable(source.getNtaTable());
        target.setParentText(source.getParentText());
        target.setHeaderIds(source.getHeaderIds());
        target.setCellsText(source.getCellsText());
        target.setScore(source.getScore());
        target.setComment(source.getComment());
        target.setBbox(source.getBbox());
        target.setLayoutBlock(source.getLayoutBlock());
        target.setCoaData(source.getCoaData());
        target.setPos(source.getPos());
    }
}
