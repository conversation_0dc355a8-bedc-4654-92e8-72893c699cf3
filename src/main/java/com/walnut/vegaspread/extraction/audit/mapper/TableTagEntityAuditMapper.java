package com.walnut.vegaspread.extraction.audit.mapper;

import com.walnut.vegaspread.extraction.entity.TableTagEntity;
import com.walnut.vegaspread.extraction.model.audit.TableTagAuditDto;
import com.walnut.vegaspread.common.model.audit.envers.BaseEntityMapper;
import org.mapstruct.Mapper;

@Mapper(componentModel = "cdi")
public interface TableTagEntityAuditMapper extends BaseEntityMapper<TableTagEntity, TableTagAuditDto.Response> {

    @Override
    TableTagAuditDto.Response toDto(TableTagEntity entity);
}
