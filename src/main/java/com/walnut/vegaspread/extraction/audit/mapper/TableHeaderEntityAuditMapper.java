package com.walnut.vegaspread.extraction.audit.mapper;

import com.walnut.vegaspread.extraction.entity.TableHeaderEntity;
import com.walnut.vegaspread.extraction.model.audit.TableHeaderAuditDto;
import com.walnut.vegaspread.common.model.audit.envers.BaseEntityMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "cdi")
public interface TableHeaderEntityAuditMapper extends BaseEntityMapper<TableHeaderEntity, TableHeaderAuditDto.Response> {

    @Override
    @Mapping(target = "tableId", source = "tableHeaderPkId.tableId")
    @Mapping(target = "headerId", source = "tableHeaderPkId.headerId")
    TableHeaderAuditDto.Response toDto(TableHeaderEntity entity);
}
