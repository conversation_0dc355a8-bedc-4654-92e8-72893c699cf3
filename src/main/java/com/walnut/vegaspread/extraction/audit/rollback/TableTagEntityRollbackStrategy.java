package com.walnut.vegaspread.extraction.audit.rollback;

import com.walnut.vegaspread.extraction.entity.TableTagEntity;
import com.walnut.vegaspread.extraction.repository.TableTagRepository;
import com.walnut.vegaspread.common.service.audit.envers.EntityRollbackStrategy;
import jakarta.enterprise.context.ApplicationScoped;
import org.hibernate.envers.RevisionType;

@ApplicationScoped
public class TableTagEntityRollbackStrategy implements EntityRollbackStrategy<TableTagEntity, Integer> {

    private final TableTagRepository tableTagRepository;

    public TableTagEntityRollbackStrategy(TableTagRepository tableTagRepository) {
        this.tableTagRepository = tableTagRepository;
    }

    @Override
    public Class<TableTagEntity> getEntityClass() {
        return TableTagEntity.class;
    }

    @Override
    public TableTagEntity findById(Integer id) {
        return tableTagRepository.findById(id);
    }

    @Override
    public void persist(TableTagEntity entity) {
        tableTagRepository.persist(entity);
    }

    @Override
    public void delete(Integer id) {
        TableTagEntity entity = findById(id);
        if (entity != null) {
            tableTagRepository.delete(entity);
        }
    }

    @Override
    public Integer getEntityId(TableTagEntity entity) {
        return entity.getId();
    }

    @Override
    public TableTagEntity createNewEntity() {
        return new TableTagEntity();
    }

    @Override
    public void copyAuditedFields(TableTagEntity source, TableTagEntity target, RevisionType revisionType, boolean isEntityCreation) {
        // Copy all audited fields from source to target
        target.setId(source.getId());
        target.setTag(source.getTag());
        // Note: createdBy, createdTime, lastModifiedBy, lastModifiedTime are @NotAudited
        // so they won't be restored from audit data and will keep their current values
    }
}
