package com.walnut.vegaspread.extraction.audit.service;

import com.walnut.vegaspread.extraction.audit.rollback.LayoutBlockEntityRollbackStrategy;
import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.audit.mapper.LayoutBlockEntityAuditMapper;
import com.walnut.vegaspread.extraction.model.audit.LayoutBlockAuditDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditFilterDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditRequestDto;
import com.walnut.vegaspread.common.service.audit.envers.GenericAuditService;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;

import java.util.List;
import java.util.UUID;

@ApplicationScoped
public class LayoutBlockAuditService {

    private final GenericAuditService genericAuditService;
    private final LayoutBlockEntityRollbackStrategy rollbackStrategy;

    public LayoutBlockAuditService(GenericAuditService genericAuditService, 
                                  LayoutBlockEntityRollbackStrategy rollbackStrategy) {
        this.genericAuditService = genericAuditService;
        this.rollbackStrategy = rollbackStrategy;
    }

    /**
     * Get paginated audit data for LayoutBlockEntity with DTO mapping.
     *
     * @param request       The audit request
     * @param mapper        The LayoutBlockEntity to LayoutBlockAuditDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<LayoutBlockAuditDto.Response>> getPaginatedAuditsAsDto(
            AuditRequestDto request, LayoutBlockEntityAuditMapper mapper, EntityManager entityManager) {
        return genericAuditService.getPaginatedAuditsAsDto(LayoutBlockEntity.class, request, mapper, entityManager);
    }

    /**
     * Convenience method to get paginated audits for a specific LayoutBlock ID.
     *
     * @param blockId       The LayoutBlock ID to get audits for
     * @param mapper        The LayoutBlockEntity to LayoutBlockAuditDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<LayoutBlockAuditDto.Response>> getAuditForLayoutBlockId(
            Integer blockId, LayoutBlockEntityAuditMapper mapper, EntityManager entityManager) {

        // Create a request to filter by blockId
        var filterDto = new AuditFilterDto(
                "blockId",
                blockId,
                AuditFilterDto.FilterOperation.EQUALS
        );

        var request = new AuditRequestDto(
                List.of(filterDto),
                null,
                1,
                100
        );

        return getPaginatedAuditsAsDto(request, mapper, entityManager);
    }

    /**
     * Convenience method to get paginated audits for a specific document.
     *
     * @param docId         The document ID to get audits for
     * @param mapper        The LayoutBlockEntity to LayoutBlockAuditDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<LayoutBlockAuditDto.Response>> getAuditForDocId(
            UUID docId, LayoutBlockEntityAuditMapper mapper, EntityManager entityManager) {

        // Create a request to filter by docId
        var filterDto = new AuditFilterDto(
                "docId",
                docId,
                AuditFilterDto.FilterOperation.EQUALS
        );

        var request = new AuditRequestDto(
                List.of(filterDto),
                null,
                1,
                100
        );

        return getPaginatedAuditsAsDto(request, mapper, entityManager);
    }

    /**
     * Rollback LayoutBlockEntity to a specific traceId state.
     *
     * @param traceId       The traceId to rollback to
     * @param entityManager The entity manager
     */
    @Transactional
    public void rollback(String traceId, EntityManager entityManager) {
        genericAuditService.rollback(traceId, rollbackStrategy, entityManager);
    }
}
