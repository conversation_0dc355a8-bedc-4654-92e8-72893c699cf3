package com.walnut.vegaspread.extraction.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Objects;

@Getter
@Setter
@Entity
@NoArgsConstructor
@ToString
@Table(name = SubtotalEntity.TABLE_NAME, uniqueConstraints = {
        @UniqueConstraint(name = "unique_client_excel_row", columnNames = {SubtotalEntity.COA_CLIENT_COL_NAME,
                SubtotalEntity.CLIENT_COL_NAME, SubtotalEntity.EXCEL_ROW_NUM_COL_NAME})
})
public class SubtotalEntity {
    public static final String TABLE_NAME = "subtotal";
    public static final String ID_COL_NAME = "id";
    public static final String COA_CLIENT_COL_NAME = "coa_client";
    public static final String CLIENT_COL_NAME = "client";
    public static final String SUBTOTAL_NAME_COL_NAME = "subtotal_name";
    public static final String EXCEL_ROW_NUM_COL_NAME = "excel_row_number";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = ID_COL_NAME, nullable = false)
    private Integer id;

    @NotNull
    @Column(name = COA_CLIENT_COL_NAME, nullable = false)
    private String coaClient;

    @NotNull
    @Column(name = CLIENT_COL_NAME, nullable = false)
    private String client;

    @NotNull
    @Column(name = SUBTOTAL_NAME_COL_NAME, nullable = false)
    private String subtotalName;

    @NotNull
    @Column(name = EXCEL_ROW_NUM_COL_NAME)
    private Short excelRowNumber;

    public SubtotalEntity(SubtotalEntity other) {
        this.id = other.id;
        this.coaClient = other.coaClient;
        this.client = other.client;
        this.subtotalName = other.subtotalName;
        this.excelRowNumber = other.excelRowNumber;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof SubtotalEntity that)) return false;
        return Objects.equals(getId(), that.getId()) && Objects.equals(getCoaClient(),
                that.getCoaClient()) && Objects.equals(getClient(), that.getClient()) && Objects.equals(
                getSubtotalName(), that.getSubtotalName()) && Objects.equals(getExcelRowNumber(),
                that.getExcelRowNumber());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId(), getCoaClient(), getClient(), getSubtotalName(), getExcelRowNumber());
    }
}