package com.walnut.vegaspread.extraction.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.Objects;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Embeddable
public class Bbox {
    public static final String X_MIN_COL_NAME = "x_min";
    public static final String X_MAX_COL_NAME = "x_max";
    public static final String Y_MIN_COL_NAME = "y_min";
    public static final String Y_MAX_COL_NAME = "y_max";
    private static final String X_MIN_JSON_NAME = "xMin";
    private static final String X_MAX_JSON_NAME = "xMax";
    private static final String Y_MIN_JSON_NAME = "yMin";
    private static final String Y_MAX_JSON_NAME = "yMax";
    @Schema(type = SchemaType.INTEGER, example = "0")
    @JsonProperty(X_MIN_JSON_NAME)
    @Column(name = X_MIN_COL_NAME, nullable = false)
    private Short xMin;
    @Schema(type = SchemaType.INTEGER, example = "0")
    @JsonProperty(X_MAX_JSON_NAME)
    @Column(name = X_MAX_COL_NAME, nullable = false)
    private Short xMax;
    @Schema(type = SchemaType.INTEGER, example = "0")
    @JsonProperty(Y_MIN_JSON_NAME)
    @Column(name = Y_MIN_COL_NAME, nullable = false)
    private Short yMin;
    @Schema(type = SchemaType.INTEGER, example = "0")
    @JsonProperty(Y_MAX_JSON_NAME)
    @Column(name = Y_MAX_COL_NAME, nullable = false)
    private Short yMax;

    public Bbox(Integer xMin, Integer xMax, Integer yMin, Integer yMax) {
        this.xMin = xMin.shortValue();
        this.xMax = xMax.shortValue();
        this.yMin = yMin.shortValue();
        this.yMax = yMax.shortValue();
    }

    public Bbox(Bbox other) {
        this.xMin = other.xMin;
        this.xMax = other.xMax;
        this.yMin = other.yMin;
        this.yMax = other.yMax;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Bbox bbox)) return false;
        return Objects.equals(xMin, bbox.xMin) && Objects.equals(xMax, bbox.xMax) && Objects.equals(yMin,
                bbox.yMin) && Objects.equals(yMax, bbox.yMax);
    }

    @Override
    public int hashCode() {
        return Objects.hash(xMin, xMax, yMin, yMax);
    }
}
