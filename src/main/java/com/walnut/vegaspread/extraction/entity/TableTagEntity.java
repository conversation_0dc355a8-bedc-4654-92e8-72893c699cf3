package com.walnut.vegaspread.extraction.entity;

import com.walnut.vegaspread.common.model.extraction.TableTagDto;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Entity
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = TableTagEntity.TABLE_TAG_TABLE_NAME, uniqueConstraints = {
        @UniqueConstraint(name = "uk_table_tag_tag", columnNames = {TableTagEntity.TAG_COL_NAME})
})
public class TableTagEntity {
    public static final String TABLE_TAG_TABLE_NAME = "table_tag";
    public static final String CREATED_BY_COL_NAME = "created_by";
    public static final String CREATED_TIME_COL_NAME = "created_time";

    public static final String TAG_COL_NAME = "tag";
    public static final String LAST_MODIFIED_BY_COL_NAME = "last_modified_by";
    public static final String LAST_MODIFIED_TIME_COL_NAME = "last_modified_time";
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    public Integer id;

    @Size(max = 255)
    @NotNull
    @Column(name = TAG_COL_NAME, nullable = false)
    public String tag;

    @Size(max = 255)
    @NotNull
    @Column(name = CREATED_BY_COL_NAME, nullable = false)
    public String createdBy;

    @NotNull
    @Column(name = CREATED_TIME_COL_NAME, nullable = false)
    public LocalDateTime createdTime;

    @Size(max = 255)
    @NotNull
    @Column(name = LAST_MODIFIED_BY_COL_NAME, nullable = false)
    public String lastModifiedBy;

    @NotNull
    @Column(name = LAST_MODIFIED_TIME_COL_NAME, nullable = false)
    public LocalDateTime lastModifiedTime;

    public static List<TableTagDto.Response> toDtoList(
            List<TableTagEntity> tableTagEntities) {
        return tableTagEntities.stream()
                .map(TableTagEntity::toDto)
                .toList();
    }

    public TableTagDto.Response toDto() {
        return new TableTagDto.Response(this.id, this.tag, this.createdBy, this.createdTime,
                this.lastModifiedBy, this.lastModifiedTime);
    }
}
