package com.walnut.vegaspread.extraction.entity;

import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.util.UUID;

import static com.walnut.vegaspread.extraction.entity.SubtotalMappingEntity.DOC_ID_COL_NAME;
import static com.walnut.vegaspread.extraction.entity.SubtotalMappingEntity.SUBTOTAL_ID_COL_NAME;

@Getter
@Setter
@Entity
@Builder
@Table(name = SubtotalMappingEntity.TABLE_NAME, uniqueConstraints = {
        @UniqueConstraint(name = DOC_ID_COL_NAME, columnNames = {DOC_ID_COL_NAME, SUBTOTAL_ID_COL_NAME})
})
@NoArgsConstructor
@AllArgsConstructor
public class SubtotalMappingEntity {
    public static final String TABLE_NAME = "subtotal_mapping";
    public static final String DOC_ID_COL_NAME = "doc_id";
    public static final String SUBTOTAL_ID_COL_NAME = "subtotal_id";

    @Column(name = DOC_ID_COL_NAME, columnDefinition = "uuid", nullable = false)
    private UUID docId;

    @JsonUnwrapped
    @EmbeddedId
    private TableRowPkId id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = SUBTOTAL_ID_COL_NAME, referencedColumnName = SubtotalEntity.ID_COL_NAME, nullable = false)
    private SubtotalEntity subtotal;

    @Override
    public String toString() {
        return "SubtotalMappingEntity{" + "docId=" + docId +
                ", id=" + id +
                ", subtotal=" + subtotal +
                '}';
    }
}