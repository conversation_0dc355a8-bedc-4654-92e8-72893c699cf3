package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.extraction.entity.TableHeaderEntity;
import com.walnut.vegaspread.extraction.model.DbHeader;
import com.walnut.vegaspread.extraction.model.DbRow;
import com.walnut.vegaspread.extraction.model.ResponseDto;
import com.walnut.vegaspread.extraction.service.TableService;
import com.walnut.vegaspread.extraction.service.mapper.TableRowEntityMapper;
import io.quarkus.security.Authenticated;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/table/extracted")
@Authenticated
public class TableResource {

    private final TableService tableService;
    private final TableRowEntityMapper tableRowEntityMapper;

    public TableResource(TableService tableService,
                         TableRowEntityMapper tableRowEntityMapper) {
        this.tableService = tableService;

        this.tableRowEntityMapper = tableRowEntityMapper;
    }

    @Path("/rows/comments")
    @PATCH
    @RolesAllowed(Roles.MAP_COA)
    public List<DbRow.UpdateCommentDto> updateRowsComments(@NotNull List<DbRow.UpdateCommentDto> updateCommentDtos) {
        List<DbRow.RowDto> updateRowDtos = updateCommentDtos.stream()
                .map(dto -> DbRow.RowDto.builder()
                        .tableId(dto.tableId())
                        .rowId(dto.rowId())
                        .comment(dto.comment())
                        .build())
                .toList();
        tableService.createOrUpdateRows(updateRowDtos, true, false, false);
        return updateCommentDtos;
    }

    @Path("/text")
    @PATCH
    @RolesAllowed(Roles.EDIT_FIGURES)
    public List<DbRow.UpdateTextDto> updateRowsText(@NotNull List<DbRow.UpdateTextDto> updateTextDtos) {
        List<DbRow.RowDto> updateRowDtos = updateTextDtos.stream()
                .map(dto -> DbRow.RowDto.builder()
                        .tableId(dto.tableId())
                        .rowId(dto.rowId())
                        .cellsText(dto.cellsText())
                        .build())
                .toList();
        tableService.createOrUpdateRows(updateRowDtos, true, false, false);
        return updateTextDtos;
    }

    @Path("/header-text")
    @PATCH
    @RolesAllowed(Roles.EDIT_FIGURES)
    public List<DbHeader.UpdateHeaderTextDto> updateHeaderText(
            @NotNull List<DbHeader.UpdateHeaderTextDto> updateHeaderTextDtos) {
        List<DbHeader.HeaderDto> updateHeaderDtos = updateHeaderTextDtos.stream()
                .map(dto -> DbHeader.HeaderDto.builder()
                        .tableId(dto.tableId())
                        .headerId(dto.headerId())
                        .text(dto.text())
                        .build())
                .toList();
        tableService.createOrUpdateHeaders(updateHeaderDtos, true, false);
        return updateHeaderTextDtos;
    }

    @Path("/fyheaders")
    @PATCH
    @RolesAllowed(Roles.TAG_TABLE)
    public List<DbRow.UpdateFyHeaderDto> updateRowsFyHeaders(
            @NotNull List<DbRow.UpdateFyHeaderDto> updateFyHeaderDtos) {
        List<DbRow.RowDto> updateRowDtos = updateFyHeaderDtos.stream()
                .map(dto -> DbRow.RowDto.builder()
                        .tableId(dto.tableId())
                        .rowId(dto.rowId())
                        .headerIds(dto.headerIds())
                        .build())
                .toList();
        tableService.createOrUpdateRows(updateRowDtos, true, false, false);
        return updateFyHeaderDtos;
    }

    @Path("/coamap")
    @PATCH
    @RolesAllowed(Roles.MAP_COA)
    public List<DbRow.UpdateCoaDto> updateCoa(@NotNull List<DbRow.UpdateCoaDto> updateCoaDtos) {
        List<DbRow.RowDto> updateRowDtos = updateCoaDtos.stream()
                .map(dto -> DbRow.RowDto.builder()
                        .tableId(dto.tableId())
                        .rowId(dto.rowId())
                        .coaId(dto.coaId())
                        .useCoa(dto.useCoa())
                        .coaScore(100)
                        .explainabilityId(null)
                        .build())
                .toList();
        tableService.createOrUpdateRows(updateRowDtos, true, false, false);
        return updateCoaDtos;
    }

    @Path("/nta-link")
    @PATCH
    @RolesAllowed(Roles.TAG_TABLE)
    public List<DbRow.UpdateNtaLinkDto> updateNtaLink(@NotNull List<DbRow.UpdateNtaLinkDto> updateNtaLinkDtos) {
        List<DbRow.RowDto> updateRowDtos = updateNtaLinkDtos.stream()
                .map(dto -> DbRow.RowDto.builder()
                        .tableId(dto.fsTableId())
                        .rowId(dto.rowId())
                        .ntaTableId(dto.ntaTableId())
                        .build())
                .toList();
        tableService.createOrUpdateRows(updateRowDtos, true, false, false);
        return updateNtaLinkDtos;
    }

    @Path("/header")
    @POST
    public List<ResponseDto.TableHeader> insertTableHeader(DbHeader.InsertHeaderDto insertHeaderDto) {
        DbHeader.HeaderDto headerDto = DbHeader.HeaderDto.builder()
                .tableId(insertHeaderDto.tableId())
                .pos(insertHeaderDto.pos())
                .text(insertHeaderDto.text())
                .xMin(0)
                .xMax(0)
                .yMin(0)
                .yMax(0)
                .score(0)
                .build();
        return tableService.insertHeader(headerDto).parallelStream().map(TableHeaderEntity::toDto).toList();
    }

    @Path("/header")
    @DELETE
    public List<ResponseDto.TableHeader> deleteTableHeader(DbHeader.DeleteHeaderDto deleteHeaderDto) {
        return tableService.deleteHeader(deleteHeaderDto).parallelStream().map(TableHeaderEntity::toDto).toList();
    }

    @Path("/rows")
    @POST
    public List<ResponseDto.TableRow> insertTableRow(DbRow.InsertRowDto insertRowDto) {
        DbRow.RowDto rowDto = DbRow.RowDto.builder()
                .tableId(insertRowDto.tableId())
                .parentText(StringUtils.EMPTY)
                .cellsText(new ArrayList<>(List.of(insertRowDto.text())))
                .xMin(0)
                .xMax(0)
                .yMin(0)
                .yMax(0)
                .score(0)
                .comment(StringUtils.EMPTY)
                .pos(insertRowDto.pos())
                .build();

        return tableService.insertRow(rowDto)
                .stream()
                .map(row -> tableRowEntityMapper.toDto(row, Boolean.FALSE))
                .toList();
    }

    @Path("/row")
    @DELETE
    public List<ResponseDto.TableRow> deleteTableRow(DbRow.DeleteRowDto deleteRowDto) {
        return tableService.deleteRow(deleteRowDto)
                .stream()
                .map(row -> tableRowEntityMapper.toDto(row, Boolean.FALSE))
                .toList();
    }
}
