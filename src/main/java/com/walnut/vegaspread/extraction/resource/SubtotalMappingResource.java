package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.extraction.model.SubtotalMappingDto;
import com.walnut.vegaspread.extraction.service.SubtotalMappingService;
import com.walnut.vegaspread.extraction.service.mapper.SubtotalMappingMapper;
import io.quarkus.security.Authenticated;
import jakarta.transaction.RollbackException;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;

import java.util.List;
import java.util.UUID;

@Path("/subtotal-mapping")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Authenticated
public class SubtotalMappingResource {

    private final SubtotalMappingService subtotalMappingService;
    private final SubtotalMappingMapper entityMapper;

    public SubtotalMappingResource(SubtotalMappingService subtotalMappingService, SubtotalMappingMapper entityMapper) {
        this.subtotalMappingService = subtotalMappingService;
        this.entityMapper = entityMapper;
    }

    @POST
    @Path("/{docId}")
    public List<SubtotalMappingDto.Response> createOrUpdateSubtotalMappings(@PathParam("docId") UUID docId,
                                                                            List<SubtotalMappingDto.CreateOrUpdateMapping> subtotalMappingDtos) throws RollbackException, IllegalArgumentException {
        subtotalMappingService.deleteMappings(subtotalMappingDtos, docId);
        return entityMapper.toDtoList(subtotalMappingService.createOrUpdateMappings(subtotalMappingDtos, docId));
    }

    @GET
    public List<SubtotalMappingDto.Response> getSubtotalMappings(@QueryParam("tableId") Integer tableId) {
        return entityMapper.toDtoList(subtotalMappingService.getSubtotalMappings(tableId));
    }

    @GET
    @Path("/{docId}")
    public List<SubtotalMappingDto.Response> getSubtotalMappingsForDoc(@PathParam("docId") UUID docId) {
        return entityMapper.toDtoList(subtotalMappingService.getSubtotalMappingsForDoc(docId));
    }
}
