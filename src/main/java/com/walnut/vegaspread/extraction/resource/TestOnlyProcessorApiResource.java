package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.common.security.ApiKeyAuthenticate;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto;
import com.walnut.vegaspread.extraction.service.ExtractedRowCoaDataService;
import com.walnut.vegaspread.extraction.service.mapper.ExtractedTableRowCoaDataJoinEntityMapper;
import io.quarkus.arc.profile.IfBuildProfile;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.OpenAPIDefinition;
import org.eclipse.microprofile.openapi.annotations.info.Info;

import java.util.List;

@OpenAPIDefinition(info = @Info(title = "Processor API authenticated by X-API-Key", version = "1.0"))
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Path("/test/wise/processor")
@ApiKeyAuthenticate
@IfBuildProfile("test")
public class TestOnlyProcessorApiResource {

    private static final String ROW_COA_PREFIX = "/row-coa-data-mapping";

    private final ExtractedRowCoaDataService extractedRowCoaDataService;
    private final ExtractedTableRowCoaDataJoinEntityMapper extractedTableRowCoaDataJoinEntityMapper;

    public TestOnlyProcessorApiResource(
            ExtractedTableRowCoaDataJoinEntityMapper extractedTableRowCoaDataJoinEntityMapper,
            ExtractedRowCoaDataService extractedRowCoaDataService) {

        this.extractedTableRowCoaDataJoinEntityMapper = extractedTableRowCoaDataJoinEntityMapper;
        this.extractedRowCoaDataService = extractedRowCoaDataService;
    }

    @Path(ROW_COA_PREFIX)
    @POST
    public List<ExtractedTableRowCoaDataJoinDto.Response> createOrUpdateCoaDataMapping(
            List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> extractedTableRowCoaDataJoinDtos) {
        List<ExtractedTableRowCoaDataJoinEntity> extractedTableRowCoaDataJoinEntities =
                extractedRowCoaDataService.createOrUpdateCoaDataMapping(extractedTableRowCoaDataJoinDtos, true);
        return extractedTableRowCoaDataJoinEntityMapper.toDtoList(extractedTableRowCoaDataJoinEntities);
    }
}
