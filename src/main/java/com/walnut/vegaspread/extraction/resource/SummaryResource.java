package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.common.exceptions.ExcelSaveException;
import com.walnut.vegaspread.common.exceptions.JSONDeserializationException;
import com.walnut.vegaspread.common.exceptions.WorkbookWriteException;
import com.walnut.vegaspread.extraction.model.Summary;
import com.walnut.vegaspread.extraction.model.SummaryInputDocDto;
import com.walnut.vegaspread.extraction.model.SummaryPreviewCell;
import com.walnut.vegaspread.extraction.service.SummaryService;
import io.quarkus.security.Authenticated;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.jboss.resteasy.reactive.RestQuery;

import java.io.IOException;
import java.util.List;
import java.util.UUID;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/summary")
@Authenticated
public class SummaryResource {

    private final SummaryService summaryService;

    public SummaryResource(SummaryService summaryService) {
        this.summaryService = summaryService;
    }

    @POST
    public Summary.SummaryOutput buildSummary(List<SummaryInputDocDto> docsForSpread, @RestQuery String clientName,
                                              @RestQuery UUID currentDocId,
                                              @NotNull @RestQuery @DefaultValue("Summarized FS") String sheetName) throws ExcelSaveException {
        return summaryService.buildSummaryForDocSpread(docsForSpread, clientName, currentDocId, sheetName);
    }

    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    @Path("/excel/{docId}")
    @GET
    public byte[] downloadSummaryExcel(@PathParam("docId") UUID docId,
                                       @RestQuery String clientName) throws WorkbookWriteException {
        return summaryService.downloadSummaryExcel(docId, clientName);
    }

    @Path("/preview/{docId}")
    @GET
    public List<List<SummaryPreviewCell>> buildSummaryPreview(@PathParam("docId") UUID docId,
                                                              @RestQuery String clientName,
                                                              @NotNull @RestQuery String sheetName) throws ExcelSaveException {
        return summaryService.generateSummaryPreview(docId, clientName, sheetName);
    }

    @Path("/json/{docId}")
    @POST
    public String buildSummaryJson(@PathParam("docId") UUID docId,
                                   @RestQuery String clientName,
                                   @NotEmpty List<String> sheetNames) throws IOException {
        return summaryService.downloadSummaryJson(docId, clientName, sheetNames);
    }

    @Path("/sheetNames")
    @GET
    public List<String> getSheetNames(@RestQuery String clientName) {
        return summaryService.getSheetNames(clientName);
    }

    @Path("/{clientName}/{docId}")
    @GET
    public Response generateCustomSummaryOutput(@PathParam("clientName") String clientName,
                                                @PathParam("docId") UUID docId) throws ExcelSaveException,
            JSONDeserializationException {
        return summaryService.generateCustomSummaryOutput(clientName, docId);
    }
}
