package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.extraction.model.SubtotalDto;
import com.walnut.vegaspread.extraction.service.SubtotalService;
import com.walnut.vegaspread.extraction.service.mapper.SubtotalMapper;
import io.quarkus.security.Authenticated;
import jakarta.annotation.security.RolesAllowed;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Path("/subtotal")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Authenticated
public class SubtotalResource {

    private final SubtotalService subtotalService;
    private final SubtotalMapper entityMapper;

    public SubtotalResource(SubtotalService subtotalService, SubtotalMapper entityMapper) {
        this.subtotalService = subtotalService;
        this.entityMapper = entityMapper;
    }

    @POST
    @RolesAllowed(Roles.ADMIN)
    public List<SubtotalDto.Response> createOrUpdateOrDeleteSubtotals(
            List<SubtotalDto.CreateOrUpdate> subtotalDtos,
            @QueryParam("coaClient") String coaClient, @QueryParam("client") String client) {
        return entityMapper.toDtoList(
                subtotalService.createOrUpdateOrDeleteSubtotals(subtotalDtos, coaClient, client));
    }

    @GET
    public List<SubtotalDto.Response> getSubtotals(@QueryParam("coaClient") String coaClient,
                                                   @QueryParam("client") String client) {
        return entityMapper.toDtoList(subtotalService.getSubtotals(coaClient, client));
    }
}
