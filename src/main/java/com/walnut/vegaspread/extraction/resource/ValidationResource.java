package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.extraction.model.Validation;
import com.walnut.vegaspread.extraction.service.ValidationService;
import io.quarkus.security.Authenticated;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.List;
import java.util.UUID;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/validate")
@Authenticated
public class ValidationResource {

    private final ValidationService validationService;

    public ValidationResource(ValidationService validationService) {

        this.validationService = validationService;
    }

    @GET
    @Path("/{docId}/fs-tables")
    public List<Validation.ResponseDto> validateBSOrIS(@PathParam("docId") UUID docId) {
        return validationService.validateBSOrIS(docId);
    }

    @GET
    @Path("/{docId}/nta-tables")
    public List<Validation.ResponseDto> validateNtaTables(@PathParam("docId") UUID docId) {
        return validationService.validateNtaTables(docId);
    }

    @GET
    @Path("/{docId}/coa-mapped-rows")
    public List<Validation.ResponseDto> validateCoaMappedRows(@PathParam("docId") UUID docId) {
        return validationService.validateCoaMappedRows(docId);
    }
}
