package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.common.model.extraction.TableTagDto;
import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.extraction.entity.TableTagEntity;
import com.walnut.vegaspread.extraction.service.TableTagService;
import io.quarkus.security.Authenticated;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.jwt.JsonWebToken;

import java.util.List;

import static com.walnut.vegaspread.common.utils.Jwt.getUsername;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/table/tag")
@Authenticated
public class TableTagResource {

    private final TableTagService tableTagService;
    private final JsonWebToken jwt;

    public TableTagResource(TableTagService tableTagService, JsonWebToken jwt) {
        this.tableTagService = tableTagService;
        this.jwt = jwt;
    }

    @POST
    @RolesAllowed(Roles.ADMIN)
    public List<TableTagDto.Response> createTableTag(@NotEmpty @NotNull List<@NotBlank String> tags) {
        return TableTagEntity.toDtoList(tableTagService.createTableTag(tags, getUsername(jwt)));
    }

    @PATCH
    @RolesAllowed(Roles.ADMIN)
    public List<TableTagDto.Response> updateTableTag(@Valid @NotEmpty @NotNull List<TableTagDto.Update> updateDtos) {
        return TableTagEntity.toDtoList(tableTagService.updateTableTag(updateDtos, getUsername(jwt)));
    }

    @DELETE
    @RolesAllowed(Roles.ADMIN)
    public long deleteTableTag(@Valid @NotEmpty List<@NotNull Integer> tagIds) {
        return tableTagService.deleteTableTag(tagIds);
    }

    @GET
    @Path("/list")
    public List<TableTagDto.Response> getTableTags() {
        return TableTagEntity.toDtoList(tableTagService.getTableTags());
    }

    @POST
    @Path("/list/ids")
    public List<TableTagDto.Response> getTableTagsByIds(@NotNull List<@NotNull Integer> tagIds) {
        return TableTagEntity.toDtoList(tableTagService.getTableTagByIds(tagIds));
    }
}
