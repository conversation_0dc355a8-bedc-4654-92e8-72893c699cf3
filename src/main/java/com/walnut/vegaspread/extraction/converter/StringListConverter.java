package com.walnut.vegaspread.extraction.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Converter
public class StringListConverter implements AttributeConverter<List<String>, String> {
    private static final String DELIMITER = "<CELL_SEPARATOR>";

    @Override
    public String convertToDatabaseColumn(List<String> strings) {
        if (strings != null && !strings.isEmpty()) {
            return String.join(DELIMITER, strings);
        }
        return "";
    }

    @Override
    public List<String> convertToEntityAttribute(String s) {
        if (s != null) {
            return Arrays.stream(s.split(DELIMITER, -1)).toList();
        }
        return Collections.emptyList();
    }
}
