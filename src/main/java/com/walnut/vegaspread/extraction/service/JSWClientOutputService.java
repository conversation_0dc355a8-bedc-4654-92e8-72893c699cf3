package com.walnut.vegaspread.extraction.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.walnut.vegaspread.common.exceptions.ExcelSaveException;
import com.walnut.vegaspread.common.exceptions.JSONDeserializationException;
import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.common.model.extraction.DocData;
import com.walnut.vegaspread.common.utils.Client;
import com.walnut.vegaspread.extraction.entity.SubtotalEntity;
import com.walnut.vegaspread.extraction.model.JSWSummaryOutput;
import com.walnut.vegaspread.extraction.model.SummaryPreviewCell;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.core.Response;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.Logger;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.walnut.vegaspread.common.utils.Constants.DEFAULT_CLIENT_NAME;
import static com.walnut.vegaspread.extraction.service.SummaryService.CUSTOM_SUMMARY_SUBTOTAL_NAME_SEPARATOR;
import static com.walnut.vegaspread.extraction.service.SummaryService.DATE_TIME_FORMATTER;
import static com.walnut.vegaspread.extraction.service.SummaryService.excelYearRowNo;
import static com.walnut.vegaspread.extraction.service.SummaryService.getValueTextFromPreviewText;

@ApplicationScoped
public class JSWClientOutputService {
    private static final String JSW_CLIENT_SUMMARY_TEMPLATE_PATH = "clientSummaryTemplate/jsw_output.json";
    private static final Logger logger = Logger.getLogger(JSWClientOutputService.class);
    private final SummaryService summaryService;
    private final SubtotalService subtotalService;

    public JSWClientOutputService(SummaryService summaryService, SubtotalService subtotalService) {
        this.summaryService = summaryService;
        this.subtotalService = subtotalService;
    }

    private static JSWSummaryOutput.FinancialDto populateFinancialDto(
            Map<String, Map<String, Map<String, List<SummaryPreviewCell>>>> inputData,
            JSWSummaryOutput.FinancialDto financialDto, int yearForFinancial, DocData currentDocData) {

        JSWSummaryOutput.StatementOfAssetsAndLiabilities updatedAssetsAndLiabilities =
                financialDto.statementOfAssetsAndLiabilities();
        JSWSummaryOutput.StatementOfIncomeAndExpenditure updatedIncomeAndExpenditure =
                financialDto.statementOfIncomeAndExpenditure();

        for (var entry : inputData.entrySet()) {
            switch (entry.getKey()) {
                case "statement_of_assets_and_liabilities" ->
                        updatedAssetsAndLiabilities = populateStatementOfAssetsAndLiabilities(entry.getValue(),
                                updatedAssetsAndLiabilities, yearForFinancial, currentDocData);
                case "statement_of_income_and_expenditure" ->
                        updatedIncomeAndExpenditure = populateStatementOfIncomeAndExpenditure(entry.getValue(),
                                updatedIncomeAndExpenditure, yearForFinancial, currentDocData);
                default -> {
                    //Do nothing.
                }
            }
        }

        return new JSWSummaryOutput.FinancialDto(
                financialDto.year(),
                financialDto.statedOn(),
                updatedAssetsAndLiabilities,
                updatedIncomeAndExpenditure,
                financialDto.certifiers()
        );
    }

    private static JSWSummaryOutput.StatementOfAssetsAndLiabilities populateStatementOfAssetsAndLiabilities(
            Map<String, Map<String, List<SummaryPreviewCell>>> data,
            JSWSummaryOutput.StatementOfAssetsAndLiabilities statement, int yearForFinancial, DocData currentDocData) {

        JSWSummaryOutput.Assets updatedAssets = statement.assets();
        JSWSummaryOutput.Liabilities updatedLiabilities = statement.liabilities();
        JSWSummaryOutput.Metadata updatedMetadata = statement.metadata();
        double givenAssetsTotal = 0.0;
        double givenLiabilitiesTotal = 0.0;

        for (var entry : data.entrySet()) {
            switch (entry.getKey()) {
                case "assets":
                    double grossFixedAssets = getDoubleValue(entry.getValue(), "gross_fixed_assets", yearForFinancial,
                            currentDocData);
                    double depreciationAndAmortization = getDoubleValue(entry.getValue(),
                            "depreciation_and_amortization", yearForFinancial, currentDocData);
                    double investments = getDoubleValue(entry.getValue(), "investments", yearForFinancial,
                            currentDocData);
                    double loansAndAdvances = getDoubleValue(entry.getValue(), "loans_and_advances", yearForFinancial,
                            currentDocData);
                    double inventories = getDoubleValue(entry.getValue(), "inventories", yearForFinancial,
                            currentDocData);
                    double tradeReceivables = getDoubleValue(entry.getValue(), "trade_receivables", yearForFinancial,
                            currentDocData);
                    double cashAndCashEquivalents = getDoubleValue(entry.getValue(), "cash_and_cash_equivalents",
                            yearForFinancial, currentDocData);
                    double otherAssets = getDoubleValue(entry.getValue(), "other_assets", yearForFinancial,
                            currentDocData);
                    double netFixedAssets = getDoubleValue(entry.getValue(), "net_fixed_assets", yearForFinancial,
                            currentDocData);

                    givenAssetsTotal += grossFixedAssets + depreciationAndAmortization + investments + loansAndAdvances +
                            inventories + tradeReceivables + cashAndCashEquivalents + otherAssets + netFixedAssets;

                    updatedAssets = new JSWSummaryOutput.Assets(
                            grossFixedAssets,
                            depreciationAndAmortization,
                            investments,
                            loansAndAdvances,
                            inventories,
                            tradeReceivables,
                            cashAndCashEquivalents,
                            otherAssets,
                            netFixedAssets
                    );
                    break;

                case "liabilities":
                    double contributionReceived = getDoubleValue(entry.getValue(), "contribution_received",
                            yearForFinancial, currentDocData);
                    double reservesAndSurplus = getDoubleValue(entry.getValue(), "reserves_and_surplus",
                            yearForFinancial, currentDocData);
                    double securedLoan = getDoubleValue(entry.getValue(), "secured_loan", yearForFinancial,
                            currentDocData);
                    double unsecuredLoan = getDoubleValue(entry.getValue(), "unsecured_loan", yearForFinancial,
                            currentDocData);
                    double shortTermBorrowing = getDoubleValue(entry.getValue(), "short_term_borrowing",
                            yearForFinancial, currentDocData);
                    double tradePayables = getDoubleValue(entry.getValue(), "trade_payables", yearForFinancial,
                            currentDocData);
                    double otherLiabilities = getDoubleValue(entry.getValue(), "other_liabilities", yearForFinancial,
                            currentDocData);
                    double provisionsForTaxation = getDoubleValue(entry.getValue(), "provisions_for_taxation",
                            yearForFinancial, currentDocData);
                    double provisionsForContingencies = getDoubleValue(entry.getValue(), "provisions_for_contingencies",
                            yearForFinancial, currentDocData);
                    double provisionsForInsurance = getDoubleValue(entry.getValue(), "provisions_for_insurance",
                            yearForFinancial, currentDocData);
                    double otherProvisions = getDoubleValue(entry.getValue(), "other_provisions", yearForFinancial,
                            currentDocData);

                    givenLiabilitiesTotal += contributionReceived + reservesAndSurplus + securedLoan + unsecuredLoan +
                            shortTermBorrowing + tradePayables + otherLiabilities + provisionsForTaxation +
                            provisionsForContingencies + provisionsForInsurance + otherProvisions;

                    updatedLiabilities = new JSWSummaryOutput.Liabilities(
                            contributionReceived,
                            reservesAndSurplus,
                            securedLoan,
                            unsecuredLoan,
                            shortTermBorrowing,
                            tradePayables,
                            otherLiabilities,
                            provisionsForTaxation,
                            provisionsForContingencies,
                            provisionsForInsurance,
                            otherProvisions
                    );
                    break;
                default:
                    break;
            }
        }

        JSWSummaryOutput.SubTotals updatedSubTotals = new JSWSummaryOutput.SubTotals(givenAssetsTotal,
                givenLiabilitiesTotal);

        // Return a new updated StatementOfAssetsAndLiabilities instance
        return new JSWSummaryOutput.StatementOfAssetsAndLiabilities(updatedAssets, updatedSubTotals, updatedLiabilities,
                updatedMetadata);
    }

    private static JSWSummaryOutput.StatementOfIncomeAndExpenditure populateStatementOfIncomeAndExpenditure(
            Map<String, Map<String, List<SummaryPreviewCell>>> data,
            JSWSummaryOutput.StatementOfIncomeAndExpenditure statement, int yearForFinancial, DocData currentDocData) {

        JSWSummaryOutput.LineItems updatedLineItems = statement.lineItems();
        JSWSummaryOutput.RevenueBreakup updatedRevenueBreakup = statement.revenueBreakup();
        JSWSummaryOutput.DepreciationBreakup updatedDepreciationBreakup = statement.depreciationBreakup();
        JSWSummaryOutput.Metadata updatedMetadata = statement.metadata();

        for (var entry : data.entrySet()) {
            switch (entry.getKey()) {
                case "revenue_breakup" -> updatedRevenueBreakup = new JSWSummaryOutput.RevenueBreakup(
                        getDoubleValue(entry.getValue(), "sale_of_goods_manufactured_domestic", yearForFinancial,
                                currentDocData),
                        getDoubleValue(entry.getValue(), "sale_of_goods_traded_domestic", yearForFinancial,
                                currentDocData),
                        getDoubleValue(entry.getValue(), "sale_or_supply_of_services_domestic", yearForFinancial,
                                currentDocData),
                        getDoubleValue(entry.getValue(), "sale_of_goods_manufactured_export", yearForFinancial,
                                currentDocData),
                        getDoubleValue(entry.getValue(), "sale_of_goods_traded_export", yearForFinancial,
                                currentDocData),
                        getDoubleValue(entry.getValue(), "sale_or_supply_of_services_export", yearForFinancial,
                                currentDocData)
                );
                case "lineItems" -> updatedLineItems = new JSWSummaryOutput.LineItems(
                        getDoubleValue(entry.getValue(), "net_revenue", yearForFinancial, currentDocData),
                        getDoubleValue(entry.getValue(), "operating_cost", yearForFinancial, currentDocData),
                        getDoubleValue(entry.getValue(), "total_cost_of_materials_consumed", yearForFinancial,
                                currentDocData),
                        getDoubleValue(entry.getValue(), "total_purchases_of_stock_in_trade", yearForFinancial,
                                currentDocData),
                        getDoubleValue(entry.getValue(), "total_changes_in_inventories_or_finished_goods",
                                yearForFinancial, currentDocData),
                        getDoubleValue(entry.getValue(), "total_employee_benefit_expense", yearForFinancial,
                                currentDocData),
                        getDoubleValue(entry.getValue(), "total_other_expenses", yearForFinancial, currentDocData),
                        getDoubleValue(entry.getValue(), "operating_profit", yearForFinancial, currentDocData),
                        getDoubleValue(entry.getValue(), "other_income", yearForFinancial, currentDocData),
                        getDoubleValue(entry.getValue(), "depreciation", yearForFinancial, currentDocData),
                        getDoubleValue(entry.getValue(), "profit_before_interest_and_tax", yearForFinancial,
                                currentDocData),
                        getDoubleValue(entry.getValue(), "interest", yearForFinancial, currentDocData),
                        getDoubleValue(entry.getValue(), "profit_before_tax_and_exceptional_items_before_tax",
                                yearForFinancial, currentDocData),
                        getDoubleValue(entry.getValue(), "exceptional_items_before_tax", yearForFinancial,
                                currentDocData),
                        getDoubleValue(entry.getValue(), "profit_before_tax", yearForFinancial, currentDocData),
                        getDoubleValue(entry.getValue(), "income_tax", yearForFinancial, currentDocData),
                        getDoubleValue(entry.getValue(), "profit_for_period_from_continuing_operations",
                                yearForFinancial, currentDocData),
                        getDoubleValue(entry.getValue(), "profit_from_discontinuing_operation_after_tax",
                                yearForFinancial, currentDocData),
                        getDoubleValue(entry.getValue(),
                                "minority_interest_and_profit_from_associates_and_joint_ventures", yearForFinancial,
                                currentDocData),
                        getDoubleValue(entry.getValue(), "profit_after_tax", yearForFinancial, currentDocData)
                );
                case "depreciation_breakup" -> updatedDepreciationBreakup = new JSWSummaryOutput.DepreciationBreakup(
                        getDoubleValue(entry.getValue(), "depreciation_and_amortization", yearForFinancial,
                                currentDocData)
                );
                default -> {
                    //Do nothing
                }
            }
        }
        return new JSWSummaryOutput.StatementOfIncomeAndExpenditure(updatedLineItems, updatedRevenueBreakup,
                updatedDepreciationBreakup, updatedMetadata);
    }

    private static double getDoubleValue(Map<String, List<SummaryPreviewCell>> map, String key, int dataIdx,
                                         DocData currentDocData) {
        return map.containsKey(key) ? Double.parseDouble(
                getValueTextFromPreviewText(currentDocData, map.get(key).get(dataIdx).getText())) : 0.0;
    }

    public static JsonNode generateClientJSON(JSWSummaryOutput.FinancialsDto financialsDto,
                                              int financialYear) throws JSONDeserializationException {
        JsonNode root;
        try {
            //Read the json template for the client output.
            ObjectMapper objectMapper = JsonMapper.builder()
                    .addModule(new JavaTimeModule())
                    .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                    .build();
            // Load the template using ClassLoader.getResourceAsStream()
            try (InputStream inputStream = JSWClientOutputService.class.getClassLoader()
                    .getResourceAsStream(JSW_CLIENT_SUMMARY_TEMPLATE_PATH)) {
                if (inputStream == null) {
                    ResponseException.throwResponseException(Response.Status.NOT_FOUND,
                            "Resource not found: " + JSW_CLIENT_SUMMARY_TEMPLATE_PATH);
                }

                // Read the template content as a String
                String templateContent = IOUtils.toString(inputStream, StandardCharsets.UTF_8);

                // Parse the JSON template
                root = objectMapper.readTree(templateContent);
            }

            //Get the data for the json
            ObjectNode dataNode = (ObjectNode) root.get("data");

            //Convert the financials to json string.
            String jsonStringForFinancials = objectMapper.writeValueAsString(financialsDto);
            //Convert the json string to json tree.
            JsonNode newFinancialsNode = objectMapper.readTree(jsonStringForFinancials);
            //Update the json node containing financials.
            dataNode.set("financials", newFinancialsNode);

            ObjectNode holdingEntitiesNode = (ObjectNode) dataNode.get("holding_entities");
            holdingEntitiesNode.put("financial_year", String.valueOf(financialYear));
        } catch (IOException e) {
            logger.error(e);
            ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR,
                    "Failed to deserialize json at " + JSW_CLIENT_SUMMARY_TEMPLATE_PATH);
            return null;
        }
        return root;
    }

    public JSWSummaryOutput.FinancialsDto getJSWClientOutput(DocData docData) throws ExcelSaveException {
        String clientName = Client.JSW.getClientName();

        //Get the summary preview for jsw.
        List<List<SummaryPreviewCell>> summaryPreview = summaryService.generateSummaryPreview(docData.docId(),
                clientName,
                "Summarized FS");

        //Get subtotals for walnut coa client and jsw client.
        ArrayList<SubtotalEntity> subtotalsForClient = new ArrayList<>(subtotalService.getSubtotals(
                Client.WALNUT.getClientName(), clientName));

        //Create a map of preview row id and preview row data.
        Map<Integer, List<SummaryPreviewCell>> previewRowIdRowMap = new HashMap<>();
        summaryPreview.forEach(
                summaryRow -> previewRowIdRowMap.put(Integer.parseInt(summaryRow.get(0).getText()), summaryRow));

        //Create a tree structure by splitting the subtotal name by separator.
        Map<String, Map<String, Map<String, List<SummaryPreviewCell>>>> subtotalTree = makeSubtotalCategoryTree(
                subtotalsForClient, previewRowIdRowMap);

        //Get column indexes for current and previous year in excel.
        Integer yearRow = excelYearRowNo.getOrDefault(clientName, excelYearRowNo.get(DEFAULT_CLIENT_NAME));
        List<SummaryPreviewCell> previewYearRow = summaryPreview.get(yearRow - 1);
        List<Integer> yearIdxsInPreviewRow = summaryService.getExcelPeriodColumnIndexes(previewYearRow, docData);

        List<JSWSummaryOutput.FinancialDto> financialDtos = new ArrayList<>();

        //Generate financials for current and previous year.
        for (Integer yearIdxInPreviewRow : yearIdxsInPreviewRow) {
            //Create an empty financial with year and stated on.
            LocalDate currentYear = LocalDate.parse(previewYearRow.get(yearIdxInPreviewRow).getText(),
                    DATE_TIME_FORMATTER);
            JSWSummaryOutput.FinancialDto financialDto = createEmptyFinancialDto(currentYear, docData.period());
            //Populate data for the financial.
            financialDto = populateFinancialDto(subtotalTree, financialDto, yearIdxInPreviewRow, docData);
            financialDtos.add(financialDto);
        }
        return new JSWSummaryOutput.FinancialsDto(financialDtos);
    }

    private Map<String, Map<String, Map<String, List<SummaryPreviewCell>>>> makeSubtotalCategoryTree(
            ArrayList<SubtotalEntity> subtotalsForClient,
            Map<Integer, List<SummaryPreviewCell>> previewRow) {
        return subtotalsForClient.stream()
                .filter(subtotalForClient -> subtotalForClient.getSubtotalName()
                        .split(CUSTOM_SUMMARY_SUBTOTAL_NAME_SEPARATOR).length >= 3)
                .collect(Collectors.groupingBy(
                        subtotalForClient -> subtotalForClient.getSubtotalName()
                                .split(CUSTOM_SUMMARY_SUBTOTAL_NAME_SEPARATOR)[0],
                        // GrandParent level financial such as statement_of_assets_and_liabilities
                        Collectors.groupingBy(
                                subtotalForClient -> subtotalForClient.getSubtotalName()
                                        .split(CUSTOM_SUMMARY_SUBTOTAL_NAME_SEPARATOR)[1],
                                // Parent level financials such as assets
                                Collectors.toMap(
                                        //Map of subtotal and its corresponding preview row.
                                        subtotalForClient -> subtotalForClient.getSubtotalName()
                                                .split(CUSTOM_SUMMARY_SUBTOTAL_NAME_SEPARATOR)[2],
                                        subtotalForClient -> previewRow.get(
                                                subtotalForClient.getExcelRowNumber().intValue())
                                )
                        )
                ));
    }

    private JSWSummaryOutput.FinancialDto createEmptyFinancialDto(LocalDate currentYear, LocalDate statedOn) {

        JSWSummaryOutput.StatementOfAssetsAndLiabilities statementOfAssetsAndLiabilities =
                new JSWSummaryOutput.StatementOfAssetsAndLiabilities(null, null, null, new JSWSummaryOutput.Metadata(
                        StringUtils.EMPTY));
        JSWSummaryOutput.StatementOfIncomeAndExpenditure statementOfIncomeAndExpenditure =
                new JSWSummaryOutput.StatementOfIncomeAndExpenditure(null, null, null, new JSWSummaryOutput.Metadata(
                        StringUtils.EMPTY));

        return new JSWSummaryOutput.FinancialDto(
                currentYear,
                statedOn,
                statementOfAssetsAndLiabilities,
                statementOfIncomeAndExpenditure,
                Map.of()
        );
    }
}
