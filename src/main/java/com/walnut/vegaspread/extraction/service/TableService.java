package com.walnut.vegaspread.extraction.service;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedTableHeaderAuditDto;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedTableRowAuditDto;
import com.walnut.vegaspread.extraction.entity.Bbox;
import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.entity.TableHeaderEntity;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.entity.TableTagEntity;
import com.walnut.vegaspread.extraction.model.CoaDataDto;
import com.walnut.vegaspread.extraction.model.DbHeader;
import com.walnut.vegaspread.extraction.model.DbRow;
import com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto;
import com.walnut.vegaspread.extraction.model.MappedRowDto;
import com.walnut.vegaspread.extraction.primarykey.TableHeaderPkId;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import com.walnut.vegaspread.extraction.repository.CoaMappingRepository;
import com.walnut.vegaspread.extraction.repository.TableHeaderRepository;
import com.walnut.vegaspread.extraction.repository.TableRowRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.LockModeType;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response;
import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@ApplicationScoped
public class TableService {
    private static final Logger logger = Logger.getLogger(TableService.class);
    private final LayoutService layoutService;
    private final ExchangeService exchangeService;
    private final TableHeaderRepository tableHeaderRepository;
    private final TableRowRepository tableRowRepository;
    private final CoaMappingRepository coaMappingRepository;
    private final ExtractedRowCoaDataService extractedRowCoaDataService;
    private final EntityManager entityManager;
    private final CoaMappingService coaMappingService;

    public TableService(LayoutService layoutService, ExchangeService exchangeService,
                        TableHeaderRepository tableHeaderRepository, TableRowRepository tableRowRepository,
                        CoaMappingRepository coaMappingRepository,
                        ExtractedRowCoaDataService extractedRowCoaDataService,
                        EntityManager entityManager,
                        CoaMappingService coaMappingService
    ) {
        this.layoutService = layoutService;
        this.exchangeService = exchangeService;
        this.tableHeaderRepository = tableHeaderRepository;
        this.tableRowRepository = tableRowRepository;
        this.coaMappingRepository = coaMappingRepository;
        this.extractedRowCoaDataService = extractedRowCoaDataService;
        this.entityManager = entityManager;
        this.coaMappingService = coaMappingService;
    }

    private Object getCurrentHeaderValue(HeaderUpdateField field, TableHeaderEntity currentHeader) {
        return switch (field) {
            case TEXT -> currentHeader.getText();
            case BBOX -> currentHeader.getBbox();
            case SCORE -> currentHeader.getScore();
        };
    }

    private Object getNewHeaderValue(HeaderUpdateField field, DbHeader.HeaderDto headerDto) {
        return switch (field) {
            case TEXT -> headerDto.getText();
            case BBOX -> {
                if (headerDto.getXMin() != null && headerDto.getYMin() != null && headerDto.getXMax() != null && headerDto.getYMax() != null) {
                    yield new Bbox(headerDto.getXMin(), headerDto.getXMax(), headerDto.getYMin(), headerDto.getYMax());
                } else {
                    yield null;
                }
            }
            case SCORE -> headerDto.getScore();
        };
    }

    private void updateHeaderValue(HeaderUpdateField field, TableHeaderEntity header, DbHeader.HeaderDto headerDto) {
        switch (field) {
            case TEXT -> header.setText(headerDto.getText());
            case BBOX -> header.setBbox(
                    new Bbox(headerDto.getXMin(), headerDto.getXMax(), headerDto.getYMin(), headerDto.getYMax()));
            case SCORE -> header.setScore(headerDto.getScore().byteValue());
        }
    }

    private void addHeaderAuditEntry(HeaderUpdateField field, TableHeaderPkId tableHeaderPkId, Object currentValue,
                                     Object newValue, List<ExtractedTableHeaderAuditDto.Update> auditChanges) {
        switch (field) {
            case TEXT -> auditChanges.add(new ExtractedTableHeaderAuditDto.Update(tableHeaderPkId.getTableId(),
                    tableHeaderPkId.getHeaderId().intValue(), TableHeaderEntity.TEXT_COL_NAME, currentValue.toString(),
                    newValue.toString()));
            case SCORE -> auditChanges.add(new ExtractedTableHeaderAuditDto.Update(tableHeaderPkId.getTableId(),
                    tableHeaderPkId.getHeaderId().intValue(), TableHeaderEntity.SCORE_COL_NAME, currentValue.toString(),
                    newValue.toString()));
            default -> {
                break;
            }
        }
    }

    private void updateHeaderField(HeaderUpdateField field, TableHeaderEntity header, TableHeaderEntity currentHeader,
                                   DbHeader.HeaderDto headerDto,
                                   List<ExtractedTableHeaderAuditDto.Update> auditChanges) {
        Object newValue = getNewHeaderValue(field, headerDto);
        Object currentValue = getCurrentHeaderValue(field, currentHeader);

        if (newValue != null && !newValue.equals(currentValue)) {
            updateHeaderValue(field, header, headerDto);
            addHeaderAuditEntry(field, header.getTableHeaderPkId(), currentValue, newValue, auditChanges);
        }
    }

    private List<TableHeaderEntity> createHeaders(List<DbHeader.HeaderDto> headerDtos) {
        List<Integer> tableIds = headerDtos.stream().map(DbHeader.HeaderDto::getTableId).distinct().toList();
        Map<Integer, LayoutBlockEntity> blockEntityMap = layoutService.getBlocks(tableIds).stream()
                .collect(Collectors.toMap(LayoutBlockEntity::getBlockId, lb -> lb));
        List<TableHeaderEntity> headers = new ArrayList<>();
        for (DbHeader.HeaderDto dto : headerDtos) {
            Integer tableId = dto.getTableId();
            Integer headerId = dto.getHeaderId();
            TableHeaderEntity header = TableHeaderEntity.builder()
                    .tableHeaderPkId(new TableHeaderPkId(tableId, headerId))
                    .text(dto.getText())
                    .bbox(new Bbox(dto.getXMin(), dto.getXMax(), dto.getYMin(), dto.getYMax()))
                    .score(dto.getScore().byteValue())
                    .pos(headerId)
                    .layoutBlock(blockEntityMap.get(tableId))
                    .build();
            headers.add(header);
        }
        tableHeaderRepository.persist(headers);

        return headers;
    }

    @Transactional
    public List<TableHeaderEntity> createOrUpdateHeaders(List<DbHeader.HeaderDto> headerDtos, boolean isUpdate,
                                                         boolean deleteExisting) {
        if (!isUpdate) {
            if (deleteExisting) {
                deleteHeaders(headerDtos.stream().map(DbHeader.HeaderDto::getTableId).distinct().toList());
            }
            return createHeaders(headerDtos);
        }
        List<TableHeaderEntity> headers = new ArrayList<>();
        List<ExtractedTableHeaderAuditDto.Update> auditUpdates = new ArrayList<>();
        for (DbHeader.HeaderDto dto : headerDtos) {
            TableHeaderEntity header;
            header = tableHeaderRepository.findById(new TableHeaderPkId(dto.getTableId(), dto.getHeaderId()));
            TableHeaderEntity currentHeader = new TableHeaderEntity(header);

            for (HeaderUpdateField field : HeaderUpdateField.values()) {
                updateHeaderField(field, header, currentHeader, dto, auditUpdates);
            }
            headers.add(header);
        }
        tableHeaderRepository.persist(headers);
        exchangeService.updateTimeInDb(headers.get(0).getLayoutBlock().getDocId(), false);
        exchangeService.auditUpdateHeaders(auditUpdates);
        return headers;
    }

    @Transactional
    public void updateRowsForNewHeader(DbHeader.HeaderDto headerDto) {
        Integer tableId = headerDto.getTableId();
        Integer newHeaderPosition = headerDto.getPos();

        List<TableRowEntity> tableRows = tableRowRepository.findByTableId(tableId);

        for (TableRowEntity tableRow : tableRows) {

            List<String> currentCellText = new ArrayList<>(tableRow.getCellsText());
            if ((headerDto.getPos() + 1) <= currentCellText.size()) {
                currentCellText.add(newHeaderPosition, StringUtils.EMPTY);
            } else {
                currentCellText.add(StringUtils.EMPTY);
            }
            tableRow.setCellsText(currentCellText);
        }

        tableRowRepository.persist(tableRows);
    }

    @Transactional
    public void updateRowsForDeletedHeader(TableHeaderEntity headerForDeletion) {
        Integer tableId = headerForDeletion.getTableHeaderPkId().getTableId();
        Integer pos = headerForDeletion.getPos();

        List<TableRowEntity> tableRows = tableRowRepository.findByTableId(tableId);

        for (TableRowEntity tableRow : tableRows) {
            List<String> currentCellText = new ArrayList<>(tableRow.getCellsText());
            currentCellText.remove(pos.intValue());
            tableRow.setCellsText(currentCellText);

            List<Integer> currentFyHeaders = new ArrayList<>(tableRow.getHeaderIds());
            currentFyHeaders.remove(Integer.valueOf(headerForDeletion.getTableHeaderPkId().getHeaderId()));
            tableRow.setHeaderIds(currentFyHeaders);
        }
        tableRowRepository.persist(tableRows);
    }

    private void updateRowField(RowUpdateField field, TableRowEntity row, TableRowEntity currentRow,
                                DbRow.RowDto rowDto, List<ExtractedTableRowAuditDto.Update> auditChanges) {
        Object newValue = getNewRowValue(field, rowDto);
        Object currentValue = getCurrentRowValue(field, currentRow);

        if (newValue != null && !newValue.equals(currentValue)) {
            updateRowValue(field, row, rowDto);
            addRowAuditEntry(field, row.getTableRowPkId(), currentValue, newValue, auditChanges);
        }
    }

    private void addRowAuditEntry(RowUpdateField field, TableRowPkId tableRowPkId, Object currentValue, Object newValue,
                                  List<ExtractedTableRowAuditDto.Update> auditChanges) {
        switch (field) {
            case CELLS_TEXT:
                if (!(currentValue instanceof List && newValue instanceof List)) {
                    ResponseException.throwResponseException(Response.Status.BAD_REQUEST,
                            "Cells text should be a list");
                }
                List<String> currentCellsText = ((List<?>) currentValue).stream().map(Object::toString).toList();
                List<String> newCellsText = ((List<?>) newValue).stream().map(Object::toString).toList();
                for (int i = 0; i < currentCellsText.size(); i++) {
                    String currentCellText = currentCellsText.get(i)
                            .substring(0, Math.min(255, currentCellsText.get(i).length()));
                    String newCellText = newCellsText.get(i).substring(0, Math.min(255, newCellsText.get(i).length()));

                    if (!currentCellText.equals(newCellText)) {
                        auditChanges.add(new ExtractedTableRowAuditDto.Update(tableRowPkId.getTableId(),
                                tableRowPkId.getRowId().intValue(),
                                TableRowEntity.CELLS_TEXT_COL_NAME, currentCellText, newCellText));
                    }
                }
                break;
            case NTA_TABLE_ID:
                auditChanges.add(
                        new ExtractedTableRowAuditDto.Update(tableRowPkId.getTableId(),
                                tableRowPkId.getRowId().intValue(),
                                TableRowEntity.NTA_TABLE_FOREIGN_KEY_COL_NAME, currentValue.toString(),
                                newValue.toString()));
                break;
            case HEADER_IDS:
                auditChanges.add(
                        new ExtractedTableRowAuditDto.Update(tableRowPkId.getTableId(),
                                tableRowPkId.getRowId().intValue(),
                                TableRowEntity.HEADER_IDS_COL_NAME, currentValue.toString(),
                                newValue.toString()));
                break;
            default:
                break;
        }
    }

    private Object getCurrentRowValue(RowUpdateField field, TableRowEntity currentRow) {
        return switch (field) {
            case CELLS_TEXT -> currentRow.getCellsText();
            case BBOX -> currentRow.getBbox();
            case SCORE -> currentRow.getScore();
            case NTA_TABLE_ID -> currentRow.getNtaTable() == null ? 0 : currentRow.getNtaTable().getBlockId();
            case PARENT_TEXT -> currentRow.getParentText();
            case HEADER_IDS -> currentRow.getHeaderIds();
            case COMMENT -> currentRow.getComment();
        };
    }

    private Object getNewRowValue(RowUpdateField field, DbRow.RowDto rowDto) {
        return switch (field) {
            case CELLS_TEXT -> rowDto.getCellsText();
            case BBOX -> {
                if (rowDto.getXMin() != null && rowDto.getXMax() != null && rowDto.getYMin() != null && rowDto.getYMax() != null) {
                    yield new Bbox(rowDto.getXMin(), rowDto.getXMax(), rowDto.getYMin(), rowDto.getYMax());
                } else {
                    yield null;
                }
            }
            case SCORE -> rowDto.getScore();
            case NTA_TABLE_ID -> rowDto.getNtaTableId();
            case PARENT_TEXT -> rowDto.getParentText();
            case HEADER_IDS -> rowDto.getHeaderIds();
            case COMMENT -> rowDto.getComment();
        };
    }

    private void updateRowValue(RowUpdateField field, TableRowEntity row, DbRow.RowDto rowDto) {
        switch (field) {
            case CELLS_TEXT:
                int headerSize = row.getLayoutBlock().getTableHeaders().size();
                if (headerSize != rowDto.getCellsText().size()) {
                    ResponseException.throwResponseException(Response.Status.BAD_REQUEST,
                            String.format("%d cells text found for %d headers at table %d and row %d",
                                    rowDto.getCellsText().size(), headerSize, row.getTableRowPkId().getTableId(),
                                    row.getTableRowPkId().getRowId()));
                }
                row.setCellsText(rowDto.getCellsText());
                break;
            case BBOX:
                row.setBbox(new Bbox(rowDto.getXMin(), rowDto.getXMax(), rowDto.getYMin(), rowDto.getYMax()));
                break;
            case SCORE:
                row.setScore(rowDto.getScore().byteValue());
                break;
            case NTA_TABLE_ID:
                Integer newNtaTableId = rowDto.getNtaTableId();
                row.setNtaTable(newNtaTableId == 0 ? null : layoutService.getBlock(newNtaTableId));
                break;
            case PARENT_TEXT:
                row.setParentText(rowDto.getParentText());
                break;
            case HEADER_IDS:
                row.setHeaderIds(rowDto.getHeaderIds());
                break;
            case COMMENT:
                row.setComment(rowDto.getComment());
                break;
            default:
                break;
        }
    }

    @Transactional
    public List<TableRowEntity> createOrUpdateRows(List<DbRow.RowDto> rowDtos, boolean isUpdate,
                                                   boolean isApiKeyAuthenticated, boolean deleteExisting) {
        List<TableRowEntity> rows = new ArrayList<>();
        List<ExtractedTableRowAuditDto.Update> auditUpdates = new ArrayList<>();

        if (!isUpdate) {
            if (isApiKeyAuthenticated && deleteExisting) {
                deleteRows(rowDtos.stream().map(DbRow.RowDto::getTableId).distinct().toList(), isApiKeyAuthenticated);
                entityManager.flush();
            }
            return createRows(rowDtos);
        }
        for (DbRow.RowDto dto : rowDtos) {
            TableRowEntity row;
            Integer tableId = dto.getTableId();
            Integer rowId = dto.getRowId();
            row = tableRowRepository.findById(new TableRowPkId(tableId, rowId), LockModeType.PESSIMISTIC_WRITE);

            TableRowEntity currentRow = new TableRowEntity(row);

            for (RowUpdateField field : RowUpdateField.values()) {
                updateRowField(field, row, currentRow, dto, auditUpdates);
            }

            rows.add(row);
        }

        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> extractedTableRowCoaDataEntities = rowDtos.stream()
                .map(rowDto -> new ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                                rowDto.getTableId(),
                                rowDto.getRowId(),
                                rowDto.getCoaId() == null ? null : new CoaDataDto.Create(
                                        rowDto.getCoaId(),
                                        rowDto.getCoaScore(),
                                        Optional.ofNullable(rowDto.getUseCoa()).orElse(Boolean.FALSE)),
                                rowDto.getExplainabilityId()
                        )
                ).toList();
        extractedRowCoaDataService.createOrUpdateCoaDataMapping(extractedTableRowCoaDataEntities,
                isApiKeyAuthenticated);
        if (!rows.isEmpty()) {
            exchangeService.updateTimeInDb(rows.get(0).getLayoutBlock().getDocId(), isApiKeyAuthenticated);
        }

        exchangeService.auditRows(auditUpdates, isApiKeyAuthenticated);

        tableRowRepository.persist(rows);

        return rows;
    }

    private List<TableRowEntity> createRows(List<DbRow.RowDto> rowDtos) {
        List<Integer> tableIds = rowDtos.stream().map(DbRow.RowDto::getTableId).distinct().toList();
        Map<Integer, LayoutBlockEntity> blockEntityMap = layoutService.getBlocks(tableIds).stream()
                .collect(Collectors.toMap(LayoutBlockEntity::getBlockId, lb -> lb));
        List<TableRowEntity> rows = new ArrayList<>();
        for (DbRow.RowDto dto : rowDtos) {

            Integer tableId = dto.getTableId();
            Integer rowId = dto.getRowId();
            TableRowEntity row = TableRowEntity.builder()
                    .tableRowPkId(new TableRowPkId(tableId, rowId))
                    .cellsText(dto.getCellsText())
                    .bbox(new Bbox(dto.getXMin(), dto.getXMax(), dto.getYMin(), dto.getYMax()))
                    .score(dto.getScore().byteValue())
                    .ntaTable(dto.getNtaTableId() == 0 ? null : layoutService.getBlock(dto.getNtaTableId()))
                    .parentText(dto.getParentText())
                    .headerIds(dto.getHeaderIds())
                    .comment(dto.getComment())
                    .pos(rowId)
                    .layoutBlock(blockEntityMap.get(tableId))
                    .build();
            rows.add(row);
        }
        tableRowRepository.persist(rows);

        return rows;
    }

    @Transactional
    public long deleteHeaders(List<Integer> tableIds) {
        return tableHeaderRepository.deleteForTableIds(tableIds);
    }

    @Transactional(Transactional.TxType.REQUIRES_NEW)
    public long deleteRows(List<Integer> tableIds, boolean isApiKeyAuthenticated) {
        extractedRowCoaDataService.deleteTableCoaDataMappings(tableIds, isApiKeyAuthenticated);
        long deleteCount = tableRowRepository.deleteForTableIds(tableIds);
        tableRowRepository.flush();
        return deleteCount;
    }

    private TableRowEntity findLinkedFsRow(Integer tableId) {
        // Find FS row linked to current ntaTable
        List<TableRowEntity> linkedFsRows = tableRowRepository.findAllByNtaBlockId(tableId);
        if (linkedFsRows.isEmpty()) {
            logger.debugf("Nta table %s not linked to any FS row", tableId);
            return null;
        } else if (linkedFsRows.size() != 1) {
            List<Map<String, Integer>> linkedRows =
                    linkedFsRows.stream()
                            .map(r -> Map.of("rowId", Integer.valueOf(r.getTableRowPkId().getRowId()), "tableId",
                                    r.getTableRowPkId().getTableId()))
                            .toList();
            logger.debugf("Nta table %s linked to multiple FS rows %s ", tableId, linkedRows);
            return linkedFsRows.get(0);
        }
        return linkedFsRows.get(0);
    }

    private CoaMappingEntity rowToCoaMapping(LayoutBlockEntity block, TableRowEntity tableRow) {
        TableTagEntity tableTag = block.getTag();
        TableRowEntity fsRow;
        if (List.of("Balance Sheet", "Income Statement", "Cash Flow").contains(tableTag.getTag())) {
            fsRow = tableRow;
        } else {
            fsRow = findLinkedFsRow(tableRow.getTableRowPkId().getTableId());
        }

        String rowText = tableRow.getCellsText().get(0);

        return CoaMappingEntity.builder()
                .tableId(tableRow.getTableRowPkId().getTableId())
                .rowId(tableRow.getTableRowPkId().getRowId())
                .docId(block.getDocId())
                .tableTypeId(tableTag.getId())
                .rowParent(tableRow.getParentText() == null ? "" : tableRow.getParentText())
                .text(rowText)
                .fsHeader(fsRow == null ? "" : Optional.ofNullable(fsRow.getParentText()).orElse(""))
                .fsText(fsRow == null ? "" : fsRow.getCellsText().get(0))
                .coaId(tableRow.getCoaData().getCoaId())
                .isApproved(tableRow.getCoaData().getUseCoa())
                .build();
    }

    @Transactional
    public List<MappedRowDto> getMappedCoaRows(List<UUID> docIds, Boolean onlyActive, Integer tableTypeId) {
        List<CoaMappingEntity> existingCoaMappings = coaMappingService.findByDocIdsAndTableTypeId(docIds, tableTypeId);
        logger.debugf("Existing coa mapped rows for docIds %s : are", docIds);
        existingCoaMappings.forEach(existingCoaMapping -> logger.debugf("%s", existingCoaMapping));
        if (tableTypeId == null) {
            List<UUID> existingDocIds = existingCoaMappings.stream()
                    .map(CoaMappingEntity::getDocId)
                    .distinct()
                    .toList();
            List<UUID> missingDocIds = docIds.stream().filter(docId -> !existingDocIds.contains(docId)).toList();
            logger.debugf("Missing docIds : %s", missingDocIds);

            List<CoaMappingEntity> newCoaMappings = new ArrayList<>();
            for (UUID docId : missingDocIds) {
                List<Integer> blockIds = layoutService.findBlocksWithCoaId(docId);
                List<LayoutBlockEntity> blocks = layoutService.getBlocks(blockIds);
                List<CoaMappingEntity> coaMappings =
                        blocks.stream()
                                .map(block -> block.getTableRows().stream()
                                        .filter(row -> row.getCoaData() != null && row.getCoaData()
                                                .getCoaId() > 1)
                                        .map(row -> rowToCoaMapping(block, row))
                                        .toList())
                                .flatMap(List::stream)
                                .toList();
                newCoaMappings.addAll(coaMappings);
                logger.debugf("Adding new coa mapping for docId %s : ", docId);
            }
            coaMappingRepository.persist(newCoaMappings);

            existingCoaMappings.addAll(newCoaMappings);
        }
        List<CoaMappingEntity> filteredCoaMappings = existingCoaMappings.stream()
                .filter(coaMapping -> !onlyActive || coaMapping.getIsApproved())
                .toList();
        return coaMappingService.toDtoList(filteredCoaMappings);
    }

    @Transactional
    public long deleteMappedCoaRows(List<UUID> docIds) {
        long deleteCount = coaMappingRepository.deleteByDocIds(docIds);
        logger.debugf("Deleted %s CoaMapping rows for docIds %s", deleteCount, docIds);
        return deleteCount;
    }

    public Optional<TableRowEntity> getRow(Integer tableId, Integer rowId) {
        return tableRowRepository.findByIdOptional(new TableRowPkId(tableId, rowId));
    }

    public List<MappedRowDto> getMappedCoaRowsByIds(List<Integer> explainabilityIds) {
        List<CoaMappingEntity> coaMappings = coaMappingRepository.findByIds(explainabilityIds);
        return coaMappingService.toDtoList(coaMappings);
    }

    @Transactional
    public List<TableHeaderEntity> insertHeader(DbHeader.HeaderDto headerDto) {

        int tableId = headerDto.getTableId();
        Byte maxHeaderIdForTable = tableHeaderRepository.findMaxHeaderIdForTable(tableId);
        LayoutBlockEntity blockForHeader = layoutService.getBlock(tableId);
        TableHeaderEntity header = TableHeaderEntity.builder()
                .tableHeaderPkId(new TableHeaderPkId(headerDto.getTableId(),
                        maxHeaderIdForTable == null ? 0 : maxHeaderIdForTable + 1))
                .pos(headerDto.getPos())
                .text(headerDto.getText())
                .bbox(new Bbox(headerDto.getXMin(), headerDto.getXMax(), headerDto.getYMin(), headerDto.getYMax()))
                .layoutBlock(blockForHeader)
                .score(headerDto.getScore().byteValue())
                .build();

        List<TableHeaderEntity> existingHeaders = tableHeaderRepository.getForTableSortByPos(tableId);
        if (header.getPos() <= existingHeaders.size()) {
            existingHeaders.add(header.getPos(), header);
        } else {
            existingHeaders.add(header);
            header.setPos(existingHeaders.size() - 1);
        }
        int indexForHeaderPosUpdate = header.getPos() + 1;
        while (indexForHeaderPosUpdate < existingHeaders.size()) {
            TableHeaderEntity headerEntityForPosUpdate = existingHeaders.get(indexForHeaderPosUpdate);
            int currentPos = headerEntityForPosUpdate.getPos();
            headerEntityForPosUpdate.setPos(currentPos + 1);
            indexForHeaderPosUpdate++;
        }
        tableHeaderRepository.persist(existingHeaders);
        updateRowsForNewHeader(headerDto);
        ExtractedTableHeaderAuditDto.Create auditHeader = new ExtractedTableHeaderAuditDto.Create(
                header.getTableHeaderPkId().getTableId(), header.getTableHeaderPkId().getHeaderId().intValue(),
                TableHeaderPkId.HEADER_ID_COL_NAME, header.getTableHeaderPkId().getHeaderId().toString());
        exchangeService.auditCreateHeaders(List.of(auditHeader));
        return existingHeaders;
    }

    @Transactional
    public List<TableHeaderEntity> deleteHeader(DbHeader.DeleteHeaderDto deleteHeaderDto) {

        List<TableHeaderEntity> existingHeaders = tableHeaderRepository.getForTableSortByPos(deleteHeaderDto.tableId());

        Optional<TableHeaderEntity> optHeaderForDeletion = existingHeaders.stream()
                .filter(tableHeaderEntity -> tableHeaderEntity.getTableHeaderPkId()
                        .getHeaderId().intValue() == deleteHeaderDto.headerId())
                .findAny();

        if (optHeaderForDeletion.isEmpty()) {
            ResponseException.throwResponseException(Response.Status.NOT_FOUND,
                    "Failed to delete missing header id " + deleteHeaderDto.headerId() + " for table " + deleteHeaderDto.headerId());
        }

        List<TableHeaderEntity> headersForPosUpdate = existingHeaders.subList(optHeaderForDeletion.get().getPos() + 1,
                existingHeaders.size());
        long deleteCount = tableHeaderRepository.deleteByTableIdAndHeaderId(deleteHeaderDto.tableId(),
                deleteHeaderDto.headerId());

        if (deleteCount != 1) {
            ResponseException.throwResponseException(Response.Status.CONFLICT,
                    "Failed to delete header id " + deleteHeaderDto.headerId() + " for table " + deleteHeaderDto.tableId());
        }

        for (TableHeaderEntity headerForPosUpdate : headersForPosUpdate) {
            headerForPosUpdate.setPos(headerForPosUpdate.getPos() - 1);
        }

        tableHeaderRepository.persist(headersForPosUpdate);
        updateRowsForDeletedHeader(optHeaderForDeletion.get());
        ExtractedTableHeaderAuditDto.Delete auditHeader = new ExtractedTableHeaderAuditDto.Delete(
                deleteHeaderDto.tableId(), deleteHeaderDto.headerId(),
                TableHeaderPkId.HEADER_ID_COL_NAME, String.valueOf(deleteHeaderDto.headerId()));
        exchangeService.auditDeleteHeaders(List.of(auditHeader));
        return tableHeaderRepository.getForTableSortByPos(deleteHeaderDto.tableId());
    }

    @Transactional
    public List<TableRowEntity> insertRow(DbRow.RowDto rowDto) {

        int tableId = rowDto.getTableId();
        Short maxRowIdForTable = tableRowRepository.findMaxRowIdForTable(tableId);

        List<TableHeaderEntity> headers = tableHeaderRepository.findHeadersForTable(rowDto.getTableId());
        LayoutBlockEntity block = layoutService.getBlock(rowDto.getTableId());

        int headersCount = headers.size();
        if (headersCount == 0) {
            ResponseException.throwResponseException(Response.Status.NOT_FOUND,
                    "No headers found for table " + rowDto.getTableId());
        }

        List<String> cellsText = rowDto.getCellsText();
        cellsText.addAll(Stream.generate(() -> StringUtils.EMPTY)
                .limit((headersCount - 1))
                .toList());

        TableRowEntity row = TableRowEntity.builder()
                .tableRowPkId(
                        new TableRowPkId(rowDto.getTableId(), maxRowIdForTable == null ? 0 : maxRowIdForTable + 1))
                .cellsText(cellsText)
                .bbox(new Bbox(rowDto.getXMin(), rowDto.getXMax(), rowDto.getYMin(), rowDto.getYMax()))
                .score(rowDto.getScore().byteValue())
                .ntaTable(null)
                .parentText(rowDto.getParentText())
                .layoutBlock(block)
                .comment(rowDto.getComment())
                .pos(rowDto.getPos())
                .build();

        List<TableRowEntity> existingRows = tableRowRepository.getForTableSortByPos(rowDto.getTableId());
        List<TableRowEntity> updatedRows = new ArrayList<>();

        if (row.getPos() <= existingRows.size()) {
            updatedRows.add(row);
        } else {
            row.setPos(existingRows.size());
            updatedRows.add(row);
        }
        List<TableRowEntity> rowsForPosUpdate = existingRows.stream()
                .filter(existingRow -> existingRow.getPos() >= row.getPos())
                .toList();
        rowsForPosUpdate.forEach(rowForPosUpdate -> rowForPosUpdate.setPos(rowForPosUpdate.getPos() + 1));

        updatedRows.addAll(rowsForPosUpdate);
        tableRowRepository.persist(updatedRows);
        ExtractedTableRowAuditDto.Create auditRow = new ExtractedTableRowAuditDto.Create(
                row.getTableRowPkId().getTableId(), row.getTableRowPkId().getRowId().intValue(),
                TableRowPkId.ROW_ID_COL_NAME,
                row.getTableRowPkId().getRowId().toString());
        exchangeService.auditCreateRows(List.of(auditRow));
        return tableRowRepository.getForTableSortByPos(rowDto.getTableId());
    }

    @Transactional
    public List<TableRowEntity> deleteRow(DbRow.DeleteRowDto deleteRowDto) {

        Optional<ExtractedTableRowCoaDataJoinEntity> optExistingRowCoaJoinEntity =
                extractedRowCoaDataService.findByRowOptional(
                        deleteRowDto.tableId(),
                        deleteRowDto.rowId());
        if (optExistingRowCoaJoinEntity.isPresent()) {
            extractedRowCoaDataService.deleteRowCoaDataMapping(deleteRowDto.tableId(), deleteRowDto.rowId(), false,
                    false);
        }

        TableRowEntity rowForDeletion = tableRowRepository.findById(
                new TableRowPkId(deleteRowDto.tableId(), deleteRowDto.rowId()));
        List<TableRowEntity> existingRows = tableRowRepository.getForTableSortByPos(deleteRowDto.tableId());

        List<TableRowEntity> rowsForPosUpdate = existingRows.stream()
                .filter(existingRow -> existingRow.getPos() > rowForDeletion.getPos())
                .toList();

        rowsForPosUpdate.forEach(rowForPosUpdate -> rowForPosUpdate.setPos(rowForPosUpdate.getPos() - 1));

        List<TableRowEntity> updatedRows = new ArrayList<>(rowsForPosUpdate);

        tableRowRepository.deleteForTableIdAndRowId(deleteRowDto.tableId(), deleteRowDto.rowId());
        ExtractedTableRowAuditDto.Delete auditRow = new ExtractedTableRowAuditDto.Delete(
                deleteRowDto.tableId(), deleteRowDto.rowId(),
                TableRowPkId.ROW_ID_COL_NAME,
                String.valueOf(deleteRowDto.rowId()));
        exchangeService.auditDeleteRows(List.of(auditRow));

        tableRowRepository.persist(updatedRows);
        return tableRowRepository.getForTableSortByPos(deleteRowDto.tableId());
    }

    public List<TableRowEntity> findByNtaTableId(Integer tableId) {
        return tableRowRepository.findAllByNtaBlockId(tableId);
    }

    @Transactional
    public List<TableRowEntity> duplicateRowsForTable(LayoutBlockEntity srcTable, LayoutBlockEntity destTable) {
        logger.debugf("Duplicating rows from table %s ", srcTable.getBlockId());
        List<TableRowEntity> srcTableRows = srcTable.getTableRows();
        List<TableRowEntity> duplicatedRows = srcTableRows.stream()
                .map(row -> {
                    TableRowEntity duplicatedRow = new TableRowEntity(row);
                    duplicatedRow.setTableRowPkId(new TableRowPkId(null, row.getTableRowPkId().getRowId()));
                    duplicatedRow.setLayoutBlock(destTable);
                    //Updated after all blocks have been created.
                    duplicatedRow.setNtaTable(null);
                    return duplicatedRow;
                })
                .toList();

        logger.debugf("Duplicated rows %s", duplicatedRows);
        return duplicatedRows;
    }

    @Transactional
    public List<TableHeaderEntity> duplicateHeadersForTable(LayoutBlockEntity srcTable, LayoutBlockEntity destTable) {
        logger.debugf("Duplicating headers from table %s", srcTable.getBlockId());
        List<TableHeaderEntity> srcHeaders = srcTable.getTableHeaders();
        return srcHeaders.stream()
                .map(header -> {
                    TableHeaderEntity duplicatedHeader = new TableHeaderEntity(header);
                    duplicatedHeader.setTableHeaderPkId(new TableHeaderPkId(null,
                            header.getTableHeaderPkId().getHeaderId()));
                    duplicatedHeader.setLayoutBlock(destTable);
                    return duplicatedHeader;
                })
                .toList();
    }

    @Transactional
    public List<TableRowEntity> saveRows(List<TableRowEntity> rows) {
        tableRowRepository.persist(rows);
        return rows;
    }

    public List<TableRowEntity> getRowForTableAtPos(int tableId, int pos) {
        return tableRowRepository.getForTableSortByPos(tableId).stream()
                .filter(row -> row.getPos() == pos)
                .toList();
    }

    private enum RowUpdateField {
        CELLS_TEXT,
        BBOX,
        SCORE,
        NTA_TABLE_ID,
        PARENT_TEXT,
        HEADER_IDS,
        COMMENT
    }

    private enum HeaderUpdateField {
        TEXT,
        BBOX,
        SCORE
    }
}
