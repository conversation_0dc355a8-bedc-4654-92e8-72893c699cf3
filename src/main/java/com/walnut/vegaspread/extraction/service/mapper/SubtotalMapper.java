package com.walnut.vegaspread.extraction.service.mapper;

import com.walnut.vegaspread.extraction.entity.SubtotalEntity;
import com.walnut.vegaspread.extraction.model.SubtotalDto;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

@ApplicationScoped
public class SubtotalMapper {

    public SubtotalDto.Response toDto(SubtotalEntity subtotal) {
        return new SubtotalDto.Response(subtotal.getId(), subtotal.getCoaClient(), subtotal.getCoaClient(),
                subtotal.getSubtotalName(), subtotal.getExcelRowNumber().intValue());
    }

    public List<SubtotalDto.Response> toDtoList(
            List<SubtotalEntity> subtotals) {
        return subtotals.stream()
                .map(this::toDto)
                .toList();
    }
}
