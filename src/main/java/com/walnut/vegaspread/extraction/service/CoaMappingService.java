package com.walnut.vegaspread.extraction.service;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import com.walnut.vegaspread.extraction.entity.TableTagEntity;
import com.walnut.vegaspread.extraction.model.MappedRowDto;
import com.walnut.vegaspread.extraction.repository.CoaMappingRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@ApplicationScoped
public class CoaMappingService {
    private final CoaMappingRepository coaMappingRepository;
    private final TableTagService tableTagService;

    public CoaMappingService(CoaMappingRepository coaMappingRepository, TableTagService tableTagService) {
        this.coaMappingRepository = coaMappingRepository;
        this.tableTagService = tableTagService;
    }

    public List<MappedRowDto> toDtoList(List<CoaMappingEntity> coaMappingEntities) {
        List<TableTagEntity> tableTagEntities = tableTagService.getTableTagByIds(coaMappingEntities.stream()
                .map(CoaMappingEntity::getTableTypeId)
                .distinct()
                .toList());
        Map<Integer, TableTagEntity> tableTagMap = tableTagEntities.stream()
                .collect(Collectors.toMap(TableTagEntity::getId, tableTagEntity -> tableTagEntity));
        return coaMappingEntities.stream().map(coaMappingEntity -> {
            TableTagEntity tableTag = tableTagMap.get(coaMappingEntity.getTableTypeId());
            if (tableTag == null) {
                ResponseException.throwResponseException(Response.Status.NOT_FOUND,
                        "Table tag not found for id " + coaMappingEntity.getTableTypeId());
                return null;
            }
            return new MappedRowDto(
                    coaMappingEntity.getId(),
                    coaMappingEntity.getTableId(),
                    coaMappingEntity.getRowId().intValue(),
                    coaMappingEntity.getDocId(),
                    coaMappingEntity.getTableTypeId(),
                    tableTag.getTag(),
                    coaMappingEntity.getRowParent(),
                    coaMappingEntity.getText(),
                    coaMappingEntity.getFsHeader(),
                    coaMappingEntity.getFsText(),
                    coaMappingEntity.getCoaId());
        }).toList();
    }

    public CoaMappingEntity findById(Integer coaMappingId) {
        return coaMappingRepository.findById(coaMappingId);
    }

    public Optional<CoaMappingEntity> findByIdOptional(Integer coaMappingId) {
        return coaMappingRepository.findByIdOptional(coaMappingId);
    }

    public List<Integer> getCoaIdsForTableTypeOrFsHeader(String tableType, String fsHeader) {
        List<Integer> tableTagIds = tableTagService.findByTableTagLike(tableType).stream()
                .map(TableTagEntity::getId)
                .toList();
        return coaMappingRepository.findByTableTypeIdInOrFsHeaderLike(tableTagIds, fsHeader).stream()
                .map(CoaMappingEntity::getCoaId)
                .distinct()
                .sorted()
                .toList();
    }

    public List<CoaMappingEntity> findByDocIdsAndTableTypeId(List<UUID> docIds, Integer tableTypeId) {
        return coaMappingRepository.findByDocIdsAndTableTypeId(docIds, tableTypeId);
    }

    @Transactional
    public long deleteByDocId(UUID docId) {
        return coaMappingRepository.deleteByDocId(docId);
    }
}
