package com.walnut.vegaspread.extraction.service;

import com.walnut.vegaspread.extraction.model.Summary;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

public class SummaryRowMergeUtility {
    public static final Logger logger = LogManager.getLogger(SummaryRowMergeUtility.class);

    private SummaryRowMergeUtility() {
    }

    public static Summary.SummaryOutput mergeSummariesForDocs(List<Summary.SummaryOutput> docSummaries) {

        List<Summary.SummaryHeader> mergedHeaders = mergeHeaders(docSummaries);
        List<Summary.SummaryRow> mergedRows = mergeRows(
                docSummaries.stream()
                        .map(Summary.SummaryOutput::rows)
                        .toList(),
                mergedHeaders.size() - 1, false);

        return new Summary.SummaryOutput(mergedHeaders, mergedRows);
    }

    private static List<Summary.SummaryHeader> mergeHeaders(List<Summary.SummaryOutput> docSummaries) {
        // Add all headers including the first COA header for the first document.
        List<Summary.SummaryHeader> mergedHeaders = new ArrayList<>(docSummaries.get(0).headers());
        for (int idx = 1; idx < docSummaries.size(); idx++) {
            List<Summary.SummaryHeader> headersForCurrentDoc = docSummaries.get(idx).headers();
            // Exclude the COA header for subsequent documents.
            mergedHeaders.addAll(headersForCurrentDoc.subList(1, headersForCurrentDoc.size()));
        }
        AtomicInteger counter = new AtomicInteger(0);
        mergedHeaders = mergedHeaders.stream()
                .map(header -> new Summary.SummaryHeader(counter.getAndIncrement(), header.text(), header.isNote()))
                .toList();
        logger.debug("Merged headers: {}", mergedHeaders);
        return mergedHeaders;
    }

    private static List<Summary.SummaryRow> mergeRows(List<List<Summary.SummaryRow>> rowsForAllSummaries,
                                                      int totalHeaders, boolean isMergeChildren) {

        Map<String, Summary.SummaryRow> mergedRowsMap = new HashMap<>();

        List<Summary.SummaryRow> rowsForBaseSummary = rowsForAllSummaries.get(0);
        if (rowsForBaseSummary.isEmpty()) {
            return Collections.emptyList();
        }
        int headersLengthForMergedSummaries = rowsForBaseSummary.get(0).getCellsText().size() - 1;

        rowsForBaseSummary.forEach(
                rowForBaseSummary -> mergedRowsMap.put(rowForBaseSummary.getCellsText().get(0), rowForBaseSummary));

        // Merge with rows from other summaries
        for (int j = 1; j < rowsForAllSummaries.size(); j++) {

            List<Summary.SummaryRow> rowsForNextSummaryToMerge = rowsForAllSummaries.get(j);

            for (Summary.SummaryRow rowForNextSummaryToMerge : rowsForNextSummaryToMerge) {

                mergeAndApplyLeftPadding(totalHeaders, rowForNextSummaryToMerge, mergedRowsMap,
                        headersLengthForMergedSummaries);
            }
            //Update headers length for merged summaries when we encounter top level coa.
            if (!isMergeChildren && !rowsForNextSummaryToMerge.isEmpty()) {
                headersLengthForMergedSummaries += rowsForNextSummaryToMerge.get(0).getCellsText().size() - 1;
            }
        }
        //Pad (n-x) zeros to the end of cellsText where a coa has not appeared after first x summaries out of n
        // summaries.
        if (!isMergeChildren) {
            applyRightPadding(totalHeaders, mergedRowsMap);
        }
        return new ArrayList<>(mergedRowsMap.values());
    }

    private static void mergeAndApplyLeftPadding(int totalHeaders, Summary.SummaryRow rowForNextSummaryToMerge,
                                                 Map<String, Summary.SummaryRow> mergedRowsMap,
                                                 int headersLengthForMergedSummaries) {
        String coaForNextSummaryToMerge = rowForNextSummaryToMerge.getCellsText().get(0);
        Summary.SummaryRow matchingRowInExistingMap = mergedRowsMap.get(coaForNextSummaryToMerge);

        //COA exists in the merged summary
        if (matchingRowInExistingMap != null) {
            // Merge the current row from next summary and update the merged summary.
            mergedRowsMap.merge(coaForNextSummaryToMerge, rowForNextSummaryToMerge, (mergedRow, rowToMerge) -> {
                //Add all data to merged row in the map excluding the coa.
                mergedRow.getCellsText()
                        .addAll(rowToMerge.getCellsText().subList(1, rowToMerge.getCellsText().size()));
                //Merge children
                List<Summary.SummaryRow> mergedChildren = new ArrayList<>();
                if (!mergedRow.getChildren().isEmpty() || !rowToMerge.getChildren().isEmpty()) {
                    mergedChildren = mergeRows(
                            List.of(mergedRow.getChildren(), rowToMerge.getChildren()), totalHeaders, true);
                }
                return Summary.SummaryRow.builder()
                        .rowId(mergedRow.getRowId())
                        .coaId(mergedRow.getCoaId())
                        .coaText(mergedRow.getCoaText())
                        .coaScore(mergedRow.getCoaScore())
                        .cellsText(mergedRow.getCellsText())
                        .tableId(mergedRow.getTableId())
                        .tableTag(mergedRow.getTableTag())
                        .pageNum(mergedRow.getPageNum())
                        .xMin(mergedRow.getXMin())
                        .xMax(mergedRow.getXMax())
                        .yMin(mergedRow.getYMin())
                        .yMax(mergedRow.getYMax())
                        .children((ArrayList<Summary.SummaryRow>) mergedChildren)
                        .build();
            });
        } else {
            // If no match found, pad cellsText with 0's for all previously merged summaries, in the
            // summary to merge row and add to merged map.
            rowForNextSummaryToMerge = padCellsTextForSummaryToMerge(List.of(rowForNextSummaryToMerge),
                    headersLengthForMergedSummaries).get(0);

            mergedRowsMap.putIfAbsent(coaForNextSummaryToMerge, rowForNextSummaryToMerge);
        }
    }

    private static void applyRightPadding(int totalHeaders, Map<String, Summary.SummaryRow> mergedRowsMap) {
        mergedRowsMap.forEach((coa, summaryRow) -> {
            ArrayList<String> endPadding = new ArrayList<>(
                    Collections.nCopies(totalHeaders - (summaryRow.getCellsText().size() - 1), "0"));
            ArrayList<Summary.SummaryRow> paddedChildren = padChildren(summaryRow.getChildren(), totalHeaders);
            summaryRow.getCellsText().addAll(endPadding);
            summaryRow.setChildren(paddedChildren);
            //Update map with padded summary row.
            mergedRowsMap.put(coa, summaryRow);
        });
    }

    private static ArrayList<Summary.SummaryRow> padChildren(ArrayList<Summary.SummaryRow> children, int totalHeaders) {
        for (Summary.SummaryRow child : children) {
            ArrayList<String> endPadding = new ArrayList<>(
                    Collections.nCopies(totalHeaders - (child.getCellsText().size() - 1), "0"));
            ArrayList<Summary.SummaryRow> paddedChildren = padChildren(child.getChildren(), totalHeaders);
            child.getCellsText().addAll(endPadding);
            child.setChildren(paddedChildren);
        }
        return children;
    }

    private static List<Summary.SummaryRow> padCellsTextForSummaryToMerge(List<Summary.SummaryRow> rows,
                                                                          int headersLengthForMergedSummaries) {
        String[] padding = new String[headersLengthForMergedSummaries];
        Arrays.fill(padding, "0");

        List<Summary.SummaryRow> paddedRows = new ArrayList<>();
        for (Summary.SummaryRow row : rows) {

            List<Summary.SummaryRow> children = row.getChildren();
            if (!children.isEmpty()) {
                children = padCellsTextForSummaryToMerge(children, headersLengthForMergedSummaries);
            }
            ArrayList<String> paddedCellsText = new ArrayList<>(row.getCellsText());
            //Add zeros for all previous summaries, after the coa name, since previous summaries were missing this coa.
            paddedCellsText.addAll(1, Arrays.asList(padding));

            paddedRows.add(Summary.SummaryRow.builder()
                    .rowId(row.getRowId())
                    .coaId(row.getCoaId())
                    .coaText(row.getCoaText())
                    .coaScore(row.getCoaScore())
                    .cellsText(paddedCellsText)
                    .tableId(row.getTableId())
                    .tableTag(row.getTableTag())
                    .pageNum(row.getPageNum())
                    .xMin(row.getXMin())
                    .xMax(row.getXMax())
                    .yMin(row.getYMin())
                    .yMax(row.getYMax())
                    .children((ArrayList<Summary.SummaryRow>) children)
                    .build());
        }
        return paddedRows;
    }
}
