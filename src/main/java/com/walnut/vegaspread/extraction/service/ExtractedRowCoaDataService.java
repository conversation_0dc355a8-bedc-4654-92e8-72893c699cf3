package com.walnut.vegaspread.extraction.service;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedRowCoaDataAuditDto;
import com.walnut.vegaspread.extraction.entity.CoaDataEntity;
import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.model.CoaDataDto;
import com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto;
import com.walnut.vegaspread.extraction.model.OperationClassification;
import com.walnut.vegaspread.extraction.primarykey.ExtractedTableRowCoaDataPkId;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import com.walnut.vegaspread.extraction.repository.ExtractedRowCoaDataRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response;
import org.jboss.logging.Logger;

import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service for managing COA (Chart of Accounts) data mappings for extracted table rows.
 * Handles creation, updates, and deletion of COA data mappings with proper auditing.
 */

@ApplicationScoped
public class ExtractedRowCoaDataService {
    private static final Logger logger = Logger.getLogger(ExtractedRowCoaDataService.class);

    private static final int COA_ID_FOR_DELETION = 1;

    private final ExtractedRowCoaDataRepository extractedRowCoaDataRepository;
    private final CoaDataService coaDataService;
    private final CoaMappingService coaMappingService;
    private final ExchangeService exchangeService;

    public ExtractedRowCoaDataService(ExtractedRowCoaDataRepository extractedRowCoaDataRepository,
                                      CoaDataService coaDataService, CoaMappingService coaMappingService,
                                      ExchangeService exchangeService) {
        this.extractedRowCoaDataRepository = extractedRowCoaDataRepository;
        this.coaDataService = coaDataService;
        this.coaMappingService = coaMappingService;
        this.exchangeService = exchangeService;
    }

    /**
     * Main orchestration method for creating or updating COA data mappings.
     * Classifies operations and delegates to appropriate handlers.
     */
    @Transactional
    public List<ExtractedTableRowCoaDataJoinEntity> createOrUpdateCoaDataMapping(
            List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> rowCoaJoinDtos,
            Boolean isApiKeyAuthenticated) {
        logger.debugf("Starting transaction for COA mapping creation/updation");

        if (rowCoaJoinDtos == null || rowCoaJoinDtos.isEmpty()) {
            logger.debugf("No row coa mapping found");
            return Collections.emptyList();
        }

        // Classify operations based on COA data and existing mappings
        OperationClassification classification = classifyOperations(rowCoaJoinDtos);

        // Execute operations in order: DELETE, CREATE, UPDATE
        List<ExtractedTableRowCoaDataJoinEntity> resultEntities = new ArrayList<>();

        logger.debugf("Deleting %d row coa mappings", classification.deleteOperations().size());
        deleteRowCoaDataMappings(classification.deleteOperations(), isApiKeyAuthenticated, false);

        logger.debugf("Creating %d row coa mappings", classification.createOperations().size());
        resultEntities.addAll(createCoaDataMapping(classification.createOperations(), isApiKeyAuthenticated));

        logger.debugf("Updating %d row coa mappings", classification.updateOperations().size());
        resultEntities.addAll(updateCoaDataMapping(classification.updateOperations(), isApiKeyAuthenticated));

        logger.debugf("Completed transaction for COA mapping creation/updation");
        return resultEntities;
    }

    /**
     * Classifies the incoming DTOs into different operation types based on COA data and existing mappings.
     */
    private OperationClassification classifyOperations(
            List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> rowCoaJoinDtos) {
        Map<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate, ExtractedTableRowCoaDataJoinEntity> updateOperations =
                new HashMap<>();
        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> createOperations = new ArrayList<>();
        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> deleteOperations = new ArrayList<>();

        for (ExtractedTableRowCoaDataJoinDto.CreateOrUpdate dto : rowCoaJoinDtos) {
            OperationType operationType = determineOperationType(dto);
            Optional<ExtractedTableRowCoaDataJoinEntity> existingEntity = findExistingMapping(dto);

            switch (operationType) {
                case CREATE -> {
                    logger.debugf("Classified CREATE operation for table %s, row %s", dto.tableId(), dto.rowId());
                    createOperations.add(dto);
                }
                case UPDATE -> {
                    logger.debugf("Classified UPDATE operation for table %s, row %s", dto.tableId(), dto.rowId());
                    updateOperations.put(dto, existingEntity.orElseThrow());
                }
                case DELETE -> {
                    logger.debugf("Classified DELETE operation for table %s, row %s", dto.tableId(), dto.rowId());
                    deleteOperations.add(dto);
                }
                case SKIP -> logger.debugf("Skipping operation for table %s, row %s", dto.tableId(), dto.rowId());
            }
        }

        return new OperationClassification(createOperations, updateOperations, deleteOperations);
    }

    /**
     * Determines the type of operation based on COA data and existing mappings.
     */
    private OperationType determineOperationType(ExtractedTableRowCoaDataJoinDto.CreateOrUpdate dto) {
        if (dto.coaData() == null) {
            return OperationType.SKIP;
        }

        Integer coaId = dto.coaData().coaId();
        Optional<ExtractedTableRowCoaDataJoinEntity> existingMapping = findExistingMapping(dto);

        if (coaId == COA_ID_FOR_DELETION) {
            return OperationType.DELETE;
        } else if (coaId > COA_ID_FOR_DELETION) {
            return existingMapping.isPresent() ? OperationType.UPDATE : OperationType.CREATE;
        }

        return OperationType.SKIP;
    }

    /**
     * Finds existing COA data mapping for the given DTO.
     */
    private Optional<ExtractedTableRowCoaDataJoinEntity> findExistingMapping(
            ExtractedTableRowCoaDataJoinDto.CreateOrUpdate dto) {
        return extractedRowCoaDataRepository.findByRowOptional(dto.tableId(), dto.rowId().shortValue());
    }

    /**
     * Creates new COA data mappings for the given DTOs.
     */
    @Transactional
    public List<ExtractedTableRowCoaDataJoinEntity> createCoaDataMapping(
            List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> rowCoaCreateDtos,
            Boolean isApiKeyAuthenticated) {
        logger.debugf("Starting transaction for COA mapping creation");

        if (rowCoaCreateDtos.isEmpty()) {
            return Collections.emptyList();
        }

        List<ExtractedTableRowCoaDataJoinEntity> createdEntities = new ArrayList<>();
        List<ExtractedRowCoaDataAuditDto.Create> auditDtos = new ArrayList<>();

        for (ExtractedTableRowCoaDataJoinDto.CreateOrUpdate dto : rowCoaCreateDtos) {
            logger.debugf("Creating row coa mapping for table %s, row %s", dto.tableId(), dto.rowId());

            // Resolve explainability mapping
            CoaMappingEntity explainabilityMapping = resolveExplainabilityForCreate(dto, isApiKeyAuthenticated,
                    auditDtos);

            // Resolve COA data entity
            CoaDataEntity coaDataEntity = resolveCoaDataEntity(dto.coaData());

            // Create join entity
            ExtractedTableRowCoaDataJoinEntity joinEntity = createJoinEntity(dto, coaDataEntity, explainabilityMapping);
            createdEntities.add(joinEntity);

            // Add COA ID audit
            auditDtos.add(createCoaIdCreateAudit(dto));
        }

        // Persist entities and audits
        saveEntitiesAndAudits(createdEntities, auditDtos, isApiKeyAuthenticated);

        logger.debugf("Create transaction completed successfully");
        return createdEntities;
    }

    /**
     * Resolves explainability mapping for create operations.
     */
    private CoaMappingEntity resolveExplainabilityForCreate(
            ExtractedTableRowCoaDataJoinDto.CreateOrUpdate dto,
            Boolean isApiKeyAuthenticated,
            List<ExtractedRowCoaDataAuditDto.Create> auditDtos) {

        if (Boolean.FALSE.equals(isApiKeyAuthenticated)) {
            logger.debugf("User created coa for table %s, row %s", dto.tableId(), dto.rowId());
            return null;
        }

        Integer explainabilityId = dto.explainabilityId();
        if (explainabilityId == null) {
            logger.debugf("Explainability id is null for table %s, row %s", dto.tableId(), dto.rowId());
            return null;
        }

        logger.debugf("Resolving explainability id %s for table %s, row %s", explainabilityId, dto.tableId(),
                dto.rowId());
        CoaMappingEntity mapping = coaMappingService.findByIdOptional(explainabilityId)
                .orElseThrow(() -> new InvalidParameterException("Invalid coa mapping id " + explainabilityId));

        // Audit explainability creation (only for non-null values to avoid null -> null audit)
        auditDtos.add(new ExtractedRowCoaDataAuditDto.Create(
                dto.tableId(), dto.rowId(),
                ExtractedTableRowCoaDataJoinEntity.EXPLAINABILITY_ID_COL_NAME,
                mapping.getId().toString()));

        return mapping;
    }

    /**
     * Resolves COA data entity, creating it if it doesn't exist.
     */
    private CoaDataEntity resolveCoaDataEntity(CoaDataDto.Create coaData) {
        return coaDataService.findByUseCoaAndCoaIdAndCoaScore(
                        coaData.useCoa(), coaData.coaId(), coaData.coaScore())
                .orElseGet(() -> {
                    logger.debugf("Adding new coa data for useCoa %s, coaId %s, coaScore %s",
                            coaData.useCoa(), coaData.coaId(), coaData.coaScore());
                    return coaDataService.addCoaData(coaData);
                });
    }

    /**
     * Creates a join entity from the given components.
     */
    private ExtractedTableRowCoaDataJoinEntity createJoinEntity(
            ExtractedTableRowCoaDataJoinDto.CreateOrUpdate dto,
            CoaDataEntity coaDataEntity,
            CoaMappingEntity explainabilityMapping) {

        logger.debugf("Creating join entity with coa data id %s for table %s, row %s",
                coaDataEntity.getId(), dto.tableId(), dto.rowId());

        return new ExtractedTableRowCoaDataJoinEntity(
                new TableRowPkId(dto.tableId(), dto.rowId()),
                coaDataEntity.getId(),
                explainabilityMapping);
    }

    /**
     * Creates COA ID audit DTO for create operations.
     */
    private ExtractedRowCoaDataAuditDto.Create createCoaIdCreateAudit(
            ExtractedTableRowCoaDataJoinDto.CreateOrUpdate dto) {
        return new ExtractedRowCoaDataAuditDto.Create(
                dto.tableId(),
                dto.rowId(),
                TableRowEntity.COA_ID_COL_NAME,
                dto.coaData().coaId().toString());
    }

    /**
     * Saves entities and creates audit records.
     */
    private void saveEntitiesAndAudits(
            List<ExtractedTableRowCoaDataJoinEntity> entities,
            List<ExtractedRowCoaDataAuditDto.Create> auditDtos,
            Boolean isApiKeyAuthenticated) {

        logger.debugf("Saving %d entities and %d audit records", entities.size(), auditDtos.size());
        extractedRowCoaDataRepository.saveRowCoaJoin(entities);
        exchangeService.auditExtractedRowCoaDataCreate(auditDtos, isApiKeyAuthenticated);
    }

    /**
     * Updates existing COA data mappings for the given DTOs.
     */
    @Transactional
    public List<ExtractedTableRowCoaDataJoinEntity> updateCoaDataMapping(
            Map<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate, ExtractedTableRowCoaDataJoinEntity> rowCoaJoinUpdateDtos,
            Boolean isApiKeyAuthenticated) {
        logger.debugf("Starting transaction for COA mapping updation");

        if (rowCoaJoinUpdateDtos.isEmpty()) {
            return Collections.emptyList();
        }

        List<ExtractedTableRowCoaDataJoinEntity> updatedEntities = new ArrayList<>();
        List<ExtractedRowCoaDataAuditDto.Update> auditDtos = new ArrayList<>();

        for (Map.Entry<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate, ExtractedTableRowCoaDataJoinEntity> entry :
                rowCoaJoinUpdateDtos.entrySet()) {
            ExtractedTableRowCoaDataJoinDto.CreateOrUpdate dto = entry.getKey();
            ExtractedTableRowCoaDataJoinEntity existingEntity = entry.getValue();

            logger.debugf("Updating COA mapping for table %s, row %s", dto.tableId(), dto.rowId());

            ExtractedTableRowCoaDataJoinEntity updatedEntity = processUpdateOperation(dto, existingEntity,
                    isApiKeyAuthenticated, auditDtos);
            updatedEntities.add(updatedEntity);
        }

        // Persist updates and audits
        saveUpdatesAndAudits(updatedEntities, auditDtos, isApiKeyAuthenticated);

        logger.debugf("Transaction completed for coa mapping updation");
        return updatedEntities;
    }

    /**
     * Processes a single update operation for COA data mapping.
     */
    private ExtractedTableRowCoaDataJoinEntity processUpdateOperation(
            ExtractedTableRowCoaDataJoinDto.CreateOrUpdate dto,
            ExtractedTableRowCoaDataJoinEntity existingEntity,
            Boolean isApiKeyAuthenticated,
            List<ExtractedRowCoaDataAuditDto.Update> auditDtos) {

        ExtractedTableRowCoaDataPkId pkId = existingEntity.getExtractedTableRowCoaDataPkId();

        // Create working copy of the existing entity
        ExtractedTableRowCoaDataJoinEntity workingEntity = new ExtractedTableRowCoaDataJoinEntity(
                pkId.getTableRowPkId(), pkId.getCoaDataId(), existingEntity.getExplainability());

        logExistingMapping(dto, workingEntity);

        // Check if COA data needs to be updated
        if (coaDataService.isUpdatedCoaData(workingEntity, dto.coaData())) {
            updateCoaDataInEntity(dto, workingEntity, isApiKeyAuthenticated, auditDtos);
        }

        return workingEntity;
    }

    /**
     * Logs information about the existing mapping.
     */
    private void logExistingMapping(ExtractedTableRowCoaDataJoinDto.CreateOrUpdate dto,
                                    ExtractedTableRowCoaDataJoinEntity entity) {
        Integer explainabilityId = Optional.ofNullable(entity.getExplainability())
                .map(CoaMappingEntity::getId)
                .orElse(null);

        logger.debugf("Existing mapping for table %s, row %s: coa_data_id=%s, explainability_id=%s",
                dto.tableId(), dto.rowId(),
                entity.getExtractedTableRowCoaDataPkId().getCoaDataId(),
                explainabilityId);
    }

    /**
     * Updates COA data in the entity when changes are detected.
     */
    private void updateCoaDataInEntity(
            ExtractedTableRowCoaDataJoinDto.CreateOrUpdate dto,
            ExtractedTableRowCoaDataJoinEntity entity,
            Boolean isApiKeyAuthenticated,
            List<ExtractedRowCoaDataAuditDto.Update> auditDtos) {

        Integer tableId = dto.tableId();
        Integer rowId = dto.rowId();
        Integer currentCoaDataId = entity.getExtractedTableRowCoaDataPkId().getCoaDataId();
        Integer currentCoaId = getCoaIdFromCoaDataId(currentCoaDataId);

        logger.debugf("COA data changed for table %s, row %s. Current COA ID: %s", tableId, rowId, currentCoaId);

        // Delete existing mapping to avoid concurrent update errors
        deleteRowCoaDataMapping(tableId, rowId, isApiKeyAuthenticated, true);

        // Resolve new COA data entity
        CoaDataEntity newCoaDataEntity = resolveCoaDataEntity(dto.coaData());
        Integer newCoaDataId = newCoaDataEntity.getId();
        Integer newCoaId = newCoaDataEntity.getCoaId();

        // Update the entity with new COA data
        entity.getExtractedTableRowCoaDataPkId().setCoaDataId(newCoaDataId);

        // Audit COA ID change if it actually changed
        if (!Objects.equals(newCoaId, currentCoaId)) {
            logger.debugf("COA ID changed from %s to %s for table %s, row %s", currentCoaId, newCoaId, tableId, rowId);
            auditDtos.add(new ExtractedRowCoaDataAuditDto.Update(
                    tableId, rowId, CoaDataEntity.COA_ID_COL_NAME,
                    currentCoaId.toString(), newCoaId.toString()));

            // Update explainability if COA ID changed
            updateExplainability(isApiKeyAuthenticated, dto, entity, auditDtos);
        }
    }

    /**
     * Saves updated entities and creates audit records.
     */
    private void saveUpdatesAndAudits(
            List<ExtractedTableRowCoaDataJoinEntity> entities,
            List<ExtractedRowCoaDataAuditDto.Update> auditDtos,
            Boolean isApiKeyAuthenticated) {

        logger.debugf("Saving %d updated entities and %d audit records", entities.size(), auditDtos.size());
        extractedRowCoaDataRepository.saveRowCoaJoin(entities);
        exchangeService.auditExtractedRowCoaDataUpdate(auditDtos, isApiKeyAuthenticated);
    }

    /**
     * Helper method to get COA ID from COA data ID.
     */
    private Integer getCoaIdFromCoaDataId(Integer coaDataId) {
        return coaDataService.findById(coaDataId).getCoaId();
    }

    /**
     * Updates explainability mapping for the given entity if changes are detected.
     */
    public ExtractedTableRowCoaDataJoinEntity updateExplainability(
            Boolean isApiKeyAuthenticated,
            ExtractedTableRowCoaDataJoinDto.CreateOrUpdate dto,
            ExtractedTableRowCoaDataJoinEntity entity,
            List<ExtractedRowCoaDataAuditDto.Update> auditDtos) {

        Integer currentExplainabilityId = Optional.ofNullable(entity.getExplainability())
                .map(CoaMappingEntity::getId)
                .orElse(null);
        Integer newExplainabilityId = dto.explainabilityId();

        // Check if explainability has changed
        if (Objects.equals(currentExplainabilityId, newExplainabilityId)) {
            return entity; // No change needed
        }

        Integer tableId = entity.getExtractedTableRowCoaDataPkId().getTableRowPkId().getTableId();
        Integer rowId = entity.getExtractedTableRowCoaDataPkId().getTableRowPkId().getRowId().intValue();

        // Handle different update scenarios
        if (shouldSetExplainabilityToNull(isApiKeyAuthenticated, newExplainabilityId)) {
            setExplainabilityToNull(entity, tableId, rowId, currentExplainabilityId, auditDtos);
        } else if (newExplainabilityId != null) {
            setExplainabilityToNewValue(entity, tableId, rowId, currentExplainabilityId, newExplainabilityId,
                    auditDtos);
        }

        return entity;
    }

    /**
     * Determines if explainability should be set to null based on authentication and new value.
     */
    private boolean shouldSetExplainabilityToNull(Boolean isApiKeyAuthenticated, Integer newExplainabilityId) {
        return Boolean.FALSE.equals(isApiKeyAuthenticated) ||
                (Boolean.TRUE.equals(isApiKeyAuthenticated) && newExplainabilityId == null);
    }

    /**
     * Sets explainability to null and creates audit record.
     */
    private void setExplainabilityToNull(
            ExtractedTableRowCoaDataJoinEntity entity,
            Integer tableId, Integer rowId, Integer currentExplainabilityId,
            List<ExtractedRowCoaDataAuditDto.Update> auditDtos) {

        if (currentExplainabilityId != null) { // Only audit if there was a previous value
            auditDtos.add(new ExtractedRowCoaDataAuditDto.Update(
                    tableId, rowId,
                    ExtractedTableRowCoaDataJoinEntity.EXPLAINABILITY_ID_COL_NAME,
                    currentExplainabilityId.toString(), null));
        }
        entity.setExplainability(null);
    }

    /**
     * Sets explainability to new value and creates audit record.
     */
    private void setExplainabilityToNewValue(
            ExtractedTableRowCoaDataJoinEntity entity,
            Integer tableId, Integer rowId, Integer currentExplainabilityId, Integer newExplainabilityId,
            List<ExtractedRowCoaDataAuditDto.Update> auditDtos) {

        CoaMappingEntity newMapping = coaMappingService.findByIdOptional(newExplainabilityId)
                .orElseThrow(() -> new InvalidParameterException("Invalid coa mapping id " + newExplainabilityId));

        auditDtos.add(new ExtractedRowCoaDataAuditDto.Update(
                tableId, rowId,
                ExtractedTableRowCoaDataJoinEntity.EXPLAINABILITY_ID_COL_NAME,
                currentExplainabilityId != null ? currentExplainabilityId.toString() : null,
                newMapping.getId().toString()));

        entity.setExplainability(newMapping);
    }

    /**
     * Deletes multiple COA data mappings.
     */
    public void deleteRowCoaDataMappings(
            List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> deleteOperations,
            Boolean isApiKeyAuthenticated,
            boolean isUpdate) {

        for (ExtractedTableRowCoaDataJoinDto.CreateOrUpdate dto : deleteOperations) {
            logger.debugf("Deleting COA mapping for table %s, row %s", dto.tableId(), dto.rowId());
            deleteRowCoaDataMapping(dto.tableId(), dto.rowId(), isApiKeyAuthenticated, isUpdate);
        }
    }

    /**
     * Deletes a single COA data mapping with proper validation and auditing.
     */
    @Transactional
    public void deleteRowCoaDataMapping(Integer tableId, Integer rowId, Boolean isApiKeyAuthenticated,
                                        boolean isUpdate) {
        logger.debugf("Starting transaction for COA mapping deletion: table %s, row %s", tableId, rowId);

        // Find and validate the mapping exists
        ExtractedTableRowCoaDataJoinEntity entityToDelete = findEntityForDeletion(tableId, rowId);

        // Create audit records if this is not part of an update operation
        List<ExtractedRowCoaDataAuditDto.Delete> auditDtos = new ArrayList<>();
        if (!isUpdate) {
            createDeletionAuditRecords(entityToDelete, tableId, rowId, auditDtos);
        }

        // Perform the deletion
        performDeletion(tableId, rowId);

        // Create audit records
        if (!auditDtos.isEmpty()) {
            exchangeService.auditExtractedRowCoaDataDelete(auditDtos, isApiKeyAuthenticated);
        }

        logger.debugf("Transaction completed for COA mapping deletion: table %s, row %s", tableId, rowId);
    }

    /**
     * Finds and validates the entity exists for deletion.
     */
    private ExtractedTableRowCoaDataJoinEntity findEntityForDeletion(Integer tableId, Integer rowId) {
        return extractedRowCoaDataRepository.findByRowOptional(tableId, rowId.shortValue())
                .orElseThrow(() -> new InvalidParameterException(
                        "Row coa data mapping cannot be deleted due to missing row for table " + tableId + " row " + rowId));
    }

    /**
     * Creates audit records for deletion operation.
     */
    private void createDeletionAuditRecords(
            ExtractedTableRowCoaDataJoinEntity entity,
            Integer tableId, Integer rowId,
            List<ExtractedRowCoaDataAuditDto.Delete> auditDtos) {

        logger.debugf("Creating deletion audit records for table %s, row %s", tableId, rowId);

        // Audit COA ID deletion
        Integer coaId = getCoaIdFromCoaDataId(entity.getExtractedTableRowCoaDataPkId().getCoaDataId());
        auditDtos.add(new ExtractedRowCoaDataAuditDto.Delete(
                tableId, rowId, CoaDataEntity.COA_ID_COL_NAME, coaId.toString()));

        // Audit explainability deletion if it exists (avoid null->null audit)
        if (entity.getExplainability() != null) {
            auditDtos.add(new ExtractedRowCoaDataAuditDto.Delete(
                    tableId, rowId,
                    ExtractedTableRowCoaDataJoinEntity.EXPLAINABILITY_ID_COL_NAME,
                    entity.getExplainability().getId().toString()));
        }
    }

    /**
     * Performs the actual deletion and validates success.
     */
    private void performDeletion(Integer tableId, Integer rowId) {
        logger.debugf("Performing deletion for table %s, row %s", tableId, rowId);

        long deleteCount = extractedRowCoaDataRepository.deleteMappedCoaData(tableId, rowId.shortValue());

        if (deleteCount != 1) {
            ResponseException.throwResponseException(Response.Status.CONFLICT,
                    "Failed to delete coa data mapping for row " + rowId + " in table " + tableId);
        }
    }

    /**
     * Deletes COA data mappings for multiple tables in bulk.
     */
    @Transactional
    public void deleteTableCoaDataMappings(List<Integer> tableIds, Boolean isApiKeyAuthenticated) {
        logger.debugf("Starting bulk deletion of COA mappings for tables: %s", tableIds);

        if (tableIds == null || tableIds.isEmpty()) {
            logger.debugf("No table IDs provided for deletion");
            return;
        }

        // Find all mappings to be deleted
        List<ExtractedTableRowCoaDataJoinEntity> entitiesToDelete =
                extractedRowCoaDataRepository.findByTableIds(tableIds);

        if (entitiesToDelete.isEmpty()) {
            logger.debugf("No COA mappings found for tables: %s", tableIds);
            return;
        }

        // Create audit records for all entities to be deleted
        List<ExtractedRowCoaDataAuditDto.Delete> auditDtos = createBulkDeletionAuditRecords(entitiesToDelete);

        // Perform bulk deletion
        performBulkDeletion(tableIds, entitiesToDelete.size());

        // Create audit records
        exchangeService.auditExtractedRowCoaDataDelete(auditDtos, isApiKeyAuthenticated);

        logger.debugf("Bulk deletion completed for tables: %s", tableIds);
    }

    /**
     * Creates audit records for bulk deletion operation.
     */
    private List<ExtractedRowCoaDataAuditDto.Delete> createBulkDeletionAuditRecords(
            List<ExtractedTableRowCoaDataJoinEntity> entities) {

        return entities.stream()
                .flatMap(entity -> createAuditRecordsForEntity(entity).stream())
                .toList();
    }

    /**
     * Creates audit records for a single entity.
     */
    private List<ExtractedRowCoaDataAuditDto.Delete> createAuditRecordsForEntity(
            ExtractedTableRowCoaDataJoinEntity entity) {
        List<ExtractedRowCoaDataAuditDto.Delete> auditDtos = new ArrayList<>();

        Integer tableId = entity.getExtractedTableRowCoaDataPkId().getTableRowPkId().getTableId();
        Integer rowId = entity.getExtractedTableRowCoaDataPkId().getTableRowPkId().getRowId().intValue();

        // Audit COA ID deletion
        Integer coaId = getCoaIdFromCoaDataId(entity.getExtractedTableRowCoaDataPkId().getCoaDataId());
        auditDtos.add(new ExtractedRowCoaDataAuditDto.Delete(
                tableId, rowId, CoaDataEntity.COA_ID_COL_NAME, coaId.toString()));

        logger.debugf("Creating bulk delete audit for table %s, row %s, COA ID %s", tableId, rowId, coaId);

        // Audit explainability deletion if it exists (avoid null->null audit)
        if (entity.getExplainability() != null) {
            auditDtos.add(new ExtractedRowCoaDataAuditDto.Delete(
                    tableId, rowId,
                    ExtractedTableRowCoaDataJoinEntity.EXPLAINABILITY_ID_COL_NAME,
                    entity.getExplainability().getId().toString()));
        }

        return auditDtos;
    }

    /**
     * Performs bulk deletion and validates the result.
     */
    private void performBulkDeletion(List<Integer> tableIds, int expectedDeleteCount) {
        logger.debugf("Performing bulk deletion for %d expected entities", expectedDeleteCount);

        long actualDeleteCount = extractedRowCoaDataRepository.deleteMappedCoaDataForTables(tableIds);

        if (actualDeleteCount != expectedDeleteCount) {
            ResponseException.throwResponseException(Response.Status.CONFLICT,
                    "Failed to delete COA mappings for tables " + tableIds + ". Expected: " + expectedDeleteCount
                            + ", Actual: " + actualDeleteCount);
        }

        logger.debugf("Successfully deleted %d COA mappings", actualDeleteCount);
    }

    /**
     * Finds COA data mapping for a specific table row.
     */
    public Optional<ExtractedTableRowCoaDataJoinEntity> findByRowOptional(Integer tableId, Integer rowId) {
        return extractedRowCoaDataRepository.findByRowOptional(tableId, rowId.shortValue());
    }

    /**
     * Finds all COA data mappings for the given table IDs.
     */
    public List<ExtractedTableRowCoaDataJoinEntity> findCoaDataMappingsForTable(List<Integer> tableIds) {
        return extractedRowCoaDataRepository.findByTableIds(tableIds);
    }

    /**
     * Gets table IDs that have COA mappings from the provided list.
     */
    public List<Integer> getTablesWithCoaMappingsInList(List<Integer> tableIds) {
        return extractedRowCoaDataRepository.findTableIdsWithCoaMappingsInList(tableIds);
    }

    // ========== Query Methods ==========

    /**
     * Duplicates COA data mappings from source tables to destination tables.
     */
    public void duplicateCoaDataMappings(Map<LayoutBlockEntity, LayoutBlockEntity> sourceToDestTableMap) {
        for (Map.Entry<LayoutBlockEntity, LayoutBlockEntity> entry : sourceToDestTableMap.entrySet()) {
            duplicateCoaDataMappingsForTable(entry.getKey(), entry.getValue());
        }
    }

    /**
     * Duplicates COA data mappings from a source table to a destination table.
     */
    @Transactional
    public List<ExtractedTableRowCoaDataJoinEntity> duplicateCoaDataMappingsForTable(
            LayoutBlockEntity sourceTable, LayoutBlockEntity destinationTable) {

        logger.debugf("Duplicating COA mappings from table %s to table %s",
                sourceTable.getBlockId(), destinationTable.getBlockId());

        // Get source mappings
        List<ExtractedTableRowCoaDataJoinEntity> sourceMappings =
                extractedRowCoaDataRepository.findByTableIds(List.of(sourceTable.getBlockId()));

        if (sourceMappings.isEmpty()) {
            logger.debugf("No COA mappings found in source table %s", sourceTable.getBlockId());
            return Collections.emptyList();
        }

        // Create row mapping structures
        Map<TableRowPkId, TableRowEntity> sourceRowMap = createRowPkIdMap(sourceTable);
        Map<Integer, TableRowEntity> destRowPositionMap = createRowPositionMap(destinationTable);

        // Create duplicated mappings
        List<ExtractedTableRowCoaDataJoinEntity> duplicatedMappings = createDuplicatedMappings(
                sourceMappings, sourceRowMap, destRowPositionMap);

        // Persist duplicated mappings
        extractedRowCoaDataRepository.saveRowCoaJoin(duplicatedMappings);

        logger.debugf("Successfully duplicated %d COA mappings to table %s",
                duplicatedMappings.size(), destinationTable.getBlockId());

        return duplicatedMappings;
    }

    /**
     * Creates a map from TableRowPkId to TableRowEntity for the given table.
     */
    private Map<TableRowPkId, TableRowEntity> createRowPkIdMap(LayoutBlockEntity table) {
        return table.getTableRows().stream()
                .collect(Collectors.toMap(TableRowEntity::getTableRowPkId, row -> row));
    }

    // ========== Duplication Methods ==========

    /**
     * Creates a map from row position to TableRowEntity for the given table.
     */
    private Map<Integer, TableRowEntity> createRowPositionMap(LayoutBlockEntity table) {
        return table.getTableRows().stream()
                .collect(Collectors.toMap(TableRowEntity::getPos, row -> row));
    }

    /**
     * Creates duplicated mappings by matching source rows to destination rows by position.
     */
    private List<ExtractedTableRowCoaDataJoinEntity> createDuplicatedMappings(
            List<ExtractedTableRowCoaDataJoinEntity> sourceMappings,
            Map<TableRowPkId, TableRowEntity> sourceRowMap,
            Map<Integer, TableRowEntity> destRowPositionMap) {

        return sourceMappings.stream()
                .map(sourceMapping -> createDuplicatedMapping(sourceMapping, sourceRowMap, destRowPositionMap))
                .filter(Objects::nonNull) // Filter out mappings where destination row wasn't found
                .toList();
    }

    /**
     * Creates a single duplicated mapping.
     */
    private ExtractedTableRowCoaDataJoinEntity createDuplicatedMapping(
            ExtractedTableRowCoaDataJoinEntity sourceMapping,
            Map<TableRowPkId, TableRowEntity> sourceRowMap,
            Map<Integer, TableRowEntity> destRowPositionMap) {

        TableRowPkId sourcePkId = sourceMapping.getExtractedTableRowCoaDataPkId().getTableRowPkId();
        TableRowEntity sourceRow = sourceRowMap.get(sourcePkId);

        if (sourceRow == null) {
            logger.warnf("Source row not found for mapping: %s", sourcePkId);
            return null;
        }

        TableRowEntity destRow = destRowPositionMap.get(sourceRow.getPos());
        if (destRow == null) {
            logger.warnf("Destination row not found for position: %s", sourceRow.getPos());
            return null;
        }

        return new ExtractedTableRowCoaDataJoinEntity(
                destRow.getTableRowPkId(),
                sourceMapping.getExtractedTableRowCoaDataPkId().getCoaDataId(),
                sourceMapping.getExplainability());
    }

    /**
     * Represents the type of operation to be performed on COA data mapping.
     */
    private enum OperationType {
        CREATE, UPDATE, DELETE, SKIP
    }
}
