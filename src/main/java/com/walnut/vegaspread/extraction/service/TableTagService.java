package com.walnut.vegaspread.extraction.service;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.common.model.extraction.TableTagDto;
import com.walnut.vegaspread.extraction.entity.TableTagEntity;
import com.walnut.vegaspread.extraction.repository.TableTagRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response;
import org.jboss.logging.Logger;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@ApplicationScoped
public class TableTagService {

    private static final Logger logger = Logger.getLogger(TableTagService.class);
    private final TableTagRepository tableTagRepository;

    public TableTagService(TableTagRepository tableTagRepository) {
        this.tableTagRepository = tableTagRepository;
    }

    @Transactional
    public List<TableTagEntity> createTableTag(List<String> tags, String createdBy) {

        if (createdBy == null) {
            ResponseException.throwResponseException(Response.Status.BAD_REQUEST,
                    "Created by cannot be null");
        }

        List<TableTagEntity> tableTagEntities = new ArrayList<>();
        for (String tag : tags) {
            TableTagEntity tableTagEntity = TableTagEntity.builder()
                    .tag(tag)
                    .createdBy(createdBy)
                    .createdTime(LocalDateTime.now())
                    .lastModifiedBy(createdBy)
                    .lastModifiedTime(LocalDateTime.now())
                    .build();

            tableTagEntities.add(tableTagEntity);
        }
        tableTagRepository.persist(tableTagEntities);
        return tableTagEntities;
    }

    @Transactional
    public List<TableTagEntity> updateTableTag(List<TableTagDto.Update> updateDtos, String username) {
        List<TableTagEntity> tableTagEntities = new ArrayList<>();
        for (TableTagDto.Update updateDto : updateDtos) {
            TableTagEntity tableTagEntity = tableTagRepository.findById(updateDto.id());
            if (tableTagEntity == null) {
                logger.errorf("Table tag with id %s not found", updateDto.id());
                continue;
            }
            tableTagEntity.setTag(updateDto.tag());
            tableTagEntity.setLastModifiedTime(LocalDateTime.now());
            tableTagEntity.setLastModifiedBy(username);
            tableTagEntities.add(tableTagEntity);
        }
        tableTagRepository.persist(tableTagEntities);
        return tableTagEntities;
    }

    @Transactional
    public long deleteTableTag(List<Integer> tagIds) {

        if (tagIds.stream().anyMatch(Objects::isNull)) {
            ResponseException.throwResponseException(Response.Status.BAD_REQUEST,
                    "Table tag ids cannot be null for deletion");
        }

        return tableTagRepository.deleteByIds(tagIds);
    }

    public List<TableTagEntity> getTableTags() {
        return tableTagRepository.listAll();
    }

    public List<TableTagEntity> getTableTagByIds(List<Integer> tagIds) {
        if (tagIds.isEmpty()) {
            return Collections.emptyList();
        }
        return tableTagRepository.findByIds(tagIds);
    }

    public TableTagEntity getTableTagById(Integer tagId) {
        return tableTagRepository.findById(tagId);
    }

    public List<TableTagEntity> findByTableTagLike(String tableType) {
        return tableTagRepository.list("tag LIKE ?1", "%" + tableType + "%");
    }

    public TableTagEntity findByTag(String tag) {
        return tableTagRepository.findByTag(tag);
    }
}
