package com.walnut.vegaspread.extraction.service.mapper;

import com.walnut.vegaspread.extraction.entity.CoaDataEntity;
import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.entity.SubtotalMappingEntity;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.model.ResponseDto;
import com.walnut.vegaspread.extraction.service.CoaDataService;
import com.walnut.vegaspread.extraction.service.ExtractedRowCoaDataService;
import com.walnut.vegaspread.extraction.service.SubtotalMappingService;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;

import java.util.Optional;

@ApplicationScoped
public class TableRowEntityMapper {
    private final ExtractedRowCoaDataService extractedRowCoaDataService;
    private final LayoutBlockEntityMapper layoutBlockEntityMapper;
    private final CoaDataService coaDataService;
    private final SubtotalMappingService subtotalMappingService;

    public TableRowEntityMapper(ExtractedRowCoaDataService extractedRowCoaDataService,
                                LayoutBlockEntityMapper layoutBlockEntityMapper, CoaDataService coaDataService,
                                SubtotalMappingService subtotalMappingService) {
        this.extractedRowCoaDataService = extractedRowCoaDataService;
        this.layoutBlockEntityMapper = layoutBlockEntityMapper;
        this.coaDataService = coaDataService;
        this.subtotalMappingService = subtotalMappingService;
    }

    @Transactional
    public ResponseDto.TableRow toDto(TableRowEntity row, Boolean isNtaBlock) {
        if (row == null) {
            return null;
        }
        Optional<ExtractedTableRowCoaDataJoinEntity> optExtractedTableRowCoaDataJoinEntity =
                extractedRowCoaDataService.findByRowOptional(
                        row.getTableRowPkId().getTableId(), row.getTableRowPkId().getRowId().intValue());
        CoaDataEntity coaDataEntity = optExtractedTableRowCoaDataJoinEntity.map(
                        extractedTableRowCoaDataJoinEntity -> coaDataService.findById(
                                extractedTableRowCoaDataJoinEntity.getExtractedTableRowCoaDataPkId().getCoaDataId()))
                .orElse(null);
        ResponseDto.LayoutBlock ntaTable = row.getNtaTable() == null || isNtaBlock ? null :
                layoutBlockEntityMapper.toDto(
                        row.getNtaTable(), Boolean.TRUE);

        Optional<SubtotalMappingEntity> subtotalMapping = subtotalMappingService.getSubtotalMappingForRow(
                row.getTableRowPkId());
        return optExtractedTableRowCoaDataJoinEntity.map(
                        coaDataJoinEntity -> new ResponseDto.TableRow(row.getTableRowPkId().getTableId(),
                                row.getTableRowPkId().getRowId(), coaDataEntity.getCoaId(), coaDataEntity.getUseCoa(),
                                coaDataEntity.getCoaScore(), row.getParentText(), row.getHeaderIds(),
                                row.getCellsText(),
                                row.getScore(), row.getComment(), row.getBbox().getXMin(), row.getBbox().getXMax(),
                                row.getBbox().getYMin(), row.getBbox().getYMax(), ntaTable,
                                Optional.ofNullable(coaDataJoinEntity.getExplainability())
                                        .map(CoaMappingEntity::getId)
                                        .orElse(null),
                                subtotalMapping.map(mappingEntity -> mappingEntity.getSubtotal().getId()).orElse(null),
                                subtotalMapping.map(
                                                subtotalMappingEntity -> subtotalMappingEntity.getSubtotal().getSubtotalName())
                                        .orElse(null))
                )
                .orElseGet(() ->
                        new ResponseDto.TableRow(row.getTableRowPkId().getTableId(), row.getTableRowPkId().getRowId(),
                                null, null, null, row.getParentText(), row.getHeaderIds(), row.getCellsText(),
                                row.getScore(), row.getComment(), row.getBbox().getXMin(), row.getBbox().getXMax(),
                                row.getBbox().getYMin(), row.getBbox().getYMax(), ntaTable, null,
                                subtotalMapping.map(mappingEntity -> mappingEntity.getSubtotal().getId()).orElse(null),
                                subtotalMapping.map(
                                                subtotalMappingEntity -> subtotalMappingEntity.getSubtotal().getSubtotalName())
                                        .orElse(null))
                );
    }
}
