package com.walnut.vegaspread.extraction.service;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.common.model.audit.extraction.LayoutBlockAuditDto;
import com.walnut.vegaspread.extraction.converter.LayoutBlockId;
import com.walnut.vegaspread.extraction.entity.Bbox;
import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.entity.TableHeaderEntity;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.entity.TableTagEntity;
import com.walnut.vegaspread.extraction.model.BlockTypeEnum;
import com.walnut.vegaspread.extraction.model.DbBlock;
import com.walnut.vegaspread.extraction.model.DbRow;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import com.walnut.vegaspread.extraction.repository.LayoutBlockRepository;
import com.walnut.vegaspread.extraction.utils.Config;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import jakarta.validation.ConstraintViolationException;
import jakarta.ws.rs.core.Response;
import org.jboss.logging.Logger;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

@ApplicationScoped
public class LayoutService {

    private static final Logger logger = Logger.getLogger(LayoutService.class);
    private final LayoutBlockRepository layoutBlockRepository;
    private final ExchangeService exchangeService;
    private final TableService tableService;
    private final ExtractedRowCoaDataService extractedRowCoaDataService;
    private final SubtotalMappingService subtotalMappingService;
    private final TableTagService tableTagService;

    public LayoutService(LayoutBlockRepository layoutBlockRepository,
                         ExchangeService exchangeService, TableService tableService,
                         ExtractedRowCoaDataService extractedRowCoaDataService,
                         SubtotalMappingService subtotalMappingService, TableTagService tableTagService) {
        this.layoutBlockRepository = layoutBlockRepository;
        this.exchangeService = exchangeService;
        this.tableService = tableService;
        this.extractedRowCoaDataService = extractedRowCoaDataService;
        this.subtotalMappingService = subtotalMappingService;
        this.tableTagService = tableTagService;
    }

    public LayoutBlockEntity getBlock(Integer blockId) {
        return layoutBlockRepository.findById(blockId);
    }

    public List<LayoutBlockEntity> getBlocks(List<Integer> blockIds) {
        return layoutBlockRepository.findAllByIds(blockIds);
    }

    @Transactional
    public List<LayoutBlockEntity> updateBlocks(List<DbBlock.BlockDto> blockDtos,
                                                boolean isApiKeyAuthenticated) {
        List<LayoutBlockEntity> blocks = new ArrayList<>();
        List<LayoutBlockAuditDto.Update> auditUpdateDtos = new ArrayList<>();
        Map<LayoutBlockEntity, List<BlockUpdateField>> blockUpdatedFields = new HashMap<>();

        List<Integer> blockIds = blockDtos.stream().map(DbBlock.BlockDto::getBlockId).distinct().toList();
        Map<Integer, LayoutBlockEntity> layoutBlockEntityMap = layoutBlockRepository.findAllByIds(blockIds)
                .stream()
                .collect(Collectors.toMap(LayoutBlockEntity::getBlockId,
                        Function.identity()));

        for (DbBlock.BlockDto dto : blockDtos) {

            LayoutBlockEntity block = layoutBlockEntityMap.get(dto.getBlockId());
            if (block == null) {
                ResponseException.throwResponseException(Response.Status.NOT_FOUND,
                        "Block not found for id " + dto.getBlockId());
            }

            LayoutBlockEntity existingBlock = new LayoutBlockEntity(block, false);

            for (LayoutService.BlockUpdateField field : LayoutService.BlockUpdateField.values()) {

                if (updateField(field, block, existingBlock, dto, auditUpdateDtos)) {
                    blockUpdatedFields.computeIfAbsent(block, k -> new ArrayList<>()).add(field);
                }
            }

            if (isApiKeyAuthenticated && dto.getTagExplainabilityId() != null) {

                auditUpdateDtos.add(new LayoutBlockAuditDto.Update(block.getBlockId(),
                        LayoutBlockEntity.TAG_EXPLAINABILITY_ID_COL_NAME,
                        existingBlock.getTagExplainabilityId().toString(),
                        dto.getTagExplainabilityId().toString()));

                block.setTagExplainabilityId(dto.getTagExplainabilityId());
            }
            blocks.add(block);
        }
        layoutBlockRepository.persist(blocks);
        updateBlocksWithNATag(isApiKeyAuthenticated, blockUpdatedFields);
        if (!auditUpdateDtos.isEmpty()) {
            exchangeService.auditUpdateBlocks(auditUpdateDtos, isApiKeyAuthenticated);
        }

        return blocks;
    }

    private void updateBlocksWithNATag(boolean isApiKeyAuthenticated,
                                       Map<LayoutBlockEntity, List<BlockUpdateField>> blockUpdatedFields) {
        //Get blocks with NA table tagId.
        List<LayoutBlockEntity> blockWithNATag = new ArrayList<>();
        blockUpdatedFields.forEach((block, updatedFields) -> {
            if (updatedFields.contains(BlockUpdateField.TAG) && block.getTag().getId().equals(Config.NA_TAG_ID)) {
                blockWithNATag.add(block);
            }
        });

        //Remove NTA table mapping for all rows for all blocks with NA tagId.
        List<DbRow.RowDto> updatedNtaRows = new ArrayList<>();
        blockWithNATag.forEach(block ->
                updatedNtaRows.addAll(
                        tableService.findByNtaTableId(block.getBlockId())
                                .stream()
                                .map(row -> DbRow.RowDto.builder()
                                        .tableId(row.getTableRowPkId().getTableId())
                                        .rowId(row.getTableRowPkId().getRowId())
                                        .ntaTableId(0)
                                        .build())
                                .toList())
        );
        if (!updatedNtaRows.isEmpty()) {
            tableService.createOrUpdateRows(updatedNtaRows, true, isApiKeyAuthenticated, false);
        }
        //Delete coa mappings for all blocks with NA tags.
        if (!blockWithNATag.isEmpty()) {
            extractedRowCoaDataService.deleteTableCoaDataMappings(
                    blockWithNATag.stream()
                            .map(LayoutBlockEntity::getBlockId)
                            .toList(),
                    isApiKeyAuthenticated);
        }
    }

    private void addAuditEntry(LayoutService.BlockUpdateField field, Integer blockId, Object currentValue,
                               Object newValue, List<LayoutBlockAuditDto.Update> auditChanges) {
        switch (field) {
            case BBOX:
                Bbox existingBbox = (Bbox) currentValue;
                Bbox dto = (Bbox) newValue;
                if (dto.getXMin() != null && dto.getYMin() != null && dto.getXMax() != null && dto.getYMax() != null) {
                    auditChanges.add(new LayoutBlockAuditDto.Update(blockId, Bbox.X_MIN_COL_NAME,
                            existingBbox.getXMin().toString(), dto.getXMin().toString()));
                    auditChanges.add(new LayoutBlockAuditDto.Update(blockId, Bbox.Y_MIN_COL_NAME,
                            existingBbox.getYMin().toString(), dto.getYMin().toString()));
                    auditChanges.add(new LayoutBlockAuditDto.Update(blockId, Bbox.X_MAX_COL_NAME,
                            existingBbox.getXMax().toString(), dto.getXMax().toString()));
                    auditChanges.add(new LayoutBlockAuditDto.Update(blockId, Bbox.Y_MAX_COL_NAME,
                            existingBbox.getYMax().toString(), dto.getYMax().toString()));
                }
                break;
            case TAG:
                auditChanges.add(new LayoutBlockAuditDto.Update(blockId, LayoutBlockEntity.TAG_COL_NAME,
                        currentValue.toString(), newValue.toString()));
                break;
            case COMMENT:
                auditChanges.add(new LayoutBlockAuditDto.Update(blockId, LayoutBlockEntity.COMMENT_COL_NAME,
                        currentValue.toString(), newValue.toString()));
                break;

            default:
                break;
        }
    }

    private boolean updateField(BlockUpdateField field, LayoutBlockEntity block, LayoutBlockEntity currentBlock,
                                DbBlock.BlockDto blockDto, List<LayoutBlockAuditDto.Update> auditChanges) {
        Object newValue = getNewValue(field, blockDto);
        Object currentValue = getCurrentValue(field, currentBlock);

        if (newValue != null && !newValue.equals(currentValue)) {
            updateValue(field, block, blockDto);
            addAuditEntry(field, block.getBlockId(), currentValue, newValue, auditChanges);
            return true;
        }
        return false;
    }

    private void updateValue(LayoutService.BlockUpdateField field, LayoutBlockEntity block, DbBlock.BlockDto blockDto) {
        switch (field) {
            case PAGE_NUM:
                block.setPageNum(blockDto.getPageNum().shortValue());
                break;
            case BLOCK_TYPE:
                block.setBlockType(blockDto.getBlockType());
                break;
            case BBOX:
                block.setBbox(new Bbox(blockDto.getXMin(), blockDto.getXMax(), blockDto.getYMin(), blockDto.getYMax()));
                break;
            case SCORE:
                block.setScore(blockDto.getScore().byteValue());
                break;
            case TAG:
                if (blockDto.getTagId() == null || tableTagService.getTableTagById(blockDto.getTagId()) == null) {
                    ResponseException.throwResponseException(Response.Status.NOT_FOUND,
                            "Invalid or non-existent tag id " + blockDto.getTagId());
                }
                block.setTag(tableTagService.getTableTagById(blockDto.getTagId()));
                break;
            case COMMENT:
                block.setComment(blockDto.getComment());
                break;
            default:
                break;
        }
    }

    private Object getCurrentValue(BlockUpdateField field, LayoutBlockEntity currentBlock) {
        return switch (field) {
            case PAGE_NUM -> currentBlock.getPageNum();
            case BLOCK_TYPE -> currentBlock.getBlockType();
            case BBOX -> currentBlock.getBbox();
            case SCORE -> currentBlock.getScore();
            case TAG -> currentBlock.getTag().getId();
            case COMMENT -> currentBlock.getComment();
        };
    }

    private Object getNewValue(BlockUpdateField field, DbBlock.BlockDto blockDto) {
        return switch (field) {
            case PAGE_NUM -> blockDto.getPageNum();
            case BLOCK_TYPE -> blockDto.getBlockType();
            case BBOX -> {
                if (blockDto.getXMin() != null && blockDto.getXMax() != null && blockDto.getYMin() != null && blockDto.getYMax() != null) {
                    yield new Bbox(blockDto.getXMin(), blockDto.getXMax(), blockDto.getYMin(), blockDto.getYMax());
                } else {
                    yield null;
                }
            }
            case SCORE -> blockDto.getScore() == null ? null : blockDto.getScore().byteValue();
            case TAG -> blockDto.getTagId();

            case COMMENT -> blockDto.getComment();
        };
    }

    @Transactional
    public List<LayoutBlockEntity> createBlocks(List<DbBlock.CreateBlockDto> blockDtos, UUID docId) {
        Map<Integer, TableTagEntity> tableTagEntityMap = tableTagService.getTableTags().stream().collect(
                Collectors.toMap(TableTagEntity::getId, tag -> tag));
        List<LayoutBlockEntity> blocks = new ArrayList<>();
        for (DbBlock.CreateBlockDto dto : blockDtos) {
            TableTagEntity tableTagEntity = tableTagEntityMap.get(dto.tagId());
            if (tableTagEntity == null) {
                ResponseException.throwResponseException(Response.Status.NOT_FOUND,
                        "Invalid table tag id " + dto.tagId());
            }
            LayoutBlockEntity block = LayoutBlockEntity.builder()
                    .docId(docId)
                    .tagExplainabilityId(Config.NA_TAG_EXPLAINABILITY_ID)
                    .pageNum(dto.pageNum().shortValue())
                    .blockType(dto.blockType())
                    .bbox(new Bbox(dto.xMin(), dto.xMax(), dto.yMin(), dto.yMax()))
                    .score(dto.score().byteValue())
                    .tag(tableTagEntity)
                    .comment(dto.comment())
                    .build();
            blocks.add(block);
        }
        layoutBlockRepository.persist(blocks);
        return blocks;
    }

    @Transactional
    public void deleteBlockById(Integer blockId) {
        try {
            layoutBlockRepository.deleteById(blockId);
        } catch (ConstraintViolationException ex) {
            logger.errorf(ex.getMessage());
        }
    }

    @Transactional
    public long deleteBlocksByIds(List<Integer> blockIds, boolean isApiKeyAuthenticated) {
        long deleteCount = 0;
        try {
            exchangeService.auditFirstAndLastBlocksForDelete(blockIds, isApiKeyAuthenticated);
            logger.debugf("%s rows deleted for blocks %s", tableService.deleteRows(blockIds, isApiKeyAuthenticated),
                    blockIds);
            logger.debugf("%s headers deleted for blocks %s", tableService.deleteHeaders(blockIds), blockIds);
            deleteCount = layoutBlockRepository.deleteAllByBlockIds(blockIds);
            if (deleteCount != blockIds.size()) {
                ResponseException.throwResponseException(Response.Status.CONFLICT,
                        "All blocks with ids " + blockIds + " could not be deleted.");
            }
        } catch (ConstraintViolationException cve) {
            logger.errorf(cve.getMessage());
        }
        return deleteCount;
    }

    @Transactional
    public long deleteBlocksByDocId(UUID docId, boolean isApiKeyAuthenticated) {
        long deleteCount = 0;
        try {
            deleteCount = deleteBlocksByIds(layoutBlockRepository.findAllBlockIdsByDocId(docId), isApiKeyAuthenticated);
        } catch (ConstraintViolationException cve) {
            logger.errorf(cve.getMessage());
        }
        return deleteCount;
    }

    public List<LayoutBlockEntity> listBlocksForDocId(UUID docId, BlockTypeEnum blockType) {
        return layoutBlockRepository.findAllByDocIdAndBlockType(docId, blockType);
    }

    public List<LayoutBlockEntity> listBlocksForDocIdAndPageNum(UUID docId, Integer pageNum, BlockTypeEnum blockType) {
        return layoutBlockRepository.findAllByDocIdAndBlockTypeAndPageNum(docId, blockType, pageNum);
    }

    public List<DbBlock.BlockTagOnly> getTags(UUID docId, BlockTypeEnum blockType) {
        return layoutBlockRepository.findBlocksWithTag(docId, blockType);
    }

    public List<Integer> findBlocksWithCoaId(UUID docId) {
        return layoutBlockRepository.findAllByDocIdWithCoaId(docId)
                .stream()
                .map(LayoutBlockId::getBlockId)
                .toList();
    }

    public List<LayoutBlockEntity> findAllBlocksInDocWithTag(UUID docId, String tag) {
        return layoutBlockRepository.findAllBlocksByDocIdAndTag(docId, tag);
    }

    public List<Integer> getBlockIdsForDoc(UUID docId) {
        return layoutBlockRepository.findAllBlockIdsByDocId(docId);
    }

    public List<LayoutBlockEntity> getTaggedTables(UUID docId) {
        return layoutBlockRepository.findAllTaggedTables(docId);
    }

    public void duplicateBlocks(UUID srcDocId, UUID destDocId) {
        List<LayoutBlockEntity> srcBlocks = layoutBlockRepository.findAllBlocksByDocId(srcDocId).stream().sorted(
                Comparator.comparingInt(LayoutBlockEntity::getBlockId)).toList();

        if (srcBlocks.isEmpty()) {
            logger.infof("No blocks found for docId %s", srcDocId);
            return;
        }

        Map<LayoutBlockEntity, LayoutBlockEntity> srcToDestBlockMap = new LinkedHashMap<>();
        srcBlocks.forEach(srcBlock -> {
            LayoutBlockEntity destBlock = duplicateBlock(srcBlock, destDocId);
            srcToDestBlockMap.put(srcBlock, destBlock);
        });
        updateNtaLinkForDuplicate(srcToDestBlockMap);
        extractedRowCoaDataService.duplicateCoaDataMappings(srcToDestBlockMap);
        subtotalMappingService.duplicateSubtotalMappings(srcToDestBlockMap, srcDocId, destDocId);
    }

    @Transactional
    public void updateNtaLinkForDuplicate(Map<LayoutBlockEntity, LayoutBlockEntity> srcToDestBlockMap) {

        List<TableRowEntity> ntaLikedRows = new ArrayList<>();

        srcToDestBlockMap.forEach((srcBlock, destBlock) -> {
            Map<Integer, TableRowEntity> destPosToRowMap = destBlock.getTableRows().stream()
                    .collect(Collectors.toMap(TableRowEntity::getPos, row -> row));
            srcBlock.getTableRows().stream()
                    .filter(row -> row.getNtaTable() != null)
                    .forEach(row -> {
                        TableRowPkId destTableRowPkId = destPosToRowMap.get(row.getPos()).getTableRowPkId();
                        TableRowEntity destRow = tableService.getRow(destTableRowPkId.getTableId(),
                                destTableRowPkId.getRowId().intValue()).orElse(null);
                        if (destRow == null) {
                            logger.errorf("Duplicated row not found for %s", destTableRowPkId);
                            return;
                        }
                        LayoutBlockEntity destNtaTable = srcToDestBlockMap.get(row.getNtaTable());
                        destRow.setNtaTable(destNtaTable);
                        ntaLikedRows.add(destRow);
                    });
        });
        tableService.saveRows(ntaLikedRows);
    }

    @Transactional
    public LayoutBlockEntity duplicateBlock(LayoutBlockEntity srcBlock, UUID destDocId) {

        LayoutBlockEntity destBlock = LayoutBlockEntity.builder()
                .docId(destDocId)
                .pageNum(srcBlock.getPageNum())
                .blockType(srcBlock.getBlockType())
                .bbox(srcBlock.getBbox())
                .score(srcBlock.getScore())
                .tag(srcBlock.getTag())
                .comment(srcBlock.getComment())
                .tagExplainabilityId(srcBlock.getTagExplainabilityId())
                .build();
        List<TableHeaderEntity> duplicatedHeaders = tableService.duplicateHeadersForTable(srcBlock, destBlock);
        List<TableRowEntity> duplicatedRows = tableService.duplicateRowsForTable(srcBlock, destBlock);
        destBlock.setTableHeaders(duplicatedHeaders);
        destBlock.setTableRows(duplicatedRows);
        layoutBlockRepository.persist(destBlock);
        return destBlock;
    }

    public enum BlockUpdateField {
        PAGE_NUM,
        BLOCK_TYPE,
        BBOX,
        SCORE,
        TAG,
        COMMENT
    }
}
