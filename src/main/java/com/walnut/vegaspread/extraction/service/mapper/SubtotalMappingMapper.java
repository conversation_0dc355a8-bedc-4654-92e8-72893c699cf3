package com.walnut.vegaspread.extraction.service.mapper;

import com.walnut.vegaspread.extraction.entity.SubtotalMappingEntity;
import com.walnut.vegaspread.extraction.model.SubtotalMappingDto;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

@ApplicationScoped
public class SubtotalMappingMapper {

    public SubtotalMappingDto.Response toDto(SubtotalMappingEntity subtotalMappingEntity) {
        return new SubtotalMappingDto.Response(subtotalMappingEntity.getDocId(),
                subtotalMappingEntity.getId().getTableId(),
                subtotalMappingEntity.getId().getRowId(), subtotalMappingEntity.getSubtotal().getId());
    }

    public List<SubtotalMappingDto.Response> toDtoList(
            List<SubtotalMappingEntity> subtotalMappingEntities) {
        return subtotalMappingEntities.stream()
                .map(this::toDto)
                .toList();
    }
}
