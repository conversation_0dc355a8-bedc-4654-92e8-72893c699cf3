package com.walnut.vegaspread.extraction.service;

import com.walnut.vegaspread.extraction.entity.CoaDataEntity;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.model.CoaDataDto;
import com.walnut.vegaspread.extraction.repository.CoaDataRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@ApplicationScoped
public class CoaDataService {
    private final CoaDataRepository coaDataRepository;

    public CoaDataService(CoaDataRepository coaDataRepository) {
        this.coaDataRepository = coaDataRepository;
    }

    @Transactional
    public CoaDataEntity addCoaData(CoaDataDto.Create coaDataCreateDto) {
        CoaDataEntity coaDataEntity = CoaDataEntity.builder()
                .coaId(coaDataCreateDto.coaId())
                .useCoa(coaDataCreateDto.useCoa())
                .coaScore(coaDataCreateDto.coaScore().byteValue())
                .build();
        coaDataRepository.persist(coaDataEntity);
        return coaDataEntity;
    }

    public Optional<CoaDataEntity> findByUseCoaAndCoaIdAndCoaScore(Boolean useCoa, Integer coaId, Integer coaScore) {
        return coaDataRepository.findByUseCoaAndCoaIdAndCoaScore(useCoa, coaId, coaScore.byteValue());
    }

    public boolean isUpdatedCoaData(ExtractedTableRowCoaDataJoinEntity entity, CoaDataDto.Create dto) {
        CoaDataEntity coaDataEntityForDto = findByUseCoaAndCoaIdAndCoaScore(dto.useCoa(), dto.coaId(),
                dto.coaScore()).orElse(null);
        return !(Objects.equals(entity.getExtractedTableRowCoaDataPkId().getCoaDataId(),
                Optional.ofNullable(coaDataEntityForDto).map(CoaDataEntity::getId).orElse(null)));
    }

    public CoaDataEntity findById(Integer coaDataId) {
        return coaDataRepository.findById(coaDataId);
    }

    public List<CoaDataEntity> getCoaDataByIds(List<Integer> coaDataIds) {
        return coaDataRepository.findByIds(coaDataIds);
    }
}
