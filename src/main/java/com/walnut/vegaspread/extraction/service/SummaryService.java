package com.walnut.vegaspread.extraction.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.walnut.vegaspread.common.exceptions.ExcelSaveException;
import com.walnut.vegaspread.common.exceptions.JSONDeserializationException;
import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.common.exceptions.WorkbookWriteException;
import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import com.walnut.vegaspread.common.model.extraction.DenominationEnum;
import com.walnut.vegaspread.common.model.extraction.DocData;
import com.walnut.vegaspread.common.model.extraction.UrlDto;
import com.walnut.vegaspread.common.model.workflow.StatusEnum;
import com.walnut.vegaspread.common.utils.Client;
import com.walnut.vegaspread.common.utils.Jwt;
import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.entity.SubtotalEntity;
import com.walnut.vegaspread.extraction.entity.SubtotalMappingEntity;
import com.walnut.vegaspread.extraction.entity.TableHeaderEntity;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.model.JSWSummaryOutput;
import com.walnut.vegaspread.extraction.model.Summary;
import com.walnut.vegaspread.extraction.model.SummaryInputDocDto;
import com.walnut.vegaspread.extraction.model.SummaryLevel;
import com.walnut.vegaspread.extraction.model.SummaryPreviewCell;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.validation.constraints.NotEmpty;
import jakarta.ws.rs.core.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.poi.ss.formula.BaseFormulaEvaluator;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.jboss.logging.Logger;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.walnut.vegaspread.common.utils.Constants.DEFAULT_CLIENT_NAME;
import static com.walnut.vegaspread.extraction.service.JSWClientOutputService.generateClientJSON;

@ApplicationScoped
public class SummaryService {
    public static final String CUSTOM_SUMMARY_SUBTOTAL_NAME_SEPARATOR = "__";
    public static final Map<String, Integer> excelYearRowNo = Map.of(Client.RCBC.getClientName(), 2,
            DEFAULT_CLIENT_NAME, 2, Client.JSW.getClientName(), 2);
    static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy",
            Locale.ENGLISH);
    private static final Logger logger = Logger.getLogger(SummaryService.class);
    private static final DateTimeFormatter FY_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yy", Locale.ENGLISH);
    private static final SummaryPreviewCell EMPTY_CELL = new SummaryPreviewCell("-", "#ffffff", "center");
    private static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat(DenominationEnum.NONE.getFormatString());
    private static final DecimalFormat SUMMARY_VALIDATION_DECIMAL_FORMAT = new DecimalFormat("#0.00");
    private static final int SHEET_END_EMPTY_ROW_COUNT = 10;
    private final LayoutService layoutService;
    private final JsonWebToken accessToken;
    private final ExchangeService exchangeService;
    private final SubtotalMappingService subtotalMappingService;
    private final TableService tableService;
    private final SubtotalService subtotalService;
    private final JSWClientOutputService jswClientOutputService;
    private final GroupByLvlCategory grouper = (summaryRows, level) -> {
        Map<String, Summary.SummaryRow> lvlRowMap = summaryRows.parallelStream()
                .filter(c -> getCategoryString(c, level) != null)
                .collect(Collectors.groupingBy(c -> getCategoryString(c, level),
                        Collectors.collectingAndThen(Collectors.toList(), children -> {
                            Summary.SummaryRow firstChild = children.get(0);
                            Summary.SummaryRow lvlRow =
                                    createEmptyRow(getCategoryString(firstChild, level), children.stream()
                                            .sorted(Comparator.comparing(c -> c.getCellsText().get(0)))
                                            .toList());
                            lvlRow.setCoaText(firstChild.getCoaText());

                            for (Summary.SummaryRow child : children) {
                                double cell1 = toDouble(child.getCellsText().get(1));
                                double cell2 = toDouble(child.getCellsText().get(2));
                                if (Boolean.FALSE.equals(child.getSign())) {
                                    cell1 = -Math.abs(cell1);
                                    cell2 = -Math.abs(cell2);
                                }
                                updateTotals(lvlRow, cell1, cell2);
                            }
                            return lvlRow;
                        })));

        List<Summary.SummaryRow> nullCategoryRows = summaryRows.parallelStream()
                .filter(c -> getCategoryString(c, level) == null)
                .toList();
        summaryRows.clear();
        summaryRows.addAll(nullCategoryRows);
        summaryRows.addAll(lvlRowMap.values().stream()
                .sorted(Comparator.comparing(c -> c.getCellsText().get(0)))
                .toList());
    };

    public SummaryService(LayoutService layoutService, JsonWebToken accessToken, ExchangeService exchangeService,
                          SubtotalMappingService subtotalMappingService, TableService tableService,
                          SubtotalService subtotalService, JSWClientOutputService jswClientOutputService) {
        this.layoutService = layoutService;
        this.accessToken = accessToken;
        this.exchangeService = exchangeService;
        this.subtotalMappingService = subtotalMappingService;
        this.tableService = tableService;
        this.subtotalService = subtotalService;
        this.jswClientOutputService = jswClientOutputService;
    }

    public static List<SummaryInputDocDto> moveOrAddCurrentDocToFirstIndex(
            List<SummaryInputDocDto> completedDocsForSummary,
            UUID currentDocId,
            SummaryInputDocDto currentDoc) {
        List<SummaryInputDocDto> sortedDocs = new ArrayList<>(completedDocsForSummary);
        // Try to find the document by docId
        for (int i = 0; i < sortedDocs.size(); i++) {
            if (sortedDocs.get(i).docId().equals(currentDocId)) {
                // If the document matches, move it to the first position
                SummaryInputDocDto matchedCurrentDoc = sortedDocs.remove(i);
                sortedDocs.add(0, matchedCurrentDoc);
                return sortedDocs;
            }
        }
        // If no match is found, add the new document at the first position
        sortedDocs.add(0, currentDoc);
        return sortedDocs;
    }

    public static String formatStringNumber(String input, DecimalFormat formatter) {
        try {
            Number number = new DecimalFormat().parse(input);
            return formatter.format(number);
        } catch (ParseException e) {
            logger.error(e);
            ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR,
                    "Invalid number format: " + input);
            return null;
        }
    }

    public static BigDecimal formatStringToDecimal(String input, DecimalFormat formatter) {
        try {
            Number number = new DecimalFormat().parse(input);
            return new BigDecimal(formatter.format(number));
        } catch (ParseException e) {
            logger.error(e);
            ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR,
                    "Invalid number format: " + input);
            return null;
        }
    }

    static String getValueTextFromPreviewText(DocData currentDocData, String previewText) {
        DecimalFormat decimalFormat = new DecimalFormat(
                currentDocData.outputDenomination().getPreviewFormatString());
        decimalFormat.setParseBigDecimal(true);
        try {
            BigDecimal previewNumber = (BigDecimal) decimalFormat.parse(previewText);
            BigDecimal factor = BigDecimal.valueOf(currentDocData.outputDenomination().getFactor());
            BigDecimal formattedPreviewNumber = formatStringToDecimal(
                    previewNumber.toPlainString(), SUMMARY_VALIDATION_DECIMAL_FORMAT);
            BigDecimal result = formattedPreviewNumber.multiply(factor);
            BigDecimal previewCellAmount = (result.setScale(formattedPreviewNumber.scale(),
                    RoundingMode.HALF_UP));
            return previewCellAmount.toPlainString();
        } catch (ParseException e) {
            logger.error(e);
            ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR,
                    "Failed to parse " + previewText + " using formatter" + decimalFormat.toPattern());
            return null;
        }
    }

    List<Integer> getExcelPeriodColumnIndexes(List<SummaryPreviewCell> previewPeriodRow,
                                              DocData currentDocData) {

        String reportingYear = currentDocData.period().format(DATE_TIME_FORMATTER);
        String prevYear = currentDocData.period().minusYears(1).format(DATE_TIME_FORMATTER);

        int reportingYearColIdx = -1; // Default value if not found
        int prevYearColIdx = -1;
        for (int i = 0; i < previewPeriodRow.size(); i++) {
            String currentColText = previewPeriodRow.get(i).getText();

            if (reportingYear.equals(currentColText) && reportingYearColIdx == -1) {
                reportingYearColIdx = i;
            }
            if (prevYear.equals(currentColText) && prevYearColIdx == -1) {
                prevYearColIdx = i;
            }

            if (reportingYearColIdx != -1 && prevYearColIdx != -1) {
                break;
            }
        }
        return new ArrayList<>(List.of(reportingYearColIdx, prevYearColIdx));
    }

    private List<Summary.SummaryHeader> getSummaryHeaders(DocData docData) {
        return List.of(
                new Summary.SummaryHeader(0, "COA", Boolean.FALSE),
                new Summary.SummaryHeader(1, docData.period().format(DATE_TIME_FORMATTER) + "(FY" + docData.period()
                        .format(FY_DATE_TIME_FORMATTER) + ")", Boolean.FALSE),
                new Summary.SummaryHeader(2,
                        docData.period().minusYears(1).format(DATE_TIME_FORMATTER) + "(FY" + docData.period()
                                .format(FY_DATE_TIME_FORMATTER) + ")", Boolean.FALSE)
        );
    }

    private String getClientName(String clientName) {
        if (clientName != null) {
            return clientName.toLowerCase();
        }
        return Jwt.getClientName(accessToken);
    }

    private double formatValue(String value) {
        String text = value.replaceAll("[^\\x00-\\x7F]", "");
        text = text.replaceAll("\\s\\s+", " ");
        String charsToRemove = "$,)% ";
        String translateTable = text.replace("(", "-").replaceAll("[" + charsToRemove + "]", "");

        try {
            return Double.parseDouble(translateTable.trim());
        } catch (NumberFormatException e) {
            if (!value.isEmpty() && !value.equals("-")) {
                logger.errorf("Error in formatting value: %s", value);
            }
            return 0;
        }
    }

    private Summary.SummaryRow createEmptyRow(String text, List<Summary.SummaryRow> children) {
        Summary.SummaryRow firstChild = children.get(0);
        return Summary.SummaryRow.builder()
                .rowId(-1)
                .coaId(-1)
                .coaScore(-1)
                .category(firstChild.getCategory())
                .cellsText(new ArrayList<>(List.of(text, "0", "0")))
                .tableId(-1)
                .pageNum(-1)
                .xMin(-1)
                .xMax(-1)
                .yMin(-1)
                .yMax(-1)
                .children(new ArrayList<>(children))
                .build();
    }

    private double toDouble(String value) {
        return Double.parseDouble(value.replace(",", ""));
    }

    private String toString(double value, DecimalFormat decimalFormat) {
        return decimalFormat.format(value);
    }

    private String toString(double value) {
        return DECIMAL_FORMAT.format(value);
    }

    private void updateTotals(Summary.SummaryRow row, double cell1, double cell2) {
        ArrayList<String> data = row.getCellsText();
        data.set(1, toString(toDouble(data.get(1)) + cell1));
        data.set(2, toString(toDouble(data.get(2)) + cell2));
        row.setCellsText(data);
    }

    private void updateWorkbook(Workbook workbook, List<Summary.SummaryHeader> headers,
                                Map<String, Summary.SummaryRow> coaLvlMaps) {
        Sheet sheet = workbook.getSheet("COA");
        CellStyle cellStyle = workbook.createCellStyle();
        CreationHelper createHelper = workbook.getCreationHelper();
        cellStyle.setDataFormat(createHelper.createDataFormat().getFormat("d/m/yy"));

        for (Row row : sheet) {
            String coaText = row.getCell(0).getStringCellValue();
            if (row.getRowNum() == 0) {
                for (int i = 0; i < headers.size(); i++) {
                    Cell cell = row.getCell(i);
                    if (i == 0) {
                        cell.setCellValue(headers.get(i).text());
                    } else {
                        String headerText = headers.get(i).text();
                        logger.debug("Header Text is: " + headerText);
                        cell.setCellValue(
                                LocalDate.parse(headerText.substring(0, headerText.indexOf("(")), DATE_TIME_FORMATTER));
                    }
                }
            } else if (coaLvlMaps.containsKey(coaText)) {
                List<String> data = coaLvlMaps.get(coaText).getCellsText();
                for (int i = 1; i < data.size(); i++) {
                    row.getCell(i).setCellValue(toDouble(data.get(i)));
                }
            }
        }
    }

    private Workbook saveExcel(DocData docData, String clientName) {
        String coaClientName = Jwt.getClientName(accessToken);
        clientName = getClientName(clientName);
        logger.debug("Save excel for client " + clientName);
        List<CoaItemDto> coaList = exchangeService.getCoaList(coaClientName);
        List<Summary.SummaryRow> coaLvlSummary = buildRowLvlSummary(docData, coaList, Boolean.TRUE);
        this.grouper.group(coaLvlSummary, SummaryLevel.COA);
        List<Summary.SummaryHeader> headers = getSummaryHeaders(docData);
        Map<String, Summary.SummaryRow> coaLvlMaps = coaLvlSummary.stream()
                .collect(Collectors.toMap(Summary.SummaryRow::getCoaText, c -> c));
        logger.debug(
                "Generating excel template url for client " + clientName + " and coa client " + coaClientName);
        UrlDto excelTemplateLink = exchangeService.getExcelTemplate(clientName, coaClientName);
        logger.debug("Writing to excel template at " + excelTemplateLink.toString());
        try (BufferedInputStream in = new BufferedInputStream(excelTemplateLink.url().openStream());
             ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            logger.debug("Starting to write to data buffer");
            byte[] dataBuffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = in.read(dataBuffer, 0, 1024)) != -1) {
                byteArrayOutputStream.write(dataBuffer, 0, bytesRead);
            }
            logger.debug("Getting workbook to write");
            Workbook workbook = new XSSFWorkbook(new ByteArrayInputStream(byteArrayOutputStream.toByteArray()));
            logger.debug("Writing to workbook");
            updateWorkbook(workbook, headers, coaLvlMaps);
            logger.debug("Resetting output stream");
            byteArrayOutputStream.reset();
            logger.debug("Removing formulas");
            BaseFormulaEvaluator.evaluateAllFormulaCells(workbook);
            logger.debug("Returning workbook");
            return workbook;
        } catch (IOException e) {
            logger.errorf("Error in saving excel: {}", e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    private HashMap<Integer, LocalDate> getHeaderId2Date(LayoutBlockEntity block) {
        HashMap<Integer, LocalDate> headerId2Date = new HashMap<>();
        List<TableHeaderEntity> headers = block.getTableHeaders();
        headers.sort(Comparator.comparingInt(TableHeaderEntity::getPos));
        for (int i = 0; i < headers.size(); i++) {
            TableHeaderEntity th = headers.get(i);
            try {
                LocalDate thDate = LocalDate.parse(th.getText(), DATE_TIME_FORMATTER);
                headerId2Date.put(i, thDate);
            } catch (Exception e) {
                logger.debugf("Error in parsing date: {}", th.getText());
            }
        }
        return headerId2Date;
    }

    private List<Integer> getHeaderIds(HashMap<Integer, LocalDate> headerId2Date, LayoutBlockEntity block,
                                       DocData docData) {
        List<Integer> headerIds = headerId2Date.entrySet().stream()
                .filter(entry -> entry.getValue().equals(docData.period()) || entry.getValue()
                        .equals(docData.period().minusYears(1)))
                .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                .map(Map.Entry::getKey)
                .toList();

        // Check for user header Ids
        TableRowEntity firstRow = block.getTableRows().get(0);
        List<Integer> fyHeaders = firstRow.getHeaderIds().stream().sorted().toList();
        if (headerIds.isEmpty() && fyHeaders.isEmpty()) {
            return Collections.emptyList();
        }
        if (fyHeaders.isEmpty()) {
            fyHeaders = headerIds;
        }
        return fyHeaders;
    }

    private double[] getCells(TableRowEntity tr, List<Integer> fyHeaders, HashMap<Integer, LocalDate> headerId2Date,
                              DocData docData, Boolean forExcel) {
        double factor = 1;
        if (Boolean.TRUE.equals(forExcel)) {
            factor = docData.fileDenomination().getFactor() / docData.outputDenomination().getFactor();
        }
        double cell1 = formatValue(tr.getCellsText().get(fyHeaders.get(0)));
        double cell2 = 0;

        // If there are two columns, then dates are already sorted and cell1 will be reporting year and
        // cell2 will be previous year
        if (fyHeaders.size() > 1) {
            cell2 = formatValue(tr.getCellsText().get(fyHeaders.get(1)));
        } else {
            // If there is only one column, we need to assign the cell to the correct year
            if (headerId2Date.get(fyHeaders.get(0)).equals(docData.period().minusYears(1))) {
                cell2 = cell1;
                cell1 = 0;
            }
        }
        cell1 *= factor;
        cell2 *= factor;
        return new double[]{cell1, cell2};
    }

    private List<Summary.SummaryRow> buildRowLvlSummary(DocData docData, List<CoaItemDto> coaList, Boolean forExcel) {
        Map<Integer, CoaItemDto> coaItemMap = coaList.stream()
                .collect(Collectors.toMap(CoaItemDto::coaId, Function.identity()));
        List<Integer> blockIds = layoutService.findBlocksWithCoaId(docData.docId());
        List<LayoutBlockEntity> blocks = layoutService.getBlocks(blockIds);

        List<Summary.SummaryRow> summaryRows = new ArrayList<>();
        for (LayoutBlockEntity block : blocks) {
            // Parse date for each column header and create a map of header id to date
            HashMap<Integer, LocalDate> headerId2Date = getHeaderId2Date(block);
            List<Integer> fyHeaders = getHeaderIds(headerId2Date, block, docData);
            if (fyHeaders.isEmpty()) {
                continue;
            }

            for (TableRowEntity tr : block.getTableRows()) {
                if (tr.getCoaData() == null || !coaItemMap.containsKey(tr.getCoaData().getCoaId())) {
                    continue;
                }
                double[] cells = getCells(tr, fyHeaders, headerId2Date, docData, forExcel);

                Integer coaId = tr.getCoaData().getCoaId();
                Summary.SummaryRow row = Summary.SummaryRow.builder()
                        .rowId(Integer.valueOf(tr.getTableRowPkId().getRowId()))
                        .coaId(coaId)
                        .coaText(coaItemMap.get(coaId).coaText())
                        .coaScore(Integer.valueOf(tr.getCoaData().getCoaScore()))
                        .cellsText(new ArrayList<>(
                                List.of(tr.getCellsText().get(0), toString(cells[0]), toString(cells[1]))))
                        .category(coaItemMap.get(coaId).lvl1Category())
                        .tableId(block.getBlockId())
                        .tableTag(block.getTag().getTag())
                        .pageNum(Integer.valueOf(block.getPageNum()))
                        .xMin(Integer.valueOf(tr.getBbox().getXMin()))
                        .xMax(Integer.valueOf(tr.getBbox().getXMax()))
                        .yMin(Integer.valueOf(tr.getBbox().getYMin()))
                        .yMax(Integer.valueOf(tr.getBbox().getYMax()))
                        .sign(coaItemMap.get(coaId).sign())
                        .children(new ArrayList<>())
                        .build();

                summaryRows.add(row);
            }
        }
        return summaryRows;
    }

    private String getCategoryString(Summary.SummaryRow row, SummaryLevel level) {
        return switch (level) {
            case LEVEL1 -> {
                String category = row.getCategory();
                yield category.contains(".") ? category.split("\\.")[0] : category;
            }
            case LEVEL2 -> {
                String category2 = row.getCategory();
                yield category2.contains(".") ? category2.split("\\.")[1] : null;
            }
            case TABLE -> row.getTableTag();
            case COA -> row.getCoaText();
        };
    }

    private void groupByLvlCategory(Summary.SummaryRow summaryRow, SummaryLevel catLevel, GroupByLvlCategory grouper) {
        if (summaryRow == null) {
            return;
        }
        Summary.SummaryRow firstChild = summaryRow.getChildren().get(0);
        if (firstChild.getChildren().isEmpty()) {
            grouper.group(summaryRow.getChildren(), catLevel);
        } else {
            summaryRow.getChildren().forEach(child -> groupByLvlCategory(child, catLevel, grouper));
        }
    }

    private void updateNetIncome(Summary.SummaryRow incomeStatementSummary) {
        if (incomeStatementSummary == null) {
            return;
        }
        Map<String, double[]> lvl1Amounts = incomeStatementSummary.getChildren().stream()
                .filter(c -> getCategoryString(c, SummaryLevel.LEVEL2) != null)
                .collect(Collectors.toMap(
                        c -> getCategoryString(c, SummaryLevel.LEVEL2),
                        c -> new double[]{
                                Math.abs(toDouble(c.getCellsText().get(1))),
                                Math.abs(toDouble(c.getCellsText().get(2)))
                        }
                ));

        List<String> additionKeys = List.of("Net Sales", "Other Income", "Interest Income", "Other Income (Expense)",
                "Extraordinary");
        List<String> subtractionKeys = List.of("COS", "Operating Expense", "Interest Expense", "Tax");

        double[] additionAmounts = additionKeys.stream()
                .map(k -> lvl1Amounts.getOrDefault(k, new double[2]))
                .reduce(new double[2], (a, b) -> new double[]{a[0] + b[0], a[1] + b[1]});

        double[] subtractionAmounts = subtractionKeys.stream()
                .map(k -> lvl1Amounts.getOrDefault(k, new double[2]))
                .reduce(new double[2], (a, b) -> new double[]{a[0] + b[0], a[1] + b[1]});

        double[] netIncome = new double[]{additionAmounts[0] - subtractionAmounts[0],
                additionAmounts[1] - subtractionAmounts[1]};

        incomeStatementSummary.setCellsText(new ArrayList<>(List.of("Net Income", toString(netIncome[0]),
                toString(netIncome[1]))));
    }

    public Summary.SummaryOutput buildSummary(UUID docId, String clientName) {
        DocData docData = exchangeService.getDocument(docId);
        clientName = getClientName(clientName);
        List<CoaItemDto> coaList = exchangeService.getCoaList(clientName);
        List<Summary.SummaryRow> summaryRows = buildRowLvlSummary(docData, coaList, Boolean.FALSE);
        this.grouper.group(summaryRows, SummaryLevel.LEVEL1);
        summaryRows.parallelStream().forEach(r -> groupByLvlCategory(r, SummaryLevel.LEVEL2, this.grouper));
        summaryRows.parallelStream().forEach(r -> groupByLvlCategory(r, SummaryLevel.TABLE, this.grouper));
        summaryRows.parallelStream().forEach(r -> groupByLvlCategory(r, SummaryLevel.COA, this.grouper));

        summaryRows.stream().filter(r -> r.getCellsText().get(0).startsWith("Income Statement"))
                .findFirst().ifPresent(this::updateNetIncome);
        List<Summary.SummaryHeader> headers = getSummaryHeaders(docData);
        return new Summary.SummaryOutput(headers, summaryRows);
    }

    public byte[] downloadSummaryExcel(UUID docId, String clientName) throws WorkbookWriteException {
        DocData docData = exchangeService.getDocument(docId);
        Workbook workbook = saveExcel(docData, clientName);
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            Objects.requireNonNull(workbook).write(byteArrayOutputStream);
            return byteArrayOutputStream.toByteArray();
        } catch (IOException e) {
            logger.error(e);
            ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR,
                    "Failed to write workbook to byte array");
            return null;
        }
    }

    private ImmutablePair<String, CellType> getCellValue(Cell cell, DecimalFormat decimalFormat) {
        String cellValue = EMPTY_CELL.getText();
        CellType cellType = CellType.STRING;
        switch (cell.getCellType()) {
            case STRING:
                cellValue = cell.getStringCellValue();
                break;
            case _NONE, BLANK:
                cellType = CellType.BLANK;
                break;
            case NUMERIC:
                if (isDateCell(cell)) {
                    cellValue = cell.getDateCellValue()
                            .toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDate()
                            .format(DATE_TIME_FORMATTER);
                } else {
                    cellValue = toString(cell.getNumericCellValue(), decimalFormat);
                    cellType = CellType.NUMERIC;
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    try {
                        if (isDateCell(cell)) {
                            cellValue = cell.getDateCellValue()
                                    .toInstant()
                                    .atZone(ZoneId.systemDefault())
                                    .toLocalDate()
                                    .format(DATE_TIME_FORMATTER);
                        } else {
                            cellValue = toString(cell.getNumericCellValue(), decimalFormat);
                            cellType = CellType.NUMERIC;
                        }
                    } catch (IllegalStateException e1) {
                        cellValue = "Formula Error";
                    }
                }
                break;
            case BOOLEAN:
                cellValue = String.valueOf(cell.getBooleanCellValue());
                break;
            case ERROR:
                cellValue = String.valueOf(cell.getErrorCellValue());
                break;
        }
        return ImmutablePair.of(cellValue, cellType);
    }

    private boolean isDateCell(Cell cell) {
        if (cell == null) {
            return false;
        }
        try {
            short formatIndex = cell.getCellStyle().getDataFormat();
            String formatString = cell.getCellStyle().getDataFormatString();
            return cell.getDateCellValue() != null &&
                    (DateUtil.isADateFormat(formatIndex, formatString) || formatString.contains("yyyy"));
        } catch (IllegalStateException e) {
            return false;
        }
    }

    private String getCellBgColor(Cell cell) {
        XSSFColor fgColor = (XSSFColor) cell.getCellStyle().getFillForegroundColorColor();
        if (fgColor == null) {
            return EMPTY_CELL.getBgColor();
        }
        byte[] rgb = fgColor.getRGB();
        return String.format("#%02x%02x%02x", rgb[0] & 0xff, rgb[1] & 0xff, rgb[2] & 0xff);
    }

    private String getCellAlign(Cell cell, CellType cellType) {
        HorizontalAlignment alignment = cell.getCellStyle().getAlignment();
        return switch (alignment) {
            case FILL, DISTRIBUTED, JUSTIFY -> "left";
            case CENTER, LEFT, RIGHT -> alignment.name().toLowerCase();
            case CENTER_SELECTION -> EMPTY_CELL.getAlign();
            case GENERAL -> switch (cellType) {
                case NUMERIC -> "right";
                case BLANK -> EMPTY_CELL.getAlign();
                default -> "left";
            };
        };
    }

    private SummaryPreviewCell getCell(Cell cell, DecimalFormat decimalFormat) {
        if (cell == null) {
            return EMPTY_CELL;
        }
        ImmutablePair<String, CellType> cellValue = getCellValue(cell, decimalFormat);
        return new SummaryPreviewCell(cellValue.getLeft(), getCellBgColor(cell),
                getCellAlign(cell, cellValue.getRight()));
    }

    public List<List<SummaryPreviewCell>> generateSummaryPreview(UUID docId, String clientName,
                                                                 String sheetName) throws ExcelSaveException {
        clientName = getClientName(clientName);
        DocData docData = exchangeService.getDocument(docId);
        DecimalFormat decimalFormat = new DecimalFormat(docData.outputDenomination().getPreviewFormatString());
        logger.debug("Saving excel for doc data " + docData + " and client " + clientName);
        try (Workbook workbook = saveExcel(docData, clientName)) {
            int consecutiveEmptyRows = 0;
            logger.debug("Workbook created");
            assert workbook != null;
            logger.debug("Workbook is non null");
            logger.debug("Get sheet for client " + clientName + " with name " + sheetName);
            Sheet summarySheet = workbook.getSheet(sheetName);

            int colRange = getColRange(clientName, summarySheet, decimalFormat, docData);

            List<List<SummaryPreviewCell>> summaryData = new ArrayList<>();
            for (Row row : summarySheet) {
                List<SummaryPreviewCell> rowList = new ArrayList<>();
                for (int col_id = 0; col_id <= colRange; col_id++) {
                    rowList.add(getCell(row.getCell(col_id), decimalFormat));
                }
                // Check if all values are empty
                if (!rowList.stream().allMatch(c -> c.getText().equals(EMPTY_CELL.getText()))) {
                    String nonWhiteColor = rowList.stream()
                            .map(SummaryPreviewCell::getBgColor)
                            .filter(color -> !color.equals(EMPTY_CELL.getBgColor()))
                            .findFirst()
                            .orElse(EMPTY_CELL.getBgColor());
                    rowList.add(0, new SummaryPreviewCell(String.valueOf(row.getRowNum() + 1), nonWhiteColor,
                            EMPTY_CELL.getAlign()));
                    rowList.stream().skip(1).forEach(c -> c.setBgColor(nonWhiteColor));
                    summaryData.add(rowList);
                    consecutiveEmptyRows = 0;
                } else {
                    consecutiveEmptyRows++;
                }
                if (consecutiveEmptyRows == SHEET_END_EMPTY_ROW_COUNT) {
                    break;
                }
            }
            return summaryData;
        } catch (IOException e) {
            logger.error(e);
            ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR,
                    "Failed to save workbook for client " + clientName);
            return null;
        }
    }

    private int getColRange(String clientName, Sheet summarySheet, DecimalFormat decimalFormat, DocData docData) {
        int headerRowId = excelYearRowNo.getOrDefault(clientName, excelYearRowNo.get(DEFAULT_CLIENT_NAME)) - 1;
        Row headerRow = summarySheet.getRow(headerRowId);
        if (headerRow == null) {
            headerRow = getFirstNonEmptyRow(summarySheet, headerRowId);
        }
        int startIndex = -1;
        int colRange = -1;
        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            Cell cell = headerRow.getCell(i);
            String cellText = getCell(cell, decimalFormat).getText();
            if (isDateCell(cell) && !cellText.equals(EMPTY_CELL.getText()) &&
                    (LocalDate.parse(cellText, DATE_TIME_FORMATTER).equals(docData.period()) ||
                            LocalDate.parse(cellText, DATE_TIME_FORMATTER).isBefore(docData.period()))) {
                // Mark the start index when the first date is found
                if (startIndex == -1) {
                    startIndex = i;
                }
                // Update the end index with the current consecutive date
                colRange = i;
            } else {
                // If we encounter a non-date after a series of consecutive dates, break out
                if (startIndex != -1) {
                    break;
                }
            }
        }
        return colRange;
    }

    private Row getFirstNonEmptyRow(Sheet summarySheet, int headerRowId) {
        for (int i = headerRowId + 1; i < summarySheet.getLastRowNum(); i++) {
            Row row = summarySheet.getRow(i);
            if (row == null) {
                continue;
            }
            boolean allBlank = true;
            for (Cell cell : row) {
                if (cell != null && cell.getCellType() != CellType.BLANK) {
                    allBlank = false;
                    break;
                }
            }
            if (!allBlank) {
                return row;
            }
        }

        ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR,
                "Failed to find header row in summary sheet");
        return null;
    }

    public Summary.SummaryOutput buildSummaryForDocSpread(List<SummaryInputDocDto> docsForSpread, String clientName,
                                                          UUID currentDocId,
                                                          String sheetName) throws ExcelSaveException {
        //Get DTO for current doc.
        SummaryInputDocDto currentDoc = docsForSpread.stream()
                .filter(docForSpread -> docForSpread.docId().equals(currentDocId))
                .findFirst()
                .orElse(null);
        if (currentDoc == null) {
            ResponseException.throwResponseException(Response.Status.BAD_REQUEST,
                    "Missing current document data for id " + currentDocId + " in input for summary");
            return null;
        }
        // Filter list for summary to only contain completed docs.
        List<SummaryInputDocDto> completedDocsForSummary = new ArrayList<>(docsForSpread.stream()
                .filter(docForSpread -> docForSpread.status().equals(StatusEnum.COMPLETED))
                .toList());

        logger.debug(
                "Merging summaries for docs " + completedDocsForSummary.stream()
                        .map(SummaryInputDocDto::docId)
                        .toList());
        //Sort by period.
        List<SummaryInputDocDto> sortedCompletedDocs = completedDocsForSummary.stream()
                .sorted(Comparator.comparing(SummaryInputDocDto::period).reversed()).toList();

        //Move current doc to the top of the list.
        sortedCompletedDocs = moveOrAddCurrentDocToFirstIndex(sortedCompletedDocs, currentDocId, currentDoc);

        List<Summary.SummaryOutput> summaryForDocs = sortedCompletedDocs.stream()
                .map(docForSpread -> buildSummary(docForSpread.docId(), clientName))
                .toList();
        List<Summary.SummaryOutput> processedSummaryForDoc = new ArrayList<>();
        //Merge same coa's at leaf level for individual summaries.
        for (Summary.SummaryOutput currentSummary : summaryForDocs) {
            processedSummaryForDoc.add(
                    new Summary.SummaryOutput(currentSummary.headers(), processSummaryRows(currentSummary.rows())));
        }

        Summary.SummaryOutput mergedSummaries = SummaryRowMergeUtility.mergeSummariesForDocs(processedSummaryForDoc);

        //Reset row data for leaf coa that do not have data for current year.
        for (Summary.SummaryRow row : mergedSummaries.rows()) {
            resetLeafRowsForCurrentSummary(row, summaryForDocs.get(0).headers().size() - 1);
        }

        checkSubtotalValidity(getClientName(clientName), currentDocId, mergedSummaries, sheetName);

        return mergedSummaries;
    }

    // Method to reset the leaf row if it contains 0 values for current year.
    private void resetLeafRowsForCurrentSummary(Summary.SummaryRow row, int len) {
        //Check for leaf rows at this level.
        if (row.getChildren() == null || row.getChildren().isEmpty()) {
            List<String> cellsText = row.getCellsText();
            if (cellsText.size() > 1 && len > 1) {
                int endIndex = Math.min(len + 1, cellsText.size());
                // If all cells for current year are zero, reset row.
                if (cellsText.subList(1, endIndex)
                        .stream()
                        .allMatch(cell -> toDouble(cell) == 0)) {
                    resetRow(row);
                }
            }
        } else {
            //Process children for all non-leaf rows at this level.
            for (Summary.SummaryRow child : row.getChildren()) {
                resetLeafRowsForCurrentSummary(child, len);
            }
        }
    }

    private void resetRow(Summary.SummaryRow row) {
        row.setRowId(-1);
        row.setCoaId(-1);
        row.setCoaScore(-1);
        row.setCategory(StringUtils.EMPTY);
        row.setTableId(-1);
        row.setTableTag(StringUtils.EMPTY);
        row.setPageNum(-1);
        row.setXMin(-1);
        row.setXMax(-1);
        row.setYMin(-1);
        row.setYMax(-1);
        row.setChildren(new ArrayList<>());
        row.setIsValid(null);
        row.setSign(null);
    }

    // Method to recursively traverse and process rows, merging leaf-level rows with the same parent
    public List<Summary.SummaryRow> processSummaryRows(List<Summary.SummaryRow> rows) {
        // Process leaf-level rows at this level
        List<Summary.SummaryRow> mergedRows = mergeLeafRows(rows);

        // Process any children recursively
        for (Summary.SummaryRow row : mergedRows) {
            if (row.getChildren() != null && !row.getChildren().isEmpty()) {
                row.setChildren((ArrayList<Summary.SummaryRow>) processSummaryRows(
                        row.getChildren())); // Process children recursively
            }
        }

        return mergedRows;
    }

    // Method to merge leaf-level rows with the same parent and same cellsText.get(0)
    private List<Summary.SummaryRow> mergeLeafRows(List<Summary.SummaryRow> rows) {
        Map<String, List<Summary.SummaryRow>> groupedRows = new HashMap<>();

        // Group leaf rows by the first element of cellsText
        for (Summary.SummaryRow row : rows) {
            // Only merge leaf rows (rows with no children)
            if (row.getChildren() == null || row.getChildren().isEmpty()) {
                String key = row.getCellsText().get(0);  // Use the first element of cellsText as the key
                groupedRows.computeIfAbsent(key, k -> new ArrayList<>()).add(row);
            } else {
                // If the row is not a leaf, just add it to the result as-is
                groupedRows.computeIfAbsent("non-leaf", k -> new ArrayList<>()).add(row);
            }
        }

        List<Summary.SummaryRow> mergedRows = new ArrayList<>();

        // Iterate over the groups and merge leaf rows if there are more than one in the group
        for (Map.Entry<String, List<Summary.SummaryRow>> entry : groupedRows.entrySet()) {
            List<Summary.SummaryRow> group = entry.getValue();

            if (group.size() == 1) {
                // No merge needed if only one row in this group (leaf or non-leaf)
                mergedRows.add(group.get(0));
            } else if (!entry.getKey().equals("non-leaf")) {
                // Merge the rows in the group if it's a group of leaf rows with the same cellsText.get(0)
                Summary.SummaryRow mergedRow = mergeRowCellsText(group);
                mergedRows.add(mergedRow);
            } else {
                // Add non-leaf rows as they are
                mergedRows.addAll(group);
            }
        }

        return mergedRows;
    }

    // Method to merge rows into one for leaf rows with the same cellsText.get(0)
    private Summary.SummaryRow mergeRowCellsText(List<Summary.SummaryRow> rows) {
        // Start with the first row's data
        Summary.SummaryRow firstRow = rows.get(0);

        // Merge cellsText.
        ArrayList<String> mergedCellsText = new ArrayList<>(firstRow.getCellsText());

        // Iterate over the remaining rows and concatenate cellsText at corresponding indices
        for (int i = 1; i < rows.size(); i++) {
            Summary.SummaryRow row = rows.get(i);
            List<String> rowCellsText = row.getCellsText();

            for (int j = 1; j < rowCellsText.size(); j++) {
                // Concatenate each corresponding index from the cellsText of all rows
                mergedCellsText.set(j, toString(toDouble(mergedCellsText.get(j)) + toDouble(rowCellsText.get(j))));
            }
        }

        // Create a new merged SummaryRow
        return Summary.SummaryRow.builder()
                .rowId(-1)
                .coaId(-1)
                .coaScore(-1)
                .cellsText(mergedCellsText)
                .category(StringUtils.EMPTY)
                .tableId(-1)
                .tableTag(StringUtils.EMPTY)
                .pageNum(-1)
                .xMin(-1)
                .xMax(-1)
                .yMin(-1)
                .yMax(-1)
                .children(new ArrayList<>())
                .isValid(null)
                .sign(null)
                .build();
    }

    private void checkSubtotalValidity(String clientName, UUID currentDocId,
                                       Summary.SummaryOutput mergedSummaries,
                                       String sheetName) throws ExcelSaveException {

        //Get top level rows from merged summaries.
        List<Summary.SummaryRow> parentRows = mergedSummaries.rows();
        parentRows.forEach(parentRow -> parentRow.setIsValid(null));
        if (parentRows.isEmpty()) {
            return;
        }

        List<SubtotalMappingEntity> subtotalMappingsForCurrentDoc = subtotalMappingService.getSubtotalMappingsForDoc(
                currentDocId);

        ArrayList<SubtotalEntity> subtotalsForClient = new ArrayList<>(subtotalService.getSubtotals(
                clientName, clientName));

        //Create a map of COA and subtotal mapping for current doc.
        Map<String, SubtotalMappingEntity> subtotalNameRowMap = new HashMap<>();
        subtotalMappingsForCurrentDoc.forEach(
                subtotalMapping -> subtotalNameRowMap.put(subtotalMapping.getSubtotal().getSubtotalName(),
                        subtotalMapping));

        for (Map.Entry<String, SubtotalMappingEntity> entry : subtotalNameRowMap.entrySet()) {
            logger.debug("Subtotal Name: " + entry.getKey() + ", Subtotal Mapping: " + entry.getValue());
        }

        List<List<SummaryPreviewCell>> summaryPreview = generateSummaryPreview(currentDocId, clientName, sheetName);

        //Get column indexes for current and previous year in excel.
        DocData currentDocData = exchangeService.getDocument(currentDocId);
        Integer yearRow = excelYearRowNo.getOrDefault(clientName, excelYearRowNo.get(DEFAULT_CLIENT_NAME));
        List<SummaryPreviewCell> previewYearRow = summaryPreview.get(yearRow - 1);
        List<Integer> yearIdxsInPreviewRow = getExcelPeriodColumnIndexes(previewYearRow, currentDocData);

        List<String> coasForMergedSummaries = mergedSummaries.rows()
                .stream()
                .map(parentRow -> parentRow.getCellsText().get(0))
                .toList();
        Map<Integer, List<SummaryPreviewCell>> previewRowIdRowMap = new HashMap<>();
        summaryPreview.forEach(
                summaryRow -> previewRowIdRowMap.put(Integer.parseInt(summaryRow.get(0).getText()), summaryRow));

        validateSubtotalWithMappings(mergedSummaries, subtotalNameRowMap, subtotalsForClient, coasForMergedSummaries,
                previewRowIdRowMap,
                yearIdxsInPreviewRow, currentDocData);

        validateSubtotalsWithoutMappings(mergedSummaries, subtotalsForClient, coasForMergedSummaries,
                previewRowIdRowMap,
                yearIdxsInPreviewRow,
                currentDocData);
    }

    private void validateSubtotalsWithoutMappings(Summary.SummaryOutput mergedSummaries,
                                                  ArrayList<SubtotalEntity> subtotalsForClient,
                                                  List<String> coasForMergedSummaries,
                                                  Map<Integer, List<SummaryPreviewCell>> previewRowIdRowMap,
                                                  List<Integer> yearIdxsInPreviewRow, DocData currentDocData) {
        //Process subtotals that have no mappings.
        subtotalsForClient.forEach(subtotal -> {

            //Check if coa for subtotal is present in merged summary.
            int coaIdxInMergedSummary = coasForMergedSummaries.indexOf(subtotal.getSubtotalName());

            //Get preview row.
            List<SummaryPreviewCell> summaryPreviewRowForCoa = previewRowIdRowMap.get(
                    subtotal.getExcelRowNumber().intValue());
            logger.debug("Summary preview row for matching " + summaryPreviewRowForCoa);
            logger.debug("Trimmed summary preview row " + yearIdxsInPreviewRow.stream()
                    .map(summaryPreviewRowForCoa::get)
                    .toList());

            //Getting list of subtotal values from preview (excluding column number and coa).
            DecimalFormat decimalFormat = new DecimalFormat(
                    currentDocData.outputDenomination().getPreviewFormatString());
            decimalFormat.setParseBigDecimal(true);
            List<String> previewRowAmount = yearIdxsInPreviewRow.stream().map(summaryPreviewRowForCoa::get)
                    .map(SummaryPreviewCell::getText)
                    .map(previewText -> {
                        try {
                            logger.debug("Parsing text" + previewText);
                            return ((BigDecimal) decimalFormat.parse(previewText)).multiply(
                                    BigDecimal.valueOf(currentDocData.outputDenomination()
                                            .getFactor())).toPlainString();
                        } catch (ParseException e) {
                            logger.error(
                                    "Failed to parse " + previewText + " using formatter" + decimalFormat.toPattern());
                            return "0";
                        }
                    }).toList();

            //If subtotal coa is present in merged summary update validation.
            if (coaIdxInMergedSummary != -1) {
                mergedSummaries.rows().get(coaIdxInMergedSummary).setIsValid(false);
            }
            //If subtotal coa is absent add new row with no children and values from preview.
            else {
                Summary.SummaryRow subtotalRowForSummary = Summary.SummaryRow.builder()
                        .rowId(-1)
                        .coaId(-1)
                        .coaScore(-1)
                        .category(StringUtils.EMPTY)
                        .cellsText(new ArrayList<>(
                                List.of(subtotal.getSubtotalName(),
                                        toString(Double.parseDouble(previewRowAmount.get(0))),
                                        toString(Double.parseDouble(
                                                previewRowAmount.size() > 1 ? previewRowAmount.get(1) : "0")))))
                        .tableId(-1)
                        .pageNum(-1)
                        .xMin(-1)
                        .xMax(-1)
                        .yMin(-1)
                        .yMax(-1)
                        .children(new ArrayList<>())
                        .isValid(Boolean.FALSE)
                        .build();
                mergedSummaries.rows().add(subtotalRowForSummary);
            }
        });
    }

    private void validateSubtotalWithMappings(Summary.SummaryOutput mergedSummaries,
                                              Map<String, SubtotalMappingEntity> subtotalNameRowMap,
                                              ArrayList<SubtotalEntity> subtotalsForClient,
                                              List<String> coasForMergedSummaries,
                                              Map<Integer, List<SummaryPreviewCell>> previewRowIdRowMap,
                                              List<Integer> yearIdxsInPreviewRow,
                                              DocData currentDocData) {
        //For subtotals with mappings update summary or add mapping.
        subtotalNameRowMap.forEach((subtotalName, subtotalMappingForCoa) -> {
            //Remove subtotal from subtotal client list.
            subtotalsForClient.remove(subtotalMappingForCoa.getSubtotal());

            logger.debug("Matching for COA " + subtotalName);
            //Check if coa for subtotal is present in merged summary.
            int coaIdxInMergedSummary = coasForMergedSummaries.indexOf(subtotalName);

            //Get preview row and table row for comparison.
            List<SummaryPreviewCell> summaryPreviewRowForCoa = previewRowIdRowMap.get(
                    subtotalMappingForCoa.getSubtotal().getExcelRowNumber().intValue());
            Optional<TableRowEntity> optRowForCoa =
                    tableService.getRow(subtotalMappingForCoa.getId().getTableId(),
                            subtotalMappingForCoa.getId().getRowId().intValue());
            if (optRowForCoa.isEmpty() || summaryPreviewRowForCoa.isEmpty()) {
                return;
            }
            logger.debug("Summary preview row for matching " + summaryPreviewRowForCoa);
            logger.debug("Trimmed summary preview row " + yearIdxsInPreviewRow.stream()
                    .map(summaryPreviewRowForCoa::get)
                    .toList());

            //Getting list of subtotal values from table data(excluding header).
            List<String> tableRowAmount = optRowForCoa.get()
                    .getCellsText()
                    .subList(1, 3)
                    .stream()
                    .map(amountStr -> amountStr.replace(",", ""))
                    .map(rowText -> formatStringNumber(rowText, SUMMARY_VALIDATION_DECIMAL_FORMAT))
                    .toList();

            //Getting list of subtotal values from preview (excluding column number and coa).

            List<String> previewRowAmount = yearIdxsInPreviewRow.stream().map(summaryPreviewRowForCoa::get)
                    .map(SummaryPreviewCell::getText)
                    .map(previewText ->
                            getValueTextFromPreviewText(currentDocData, previewText)
                    ).toList();
            logger.debug("Comparing table subtotal " + tableRowAmount);
            logger.debug("To calculated preview subtotal" + previewRowAmount);

            //If subtotal mapping coa is present in merged summary update validation.
            if (coaIdxInMergedSummary != -1) {
                mergedSummaries.rows().get(coaIdxInMergedSummary).setIsValid(tableRowAmount.equals(previewRowAmount));
            }
            //If subtotal mapping coa is absent add new row with no children.
            else {
                Summary.SummaryRow subtotalRowForSummary = Summary.SummaryRow.builder()
                        .rowId(-1)
                        .coaId(-1)
                        .coaScore(-1)
                        .category(StringUtils.EMPTY)
                        .cellsText(new ArrayList<>(
                                List.of(subtotalMappingForCoa.getSubtotal().getSubtotalName(),
                                        toString(Double.parseDouble(previewRowAmount.get(0))),
                                        toString(Double.parseDouble(
                                                previewRowAmount.size() > 1 ? previewRowAmount.get(1) : "0")))))
                        .tableId(-1)
                        .pageNum(-1)
                        .xMin(-1)
                        .xMax(-1)
                        .yMin(-1)
                        .yMax(-1)
                        .children(new ArrayList<>())
                        .isValid(tableRowAmount.equals(previewRowAmount))
                        .build();
                mergedSummaries.rows().add(subtotalRowForSummary);
            }
        });
    }

    public List<String> getSheetNames(String clientName) {
        List<String> sheetNames = new ArrayList<>();

        String coaClientName = Jwt.getClientName(accessToken);
        clientName = getClientName(clientName);

        UrlDto excelTemplateLink = exchangeService.getExcelTemplate(clientName, coaClientName);
        try {
            URL url = new URL(excelTemplateLink.url().toString());
            try (InputStream inputStream = url.openStream();
                 Workbook workbook = WorkbookFactory.create(inputStream)) {

                for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                    sheetNames.add(workbook.getSheetName(i));
                }
            }
        } catch (Exception e) {
            logger.info("Failed to get sheet names for client " + clientName);
            e.printStackTrace();
        }
        return sheetNames;
    }

    public Response generateCustomSummaryOutput(String clientName,
                                                UUID docId) throws ExcelSaveException, JSONDeserializationException {
        DocData docData = exchangeService.getDocument(docId);
        if (!Client.isValidClient(clientName) || !StatusEnum.COMPLETED.equals(docData.status())) {
            return Response.noContent().build();
        }
        Client client = Client.valueOf(clientName.toUpperCase());
        if (client.equals(Client.JSW)) {
            JSWSummaryOutput.FinancialsDto financialsDto = jswClientOutputService.getJSWClientOutput(docData);
            return Response.ok(generateClientJSON(financialsDto, docData.period().getYear())).build();
        }
        return Response.noContent().build();
    }

    public String downloadSummaryJson(UUID docId, String clientName,
                                      @NotEmpty List<String> sheetNames) throws IOException {
        DocData docData = exchangeService.getDocument(docId);
        try (Workbook workbook = saveExcel(docData, clientName)) {
            if (workbook == null) {
                ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR,
                        "Failed to save workbook for client " + clientName);
                return null;
            }
            for (String sheetName : sheetNames) {
                if (workbook.getSheetIndex(sheetName) == -1) {
                    ResponseException.throwResponseException(Response.Status.NOT_FOUND,
                            "Sheet " + sheetName + " not found in workbook");
                    return null;
                }
            }
            ObjectMapper mapper = new ObjectMapper();
            ObjectNode root = mapper.createObjectNode();

            DecimalFormat decimalFormat = new DecimalFormat(docData.outputDenomination().getPreviewFormatString());

            for (String sheetName : sheetNames) {
                Sheet sheet = workbook.getSheet(sheetName);
                ArrayNode sheetArray = mapper.createArrayNode();

                int colRange = getColRange(clientName, sheet, decimalFormat, docData);
                for (Row row : sheet) {
                    ArrayNode rowArray = mapper.createArrayNode();
                    for (int i = 0; i < colRange; i++) {
                        Cell cell = row.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                        ImmutablePair<String, CellType> cellValue = getCellValue(cell, DECIMAL_FORMAT);
                        rowArray.add(cellValue.getLeft());
                    }
                    sheetArray.add(rowArray);
                }

                // add this sheet’s data under its name
                root.set(sheetName, sheetArray);
            }

            return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(root);
        }
    }

    @FunctionalInterface
    public interface GroupByLvlCategory {
        void group(List<Summary.SummaryRow> summaryRows, SummaryLevel level);
    }
}
