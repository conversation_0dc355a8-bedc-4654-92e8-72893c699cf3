package com.walnut.vegaspread.extraction.service;

import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import com.walnut.vegaspread.extraction.entity.CoaDataEntity;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.entity.TableHeaderEntity;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.model.Validation;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import jakarta.enterprise.context.ApplicationScoped;
import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.Logger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.walnut.vegaspread.common.utils.Constants.BALANCE_SHEET_TAG;
import static com.walnut.vegaspread.common.utils.Constants.INCOME_STATEMENT_TAG;
import static com.walnut.vegaspread.extraction.model.Validation.ValidationError.LVL1_CATEGORY_HEAD_MISMATCH;
import static com.walnut.vegaspread.extraction.model.Validation.ValidationError.LVL1_CATEGORY_TABLE_CATEGORY_MISMATCH;

@ApplicationScoped
public class ValidationService {
    public static final String SEPARATOR = " - ";
    public static final String BS_NTA_TABLE_TAG = "BS.NTA";
    public static final String IS_NTA_TABLE_TAG = "IS.NTA";
    public static final List<String> UNWANTED_FS_TABLES = List.of("Cash Flow", "Changes in Equity");
    private static final String LVL1_CATEGORY_SEPARATOR = "\\.";
    private static final String MISSING_ROW_LOG = "No row found for row pk id %s";
    private final LayoutService layoutService;
    private final ExtractedRowCoaDataService extractedRowCoaDataService;
    private final Logger logger;
    private final CoaDataService coaDataService;
    private final ExchangeService exchangeService;

    public ValidationService(LayoutService layoutService, ExtractedRowCoaDataService extractedRowCoaDataService,
                             Logger logger, CoaDataService coaDataService, ExchangeService exchangeService) {
        this.layoutService = layoutService;
        this.extractedRowCoaDataService = extractedRowCoaDataService;
        this.logger = logger;
        this.coaDataService = coaDataService;
        this.exchangeService = exchangeService;
    }

    //Validate BS.NTA and IS.NTA linked row belongs to Balance Sheet or Income Statement respectively.
    private static Validation.ResponseDto validateLinkedTableTag(TableRowEntity linkedRow, String tableTag) {
        if (
                (tableTag.equals(BS_NTA_TABLE_TAG) && !linkedRow.getLayoutBlock()
                        .getTag().getTag()
                        .equals(BALANCE_SHEET_TAG))
                        || (tableTag.equals(IS_NTA_TABLE_TAG) && !linkedRow.getLayoutBlock()
                        .getTag().getTag()
                        .equals(INCOME_STATEMENT_TAG))
        ) {

            return new Validation.ResponseDto(linkedRow.getTableRowPkId().getTableId(),
                    linkedRow.getTableRowPkId().getRowId(), linkedRow.getLayoutBlock().getPageNum(),
                    tableTag, linkedRow.getCellsText().get(0), "",
                    Validation.ValidationError.INVALID_TABLE_FOR_LINKED_TABLE_TAG.getMessage());
        }
        return null;
    }

    private String cleanString(String str) {
        return str.replaceAll("[^a-zA-Z]", "").toLowerCase();
    }

    //Get all rows linked to a NTA table for a list of fs tables.
    private List<TableRowEntity> getNtaLinkedRows(List<LayoutBlockEntity> fsTables) {
        return fsTables.stream()
                .flatMap(fsTable -> fsTable.getTableRows().stream())
                .filter(row -> row.getNtaTable() != null)
                .toList();
    }

    //All tables must have the first column header as "Description"
    private List<Validation.ResponseDto> validateTableStandardization(List<LayoutBlockEntity> tables) {
        List<Validation.ResponseDto> invalidTables = new ArrayList<>();
        for (LayoutBlockEntity table : tables) {
            TableHeaderEntity firstHeader = table.getTableHeaders()
                    .stream()
                    .filter(header -> header.getPos() == 0)
                    .toList()
                    .get(0);
            if (!firstHeader.getText().equalsIgnoreCase("description")) {
                invalidTables.add(new Validation.ResponseDto(table.getBlockId(), -1, table.getPageNum(),
                        table.getTag().getTag(), "", "",
                        Validation.ValidationError.TABLE_NOT_STANDARDIZED.getMessage()));
            }
        }
        return invalidTables;
    }

    //Get a map of table row id and corresponding row header category for all table rows.
    private Map<TableRowPkId, Validation.RowCategory> getHeaderCategoriesForRows(LayoutBlockEntity table) {
        HashMap<TableRowPkId, Validation.RowCategory> rowHeaderMap = new HashMap<>();
        for (TableRowEntity row : table.getTableRows()) {
            rowHeaderMap.put(row.getTableRowPkId(), getRowHeaderCategory(row));
        }
        return rowHeaderMap;
    }

    //Get category for row header.
    private Validation.RowCategory getRowHeaderCategory(TableRowEntity row) {
        String firstCell = row.getCellsText().get(0);
        if (!firstCell.contains(SEPARATOR)) {
            return Validation.RowCategory.NA;
        }
        String cleanHead = cleanString(firstCell.split(SEPARATOR)[0]);
        if (Validation.RowCategory.getValues().contains(cleanHead)) {
            return Validation.RowCategory.fromValue(cleanHead);
        } else {
            return Validation.RowCategory.NA;
        }
    }

    //Split each FS table row first column data by " - " and get the head and validate if head is correct.
    private List<Validation.ResponseDto> validateRowDescriptions(List<LayoutBlockEntity> tables,
                                                                 List<Validation.RowCategory> categories) {
        List<Validation.ResponseDto> invalidRows = new ArrayList<>();
        for (LayoutBlockEntity table : tables) {
            Map<TableRowPkId, Validation.RowCategory> rowHeaders = getHeaderCategoriesForRows(table);
            Map<TableRowPkId, TableRowEntity> rowEntityMap = table.getTableRows().stream()
                    .collect(Collectors.toMap(TableRowEntity::getTableRowPkId, row -> row));
            rowHeaders.forEach((rowPkId, category) -> {
                if (!categories.contains(category)) {
                    TableRowEntity row = rowEntityMap.get(rowPkId);
                    invalidRows.add(new Validation.ResponseDto(
                            rowPkId.getTableId(),
                            rowPkId.getRowId(),
                            table.getPageNum(),
                            table.getTag().getTag(),
                            row.getCellsText().get(0),
                            "",
                            Validation.ValidationError.FS_ROW_INCORRECT_HEAD.getMessage()));
                }
            });
        }
        return invalidRows;
    }

    //If an FS table row has NTA linked, the row must not have a COA mapping.
    private List<Validation.ResponseDto> validateNtaRowsCoaMapping(List<LayoutBlockEntity> fsTables,
                                                                   List<TableRowEntity> ntaLinkedFsRows) {
        //Get all coa mappings for income statement and balance sheet.
        List<ExtractedTableRowCoaDataJoinEntity> mappedFsRows = extractedRowCoaDataService.findCoaDataMappingsForTable(
                fsTables.stream().map(LayoutBlockEntity::getBlockId).toList());
        List<TableRowPkId> rowPkIdsForCoaDataMappings = mappedFsRows.stream()
                .map(mapping -> mapping.getExtractedTableRowCoaDataPkId().getTableRowPkId())
                .toList();

        List<TableRowEntity> ntaLinkedRowsWithCoaMappings = ntaLinkedFsRows.stream()
                .filter(ntaLinkedRow -> rowPkIdsForCoaDataMappings.contains(ntaLinkedRow.getTableRowPkId()))
                .toList();

        List<Validation.ResponseDto> invalidTables = new ArrayList<>();
        for (TableRowEntity ntaLinkedRow : ntaLinkedRowsWithCoaMappings) {
            LayoutBlockEntity fsTable = fsTables.stream()
                    .filter(t -> t.getBlockId().equals(ntaLinkedRow.getTableRowPkId().getTableId()))
                    .findFirst()
                    .orElse(null);
            if (fsTable == null) {
                logger.warnf("FS Table not found for row %s", ntaLinkedRow.getTableRowPkId());
                continue;
            }
            invalidTables.add(new Validation.ResponseDto(
                    fsTable.getBlockId(),
                    ntaLinkedRow.getTableRowPkId().getRowId(),
                    fsTable.getPageNum(),
                    fsTable.getTag().getTag(),
                    ntaLinkedRow.getCellsText().get(0),
                    "",
                    Validation.ValidationError.LINKED_FS_ROW_MAPPED.getMessage()));
        }
        return invalidTables;
    }

    //Checks for Balance Sheet / Income Statement
    public List<Validation.ResponseDto> validateBSOrIS(UUID docId) {
        List<Validation.ResponseDto> invalidEntries = new ArrayList<>();

        //Get all tables for balance sheets and income statements;.
        List<LayoutBlockEntity> balanceSheetTables = layoutService.findAllBlocksInDocWithTag(docId, BALANCE_SHEET_TAG);
        List<LayoutBlockEntity> incomeStatementTables = layoutService.findAllBlocksInDocWithTag(docId,
                INCOME_STATEMENT_TAG);
        List<LayoutBlockEntity> fsTables = Stream.concat(balanceSheetTables.stream(),
                incomeStatementTables.stream()).toList();

        //Get all nta linked rows for income statement and balance sheet.
        List<TableRowEntity> ntaLinkedRows = getNtaLinkedRows(fsTables);

        invalidEntries.addAll(validateTableStandardization(fsTables));
        invalidEntries.addAll(validateNtaRowsCoaMapping(fsTables, ntaLinkedRows));

        List<Validation.RowCategory> bsCategories = Stream.of(Validation.RowCategory.values())
                .filter(category -> !List.of(Validation.RowCategory.INCOME_STATEMENT,
                                Validation.RowCategory.NA)
                        .contains(category))
                .toList();
        invalidEntries.addAll(validateRowDescriptions(balanceSheetTables, bsCategories));
        invalidEntries.addAll(
                validateRowDescriptions(incomeStatementTables, List.of(Validation.RowCategory.INCOME_STATEMENT)));

        return invalidEntries;
    }

    //All tables with tag have at least one row with COA mapping
    private List<Validation.ResponseDto> validateNtaTableCoaMapping(List<LayoutBlockEntity> taggedNtaTables) {
        List<Validation.ResponseDto> invalidTables = new ArrayList<>();

        List<Integer> taggedNtaTablesWithCoaMappings = extractedRowCoaDataService.getTablesWithCoaMappingsInList(
                taggedNtaTables.stream().map(LayoutBlockEntity::getBlockId).toList());
        List<LayoutBlockEntity> taggedNtaTablesWithoutCoaMappings = taggedNtaTables.stream()
                .filter(taggedNtaTable -> !taggedNtaTablesWithCoaMappings.contains(taggedNtaTable.getBlockId()))
                .toList();

        taggedNtaTablesWithoutCoaMappings.forEach(taggedNtaTablesWithoutCoaMapping ->
                invalidTables.add(new Validation.ResponseDto(taggedNtaTablesWithoutCoaMapping.getBlockId(), -1,
                        taggedNtaTablesWithoutCoaMapping.getPageNum(),
                        taggedNtaTablesWithoutCoaMapping.getTag().getTag(),
                        "",
                        "",
                        Validation.ValidationError.NTA_TABLE_NO_MAPPING.getMessage())));
        return invalidTables;
    }

    // All tagged tables must be linked to a row in Balance Sheet or Income Statement
    private List<Validation.ResponseDto> validateNtaTableRowLinking(List<LayoutBlockEntity> taggedNtaTables,
                                                                    List<LayoutBlockEntity> fsTables) {
        List<Validation.ResponseDto> invalidTables = new ArrayList<>();
        List<Integer> linkedNtaTableIds = getNtaLinkedRows(fsTables).stream()
                .map(row -> row.getNtaTable().getBlockId())
                .toList();
        List<LayoutBlockEntity> taggedTablesMissingBsIsLinkedRow = taggedNtaTables.stream()
                .filter(taggedNtaTable -> !linkedNtaTableIds.contains(taggedNtaTable.getBlockId()))
                .toList();
        taggedTablesMissingBsIsLinkedRow.forEach(taggedTableMissingBsIsLikedRow ->
                invalidTables.add(new Validation.ResponseDto(taggedTableMissingBsIsLikedRow.getBlockId(), -1,
                        taggedTableMissingBsIsLikedRow.getPageNum(),
                        taggedTableMissingBsIsLikedRow.getTag().getTag(),
                        "",
                        "",
                        Validation.ValidationError.NTA_TABLE_NO_FS_LINK.getMessage())));
        return invalidTables;
    }

    //Method to check if tag category is contained in a row header category.
    private boolean isInvalidCategory(String tagElement, List<String> messages) {

        return !messages.contains(tagElement);
    }

    //Check if the table tag and row/coa category match.
    private boolean isInvalidTag(Validation.RowCategory category, String tag) {

        List<String> tagElements = Arrays.stream(tag.split("\\.")).map(this::cleanString).toList();
        String tagCategory = StringUtils.EMPTY;
        if (tagElements.size() >= 2) {
            tagCategory = tagElements.get(tagElements.size() - 2);
        } else if (tagElements.size() == 1) {
            tagCategory = tagElements.get(0);
        }
        return switch (category) {
            case ASSETS, LIABILITIES, EQUITY, INCOME_STATEMENT ->
                    isInvalidCategory(tagCategory, category.getMessages());
            case CURRENT_ASSETS, NON_CURRENT_ASSETS ->
                    isInvalidCategory(tagCategory, category.getMessages()) && isInvalidCategory(tagCategory,
                            Validation.RowCategory.ASSETS.getMessages());
            case CURRENT_LIABILITIES, NON_CURRENT_LIABILITIES ->
                    isInvalidCategory(tagCategory, category.getMessages()) && isInvalidCategory(tagCategory,
                            Validation.RowCategory.LIABILITIES.getMessages());
            default -> true;
        };
    }

    //The table tag must match with the linked FS table row header.
    private List<Validation.ResponseDto> validateTagWithLinkedRowDesc(List<LayoutBlockEntity> taggedNtaTables,
                                                                      List<LayoutBlockEntity> fsTables) {
        List<Validation.ResponseDto> invalidTables = new ArrayList<>();
        List<TableRowEntity> linkedRows = getNtaLinkedRows(fsTables);
        for (LayoutBlockEntity taggedNtaTable : taggedNtaTables) {
            List<TableRowEntity> linkedRowsForTable = linkedRows.parallelStream()
                    .filter(row -> row.getNtaTable().getBlockId().equals(taggedNtaTable.getBlockId()))
                    .toList();
            if (linkedRowsForTable.isEmpty()) {
                logger.warnf("No linked row found for table %s", taggedNtaTable.getBlockId());
                continue;
            }
            linkedRowsForTable.forEach(linkedRow -> {
                Validation.RowCategory rowHeaderCategory = getRowHeaderCategory(linkedRow);
                String tableTag = taggedNtaTable.getTag().getTag();
                if (tableTag.equals(BS_NTA_TABLE_TAG) || tableTag.equals(IS_NTA_TABLE_TAG)) {
                    Validation.ResponseDto invalidTable = validateLinkedTableTag(linkedRow, tableTag);
                    if (invalidTable != null) {
                        invalidTables.add(invalidTable);
                    }
                    return;
                }

                if (isInvalidTag(rowHeaderCategory, taggedNtaTable.getTag().getTag())) {
                    invalidTables.add(
                            new Validation.ResponseDto(linkedRow.getTableRowPkId().getTableId(),
                                    linkedRow.getTableRowPkId().getRowId(), linkedRow.getLayoutBlock().getPageNum(),
                                    taggedNtaTable.getTag().getTag(), linkedRow.getCellsText().get(0), "",
                                    Validation.ValidationError.INVALID_FS_ROW_HEADER_FOR_LINKED_TABLE_TAG.getMessage()));
                }
            });
        }
        return invalidTables;
    }

    //Check if a table is a fs table.
    private boolean isFSTable(LayoutBlockEntity table) {
        return List.of(BALANCE_SHEET_TAG, INCOME_STATEMENT_TAG).contains(table.getTag().getTag());
    }

    // Checks for NTA tables
    public List<Validation.ResponseDto> validateNtaTables(UUID docId) {
        List<Validation.ResponseDto> invalidNtaTables = new ArrayList<>();
        List<LayoutBlockEntity> taggedTables = layoutService.getTaggedTables(docId);
        List<LayoutBlockEntity> fsTables = taggedTables.stream()
                .filter(this::isFSTable)
                .toList();
        List<LayoutBlockEntity> taggedNtaTables = taggedTables.stream()
                .filter(taggedTable -> !fsTables.contains(taggedTable) &&
                        !UNWANTED_FS_TABLES.contains(taggedTable.getTag().getTag()))
                .toList();
        invalidNtaTables.addAll(validateTableStandardization(taggedNtaTables));
        invalidNtaTables.addAll(validateNtaTableCoaMapping(taggedNtaTables));
        invalidNtaTables.addAll(validateNtaTableRowLinking(taggedNtaTables, fsTables));
        invalidNtaTables.addAll(validateTagWithLinkedRowDesc(taggedNtaTables, fsTables));
        return invalidNtaTables;
    }

    //All mapped rows must belong to a tagged table
    private List<Validation.ResponseDto> validateTagsForCoaMappedRows(List<LayoutBlockEntity> taggedTables,
                                                                      Map<Integer,
                                                                              List<ExtractedTableRowCoaDataJoinEntity>> tableCoaMappings) {
        List<Validation.ResponseDto> invalidRows = new ArrayList<>();
        //Get all table ids for tagged tables.
        List<Integer> taggedTableIds = taggedTables.stream().map(LayoutBlockEntity::getBlockId).toList();

        for (Map.Entry<Integer, List<ExtractedTableRowCoaDataJoinEntity>> tableCoaMapping :
                tableCoaMappings.entrySet()) {
            Integer tableId = tableCoaMapping.getKey();
            //Check if the current table containing coa mapped row is tagged.
            if (!taggedTableIds.contains(tableId)) {
                //Add all coa mapped to rows for untagged table to invalid list.
                Integer pageNum = Integer.valueOf(layoutService.getBlock(tableId).getPageNum());
                List<TableRowEntity> tableRows = layoutService.getBlock(tableId).getTableRows();
                for (ExtractedTableRowCoaDataJoinEntity rowCoaMapping : tableCoaMapping.getValue()) {
                    TableRowPkId rowPkId = rowCoaMapping.getExtractedTableRowCoaDataPkId().getTableRowPkId();
                    TableRowEntity row = tableRows.stream()
                            .filter(r -> r.getTableRowPkId().equals(rowPkId))
                            .findFirst()
                            .orElse(null);
                    if (row == null) {
                        logger.warnf(MISSING_ROW_LOG, rowPkId);
                        continue;
                    }
                    invalidRows.add(new Validation.ResponseDto(
                            rowPkId.getTableId(),
                            rowPkId.getRowId(),
                            pageNum,
                            "",
                            row.getCellsText().get(0),
                            "",
                            Validation.ValidationError.COA_MAPPED_ROW_NO_TABLE_TAG.getMessage()));
                }
            }
        }
        return invalidRows;
    }

    //For FS tables the lvl1 category of COA should match the row header category.
    private void validateCategoriesForFSTableMappedRows(
            Map.Entry<Integer, List<ExtractedTableRowCoaDataJoinEntity>> tableCoaMapping,
            LayoutBlockEntity table, Map<Integer, Integer> coaDataMap,
            Map<Integer, CoaItemDto> coaItemMap, List<Validation.ResponseDto> invalidRows) {
        Map<TableRowPkId, Validation.RowCategory> rowHeaders = getHeaderCategoriesForRows(table);
        for (ExtractedTableRowCoaDataJoinEntity rowCoaData : tableCoaMapping.getValue()) {
            Integer coaId = coaDataMap.get(rowCoaData.extractedTableRowCoaDataPkId.getCoaDataId());
            CoaItemDto coaItem = coaItemMap.get(coaId);
            String category = cleanString(coaItem.lvl1Category().split(LVL1_CATEGORY_SEPARATOR)[0]);
            TableRowPkId mappedRowPkId = rowCoaData.getExtractedTableRowCoaDataPkId()
                    .getTableRowPkId();
            if (isInvalidRowHeadForCOA(rowHeaders.get(mappedRowPkId), category)) {
                addInvalidRows(table, mappedRowPkId, invalidRows, coaItem, LVL1_CATEGORY_HEAD_MISMATCH);
            }
        }
    }

    //Check if row category and coa category match for FS table.
    private boolean isInvalidRowHeadForCOA(Validation.RowCategory category, String coaCategory) {

        return switch (category) {
            case ASSETS -> isInvalidCategory(coaCategory,
                    Validation.RowCategory.ASSETS.getMessages()) && isInvalidCategory(
                    coaCategory, Validation.RowCategory.CURRENT_ASSETS.getMessages()) && isInvalidCategory(
                    coaCategory, Validation.RowCategory.NON_CURRENT_ASSETS.getMessages());
            case LIABILITIES -> isInvalidCategory(coaCategory,
                    Validation.RowCategory.LIABILITIES.getMessages()) && isInvalidCategory(coaCategory,
                    Validation.RowCategory.CURRENT_LIABILITIES.getMessages()) && isInvalidCategory(coaCategory,
                    Validation.RowCategory.NON_CURRENT_LIABILITIES.getMessages());
            case EQUITY, INCOME_STATEMENT, CURRENT_ASSETS, NON_CURRENT_ASSETS, CURRENT_LIABILITIES,
                 NON_CURRENT_LIABILITIES -> isInvalidCategory(coaCategory, category.getMessages());
            default -> true;
        };
    }

    //Clean lvl1 category and get the corresponding row category.
    private Validation.RowCategory getCoaCategory(String lvl1Category) {
        String category = cleanString(lvl1Category.split(LVL1_CATEGORY_SEPARATOR)[0]);

        Validation.RowCategory coaCategory = Validation.RowCategory.NA;
        if (Validation.RowCategory.getValues().contains(category)) {
            coaCategory = Validation.RowCategory.fromValue(category);
        }
        return coaCategory;
    }

    //For NTA tables, mapped rows must match with table tag category.
    private void validateCategoriesForNTATableMappedRows(
            Map.Entry<Integer, List<ExtractedTableRowCoaDataJoinEntity>> tableCoaMapping, LayoutBlockEntity table,
            Map<Integer, Integer> coaDataMap, Map<Integer, CoaItemDto> coaItemMap,
            List<Validation.ResponseDto> invalidRows) {

        for (ExtractedTableRowCoaDataJoinEntity rowCoaDataJoinEntity : tableCoaMapping.getValue()) {
            //Get lvl1 category for the current coa mapped row.
            Integer coaDataId = rowCoaDataJoinEntity.getExtractedTableRowCoaDataPkId().getCoaDataId();
            CoaItemDto coaItem = coaItemMap.get(coaDataMap.get(coaDataId));

            TableRowPkId mappedRowPkId = rowCoaDataJoinEntity.getExtractedTableRowCoaDataPkId().getTableRowPkId();

            String tableTag = table.getTag().getTag();
            String coaText = coaItem.coaText();
            boolean isBsNtaTable = tableTag.equals(BS_NTA_TABLE_TAG);
            boolean isIsNtaTable = tableTag.equals(IS_NTA_TABLE_TAG);
            boolean isInvalidTag = isInvalidTag(getCoaCategory(coaItem.lvl1Category()), tableTag);

            if (isBsNtaTable || isIsNtaTable) {
                if ((isBsNtaTable && coaText.contains("IS")) || (isIsNtaTable && coaText.contains(
                        "BS"))) {
                    addInvalidRows(table, mappedRowPkId, invalidRows, coaItem, LVL1_CATEGORY_TABLE_CATEGORY_MISMATCH);
                }
            } else {
                if (isInvalidTag) {
                    //Verify coa category for tagged NTA tables, not tagged with BS_NTA_TABLE_TAG or IS_NTA_TABLE_TAG.
                    addInvalidRows(table, mappedRowPkId, invalidRows, coaItem, LVL1_CATEGORY_TABLE_CATEGORY_MISMATCH);
                }
            }
        }
    }

    private void addInvalidRows(LayoutBlockEntity table, TableRowPkId mappedRowPkId,
                                List<Validation.ResponseDto> invalidRows, CoaItemDto coaItem,
                                Validation.ValidationError lvl1CategoryTableCategoryMismatch) {
        //Verify coa category for tagged NTA tables, not tagged with BS_NTA_TABLE_TAG or IS_NTA_TABLE_TAG.
        TableRowEntity row = table.getTableRows().stream()
                .filter(r -> r.getTableRowPkId().equals(mappedRowPkId))
                .findFirst()
                .orElse(null);
        if (row == null) {
            logger.warnf(MISSING_ROW_LOG, mappedRowPkId);
            return;
        }
        invalidRows.add(new Validation.ResponseDto(mappedRowPkId.getTableId(), mappedRowPkId.getRowId(),
                table.getPageNum(), table.getTag().getTag(), row.getCellsText().get(0), coaItem.coaText(),
                lvl1CategoryTableCategoryMismatch.getMessage()));
    }

    //Check lvl1 category of COA, it must match category of table or head for FS table.
    private List<Validation.ResponseDto> validateCategoriesForCoaMappedRows(
            Map<Integer, List<ExtractedTableRowCoaDataJoinEntity>> tableCoaMappings,
            List<ExtractedTableRowCoaDataJoinEntity> rowCoaMappings) {
        List<Validation.ResponseDto> invalidRows = new ArrayList<>();

        //Get coa data entities for mapped rows.
        List<Integer> coaDataIdsForMappings = rowCoaMappings.stream()
                .map(rowCoaMapping -> rowCoaMapping.getExtractedTableRowCoaDataPkId().getCoaDataId())
                .toList();
        List<CoaDataEntity> coaDataEntities = coaDataService.getCoaDataByIds(coaDataIdsForMappings);

        // Create a map of coa data id and coa id.
        Map<Integer, Integer> coaDataMap = coaDataEntities.stream()
                .collect(Collectors.toMap(CoaDataEntity::getId, CoaDataEntity::getCoaId,
                        (existing, replacement) -> existing));

        List<Integer> coaIds = coaDataEntities.stream().map(CoaDataEntity::getCoaId).distinct().toList();
        List<CoaItemDto> coas = exchangeService.getCoasFromIds(coaIds);

        //Create a map of coaId and corresponding coa item.
        Map<Integer, CoaItemDto> coaItemMap = coas.stream()
                .collect(Collectors.toMap(CoaItemDto::coaId, coaItemDto -> coaItemDto,
                        (existing, replacement) -> existing));

        for (Map.Entry<Integer, List<ExtractedTableRowCoaDataJoinEntity>> tableCoaMapping :
                tableCoaMappings.entrySet()) {
            LayoutBlockEntity table = layoutService.getBlock(tableCoaMapping.getKey());
            if (isFSTable(table)) {
                validateCategoriesForFSTableMappedRows(tableCoaMapping, table, coaDataMap, coaItemMap,
                        invalidRows);
            } else {
                validateCategoriesForNTATableMappedRows(tableCoaMapping, table, coaDataMap, coaItemMap,
                        invalidRows);
            }
        }
        return invalidRows;
    }

    // Checks for COA mapped rows
    public List<Validation.ResponseDto> validateCoaMappedRows(UUID docId) {
        List<Validation.ResponseDto> invalidRows = new ArrayList<>();

        List<LayoutBlockEntity> taggedTables = layoutService.getTaggedTables(docId);
        taggedTables = taggedTables.stream()
                .filter(taggedTable -> !UNWANTED_FS_TABLES.contains(taggedTable.getTag().getTag()))
                .toList();

        List<ExtractedTableRowCoaDataJoinEntity> rowCoaMappings =
                extractedRowCoaDataService.findCoaDataMappingsForTable(
                        layoutService.getBlockIdsForDoc(docId));

        //Create a map of table and all coa mapped rows for the table.
        Map<Integer, List<ExtractedTableRowCoaDataJoinEntity>> tableCoaMappings = rowCoaMappings.stream()
                .collect(Collectors.groupingBy(rowCoaMapping -> rowCoaMapping.getExtractedTableRowCoaDataPkId()
                        .getTableRowPkId()
                        .getTableId()));

        invalidRows.addAll(validateTagsForCoaMappedRows(taggedTables, tableCoaMappings));
        invalidRows.addAll(validateCategoriesForCoaMappedRows(tableCoaMappings, rowCoaMappings));

        return invalidRows;
    }
}
