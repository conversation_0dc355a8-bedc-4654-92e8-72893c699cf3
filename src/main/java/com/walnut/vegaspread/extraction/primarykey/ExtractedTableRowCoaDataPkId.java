package com.walnut.vegaspread.extraction.primarykey;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Embeddable
public class ExtractedTableRowCoaDataPkId implements Serializable {
    public static final String COA_DATA_COL_NAME = "coa_data_id";
    private TableRowPkId tableRowPkId;

    @Column(name = COA_DATA_COL_NAME)
    private Integer coaDataId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ExtractedTableRowCoaDataPkId that = (ExtractedTableRowCoaDataPkId) o;
        return Objects.equals(tableRowPkId, that.tableRowPkId) && Objects.equals(coaDataId, that.coaDataId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tableRowPkId, coaDataId);
    }
}
