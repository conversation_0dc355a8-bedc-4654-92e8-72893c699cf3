package com.walnut.vegaspread.extraction.primarykey;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.hibernate.proxy.HibernateProxy;

import java.io.Serializable;
import java.util.Objects;

@Getter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Embeddable
public class TableRowPkId implements Serializable {

    public static final String TABLE_ID_COL_NAME = "table_id";
    public static final String ROW_ID_COL_NAME = "row_id";

    @JsonIgnore
    @Column(name = TABLE_ID_COL_NAME, nullable = false)
    private Integer tableId;

    @Schema(type = SchemaType.INTEGER, example = "0")
    @Column(name = ROW_ID_COL_NAME, nullable = false)
    private Short rowId;

    public TableRowPkId(Integer tableId, Integer rowId) {
        this.tableId = tableId;
        this.rowId = rowId.shortValue();
    }

    @Override
    public final boolean equals(Object o) {
        if (o == null) return false;
        if (this.getClass() != o.getClass()) return false;
        if (this == o) return true;
        Class<?> oEffectiveClass = o instanceof HibernateProxy hp ? hp.getHibernateLazyInitializer()
                .getPersistentClass() : o.getClass();
        Class<?> thisEffectiveClass = this instanceof HibernateProxy hp ? hp.getHibernateLazyInitializer()
                .getPersistentClass() : this.getClass();
        if (thisEffectiveClass != oEffectiveClass) return false;
        TableRowPkId that = (TableRowPkId) o;
        return tableId != null && Objects.equals(tableId, that.tableId)
                && rowId != null && Objects.equals(rowId, that.rowId);
    }

    @Override
    public final int hashCode() {
        return Objects.hash(tableId, rowId);
    }
}
