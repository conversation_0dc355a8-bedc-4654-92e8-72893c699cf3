package com.walnut.vegaspread.extraction.primarykey;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.hibernate.proxy.HibernateProxy;

import java.io.Serializable;
import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
@ToString
@Embeddable
public class TableCellPkId implements Serializable {

    public static final String TABLE_ID_COL_NAME = "table_id";
    public static final String HEADER_ID_COL_NAME = "header_id";
    public static final String ROW_ID_COL_NAME = "row_id";

    @JsonIgnore
    @Column(name = TABLE_ID_COL_NAME, nullable = false)
    public Integer tableId;

    @Schema(type = SchemaType.INTEGER, example = "0")
    @Column(name = HEADER_ID_COL_NAME, nullable = false)
    public Byte headerId;

    @Schema(type = SchemaType.INTEGER, example = "0")
    @Column(name = ROW_ID_COL_NAME, nullable = false)
    public Short rowId;

    @Override
    public final boolean equals(Object o) {
        if (o == null) return false;
        if (this.getClass() != o.getClass()) return false;
        if (this == o) return true;
        Class<?> oEffectiveClass = o instanceof HibernateProxy hp ? hp.getHibernateLazyInitializer().getPersistentClass() : o.getClass();
        Class<?> thisEffectiveClass = this instanceof HibernateProxy hp ? hp.getHibernateLazyInitializer().getPersistentClass() : this.getClass();
        if (thisEffectiveClass != oEffectiveClass) return false;
        TableCellPkId that = (TableCellPkId) o;
        return tableId != null && Objects.equals(tableId, that.tableId)
                && headerId != null && Objects.equals(headerId, that.headerId)
                && rowId != null && Objects.equals(rowId, that.rowId);
    }

    @Override
    public final int hashCode() {
        return Objects.hash(tableId, headerId, rowId);
    }
}