package com.walnut.vegaspread.extraction.repository;

import com.walnut.vegaspread.extraction.entity.SubtotalEntity;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;
import java.util.Optional;

@ApplicationScoped
public class SubtotalRepository implements PanacheRepositoryBase<SubtotalEntity, Integer> {
    public Optional<SubtotalEntity> findByCoaClientAndClientAndExcelRowNumber(String coaClient, String client,
                                                                              Short excelRowNumber) {
        return find("coaClient = ?1 AND client = ?2 AND excelRowNumber = ?3", coaClient, client,
                excelRowNumber).firstResultOptional();
    }

    public long deleteByCoaClientAndClientAndExcelRowNumber(String coaClient, String client, Short excelRowNumber) {
        return delete("coaClient = ?1 AND client = ?2 AND excelRowNumber = ?3", coaClient, client, excelRowNumber);
    }

    public List<SubtotalEntity> findByCoaClientAndClient(String coaClient, String client) {
        Sort sort = Sort.by("id");
        return find("coaClient = ?1 AND client = ?2", sort, coaClient, client).stream().toList();
    }
}
