package com.walnut.vegaspread.extraction.repository;

import com.walnut.vegaspread.extraction.entity.TableHeaderEntity;
import com.walnut.vegaspread.extraction.primarykey.TableHeaderPkId;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;

import java.util.List;

@ApplicationScoped
public class TableHeaderRepository implements PanacheRepositoryBase<TableHeaderEntity, TableHeaderPkId> {

    @Transactional
    public long deleteForTableIds(List<Integer> tableIds) {
        return delete("tableHeaderPkId.tableId in ?1", tableIds);
    }

    public TableHeaderEntity findByTableIdAndPos(int tableId, int pos) {
        return find("tableHeaderPkId.tableId = ?1 AND pos = ?2", tableId, pos).firstResult();
    }

    public Byte findMaxHeaderIdForTable(int tableId) {
        return find(" SELECT MAX(tableHeaderPkId.headerId) FROM TableHeaderEntity WHERE tableHeaderPkId.tableId = ?1",
                tableId).project(Byte.class)
                .firstResult();
    }

    public List<TableHeaderEntity> getForTableSortByPos(Integer tableId) {
        return find("tableHeaderPkId.tableId = ?1", Sort.by("pos", Sort.Direction.Ascending), tableId).list();
    }

    public long deleteByTableIdAndHeaderId(int tableId, int headerId) {
        return delete("tableHeaderPkId.tableId = ?1 AND tableHeaderPkId.headerId = ?2", tableId, headerId);
    }

    public List<TableHeaderEntity> findHeadersForTable(int tableId) {
        return find("tableHeaderPkId.tableId = ?1", tableId).list();
    }
}
