package com.walnut.vegaspread.extraction.repository;

import com.walnut.vegaspread.extraction.entity.CoaDataEntity;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;
import java.util.Optional;

@ApplicationScoped
public class CoaDataRepository implements PanacheRepositoryBase<CoaDataEntity, Integer> {
    public Optional<CoaDataEntity> findByUseCoaAndCoaIdAndCoaScore(Boolean useCoa, Integer coaId, byte coaScore) {
        return find("useCoa = ?1 AND  coaId= ?2 AND coaScore= ?3", useCoa, coaId, coaScore).firstResultOptional();
    }

    public List<CoaDataEntity> findByIds(List<Integer> coaDataIds) {
        return find("SELECT DISTINCT e FROM CoaDataEntity e WHERE e.id IN ?1", coaDataIds)
                .list();
    }
}
