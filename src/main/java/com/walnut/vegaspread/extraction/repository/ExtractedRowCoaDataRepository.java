package com.walnut.vegaspread.extraction.repository;

import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.primarykey.ExtractedTableRowCoaDataPkId;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;

@ApplicationScoped
public class ExtractedRowCoaDataRepository implements PanacheRepositoryBase<ExtractedTableRowCoaDataJoinEntity,
        ExtractedTableRowCoaDataPkId> {

    private final EntityManager em;

    public ExtractedRowCoaDataRepository(EntityManager em) {
        this.em = em;
    }

    public Optional<ExtractedTableRowCoaDataJoinEntity> findByRowOptional(Integer tableId, Short rowId) {
        return find(
                "extractedTableRowCoaDataPkId.tableRowPkId.tableId = ?1 AND extractedTableRowCoaDataPkId.tableRowPkId" +
                        ".rowId = ?2",
                tableId, rowId).firstResultOptional();
    }

    public long deleteMappedCoaData(Integer tableId, Short rowId) {
        return em.createNativeQuery(
                        "DELETE FROM " + ExtractedTableRowCoaDataJoinEntity.EXTRACTED_TABLE_ROW_COA_DATA_JOIN_TABLE_NAME
                                + " WHERE "
                                + ExtractedTableRowCoaDataJoinEntity.TABLE_ID_COL_NAME + "= :tableId"
                                + " AND " + ExtractedTableRowCoaDataJoinEntity.ROW_ID_COL_NAME + "= :rowId")
                .setParameter("tableId", tableId)
                .setParameter("rowId", rowId)
                .executeUpdate();
    }

    public long deleteMappedCoaDataForTables(List<Integer> tableIds) {
        return em.createNativeQuery(
                        "DELETE FROM " + ExtractedTableRowCoaDataJoinEntity.EXTRACTED_TABLE_ROW_COA_DATA_JOIN_TABLE_NAME
                                + " WHERE "
                                + ExtractedTableRowCoaDataJoinEntity.TABLE_ID_COL_NAME + " IN :tableIds")
                .setParameter("tableIds", tableIds)
                .executeUpdate();
    }

    public List<ExtractedTableRowCoaDataJoinEntity> findByTableIds(List<Integer> tableIds) {
        return find(
                "extractedTableRowCoaDataPkId.tableRowPkId.tableId IN ?1", tableIds).list();
    }

    public void saveRowCoaJoin(List<ExtractedTableRowCoaDataJoinEntity> extractedTableRowCoaDataJoinEntities) {
        for (ExtractedTableRowCoaDataJoinEntity extractedTableRowCoaDataJoinEntity :
                extractedTableRowCoaDataJoinEntities) {
            em.createNativeQuery(
                            "INSERT INTO" + StringUtils.SPACE + ExtractedTableRowCoaDataJoinEntity.EXTRACTED_TABLE_ROW_COA_DATA_JOIN_TABLE_NAME + StringUtils.SPACE +
                                    "(" + ExtractedTableRowCoaDataJoinEntity.TABLE_ID_COL_NAME +
                                    "," + ExtractedTableRowCoaDataJoinEntity.ROW_ID_COL_NAME +
                                    "," + ExtractedTableRowCoaDataPkId.COA_DATA_COL_NAME +
                                    "," + ExtractedTableRowCoaDataJoinEntity.EXPLAINABILITY_ID_COL_NAME + ")" + StringUtils.SPACE +
                                    "VALUES (?,?,?,?)" +
                                    " ON DUPLICATE KEY UPDATE " +
                                    ExtractedTableRowCoaDataJoinEntity.EXPLAINABILITY_ID_COL_NAME + " = VALUES(" + ExtractedTableRowCoaDataJoinEntity.EXPLAINABILITY_ID_COL_NAME + ")"
                    )
                    .setParameter(1, extractedTableRowCoaDataJoinEntity.getExtractedTableRowCoaDataPkId()
                            .getTableRowPkId()
                            .getTableId())
                    .setParameter(2, extractedTableRowCoaDataJoinEntity.getExtractedTableRowCoaDataPkId()
                            .getTableRowPkId()
                            .getRowId())
                    .setParameter(3,
                            extractedTableRowCoaDataJoinEntity.getExtractedTableRowCoaDataPkId().getCoaDataId())
                    .setParameter(4,
                            extractedTableRowCoaDataJoinEntity.getExplainability() == null ? null :
                                    extractedTableRowCoaDataJoinEntity.getExplainability()
                                            .getId())
                    .executeUpdate();
        }
    }

    public List<Integer> findTableIdsWithCoaMappingsInList(List<Integer> tableIds) {
        return find(
                "SELECT DISTINCT e.extractedTableRowCoaDataPkId.tableRowPkId.tableId " +
                        "FROM ExtractedTableRowCoaDataJoinEntity e " +
                        "WHERE e.extractedTableRowCoaDataPkId.tableRowPkId.tableId IN ?1",
                tableIds).project(Integer.class).list();
    }
}