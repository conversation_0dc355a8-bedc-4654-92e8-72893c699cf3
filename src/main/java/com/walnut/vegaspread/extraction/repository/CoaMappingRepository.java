package com.walnut.vegaspread.extraction.repository;

import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@ApplicationScoped
public class CoaMappingRepository implements PanacheRepositoryBase<CoaMappingEntity, Integer> {

    public List<CoaMappingEntity> findByDocIdsAndTableTypeId(List<UUID> docIds, Integer tableTypeId) {
        if (tableTypeId == null) {
            return list("docId IN ?1", docIds);
        }
        return list("docId IN ?1 AND tableTypeId = ?2", docIds, tableTypeId);
    }

    public long deleteByDocIds(List<UUID> docIds) {
        return delete("docId IN ?1", docIds);
    }

    public List<CoaMappingEntity> findByIds(List<Integer> ids) {
        return list("id IN ?1", ids);
    }

    public List<CoaMappingEntity> findByTableTypeIdInOrFsHeaderLike(List<Integer> tableTypeIds, String fsHeader) {
        List<String> conditions = new ArrayList<>();
        Map<String, Object> params = new HashMap<>();

        if (!tableTypeIds.isEmpty()) {
            conditions.add("tableTypeId IN :tableTypeIds");
            params.put("tableTypeIds", tableTypeIds);
        }

        if (fsHeader != null) {
            conditions.add("fsHeader LIKE :fsHeader");
            params.put("fsHeader", "%" + fsHeader + "%");
        }

        if (conditions.isEmpty()) {
            return List.of();
        }

        String query = String.join(" OR ", conditions);
        return find(query, params).list();
    }

    public long deleteByDocId(UUID docId) {
        return delete("docId = ?1", docId);
    }
}
