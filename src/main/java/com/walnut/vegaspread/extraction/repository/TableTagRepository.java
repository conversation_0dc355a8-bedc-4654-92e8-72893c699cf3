package com.walnut.vegaspread.extraction.repository;

import com.walnut.vegaspread.extraction.entity.TableTagEntity;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

@ApplicationScoped
public class TableTagRepository implements PanacheRepositoryBase<TableTagEntity, Integer> {
    public long deleteByIds(List<Integer> tagIds) {
        return delete("id in ?1", tagIds);
    }

    public List<TableTagEntity> findByIds(List<Integer> tagIds) {
        return list("id in ?1", tagIds);
    }

    public TableTagEntity findByTag(String tag) {
        return find("tag = ?1", tag).firstResult();
    }
}
