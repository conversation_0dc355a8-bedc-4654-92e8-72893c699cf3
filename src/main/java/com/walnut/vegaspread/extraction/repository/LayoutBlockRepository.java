package com.walnut.vegaspread.extraction.repository;

import com.walnut.vegaspread.extraction.converter.LayoutBlockId;
import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.model.BlockTypeEnum;
import com.walnut.vegaspread.extraction.model.DbBlock;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;
import java.util.UUID;

import static com.walnut.vegaspread.extraction.utils.Config.NA_TAG_ID;

@ApplicationScoped
public class LayoutBlockRepository implements PanacheRepositoryBase<LayoutBlockEntity, Integer> {

    public List<LayoutBlockEntity> findAllByIds(List<Integer> blockIds) {
        return list("blockId IN ?1", blockIds);
    }

    public List<LayoutBlockEntity> findAllByDocIdAndBlockType(UUID docId, BlockTypeEnum blockType) {
        return list("docId = ?1 AND blockType = ?2", docId, blockType);
    }

    public List<LayoutBlockEntity> findAllByDocIdAndBlockTypeAndPageNum(UUID docId, BlockTypeEnum blockType,
                                                                        Integer pageNum) {
        return list("docId = ?1 AND blockType = ?2 AND pageNum = ?3", docId, blockType, pageNum);
    }

    public List<DbBlock.BlockTagOnly> findBlocksWithTag(UUID docId, BlockTypeEnum blockType) {
        return find("docId = ?1 AND blockType = ?2 AND tag.id != ?3", docId, blockType, NA_TAG_ID)
                .stream().map(block -> new DbBlock.BlockTagOnly(
                        block.getBlockId(),
                        block.getTag().getId(),
                        block.getTag().getTag(),
                        block.getPageNum()
                )).toList();
    }

    public List<LayoutBlockId> findAllByDocIdWithCoaId(UUID docId) {
        String query = "SELECT DISTINCT lb.blockId FROM LayoutBlockEntity lb " +
                "INNER JOIN ExtractedTableRowCoaDataJoinEntity et ON lb.blockId = et.extractedTableRowCoaDataPkId" +
                ".tableRowPkId.tableId " +
                "WHERE lb.docId = ?1 AND lb.tag.id != " + NA_TAG_ID;
        return find(query, docId).project(LayoutBlockId.class).list();
    }

    public long deleteAllByBlockIds(List<Integer> blockIds) {
        return delete("blockId IN ?1", blockIds);
    }

    public List<Integer> findAllBlockIdsByDocId(UUID docId) {
        return find("docId = ?1", docId).project(LayoutBlockId.class).stream().map(LayoutBlockId::getBlockId).toList();
    }

    public List<LayoutBlockEntity> findAllBlocksByDocId(UUID docId) {
        return list("docId = ?1", docId);
    }

    public List<LayoutBlockEntity> findAllBlocksByDocIdAndTag(UUID docId, String tag) {
        return list("docId = ?1 AND tag.tag = ?2", docId, tag);
    }

    public List<LayoutBlockEntity> findAllTaggedTables(UUID docId) {
        return list("docId = ?1 AND tag.id != ?2", docId, NA_TAG_ID);
    }
}
