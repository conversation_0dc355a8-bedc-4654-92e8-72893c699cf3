package com.walnut.vegaspread.extraction.repository;

import com.walnut.vegaspread.extraction.entity.SubtotalMappingEntity;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@ApplicationScoped
public class SubtotalMappingRepository implements PanacheRepositoryBase<SubtotalMappingEntity, TableRowPkId> {
    public List<SubtotalMappingEntity> getByTableId(Integer tableId) {
        return find("id.tableId = ?1", tableId).list();
    }

    public Optional<SubtotalMappingEntity> findByDocIdAndSubtotalId(UUID docId, Integer subtotalId) {
        return find("docId = ?1 AND subtotal.id = ?2", docId, subtotalId)
                .stream()
                .findFirst();
    }

    public long deleteMappingForSubtotalId(SubtotalMappingEntity subtotalMappingEntity) {
        return delete("docId = ?1 AND subtotal.id = ?2",
                subtotalMappingEntity.getDocId(),
                subtotalMappingEntity.getSubtotal().getId());
    }

    public List<SubtotalMappingEntity> getByDocId(UUID docId) {
        return find("docId = ?1", docId).list();
    }
}
