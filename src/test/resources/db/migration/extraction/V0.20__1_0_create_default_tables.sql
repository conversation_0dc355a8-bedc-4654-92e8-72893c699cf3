CREATE SCHEMA IF NOT EXISTS ${extraction};
SET SCHEMA ${extraction};
CREATE TABLE layout_block
(
    `block_id`   INT                    NOT NULL AUTO_INCREMENT,
    `x_max`      SMALLINT               NOT NULL,
    `x_min`      SMALLINT               NOT NULL,
    `y_max`      SMALLINT               NOT NULL,
    `y_min`      SMALLINT               NOT NULL,
    `block_type` ENUM ('CAPTION','FOOTNOTE','FORMULA',
        'LIST_ITEM','PAGE_FOOTER','PAGE_HEADER',
        'PICTURE','SECTION_HEADER','TABLE',
        'TEXT','TITLE','LIST','FIGURE') NOT NULL,
    `doc_id`     BINARY(16)             NOT NULL,
    `page_num`   SMALLINT               NOT NULL,
    `score`      TINYINT                NOT NULL,
    `tag`        VARCHAR(255) DEFAULT NULL,
    PRIMARY KEY (`block_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

CREATE TABLE extracted_table_row
(
    `table_id`     INT      NOT NULL,
    `row_id`       TINYINT  NOT NULL,
    `coa_id`       INT          DEFAULT NULL,
    `coa_score`    TINYINT      DEFAULT 0,
    `nta_table_id` INT          DEFAULT NULL,
    `parent_text`  VARCHAR(255) DEFAULT NULL,
    `header_ids`   VARCHAR(50)  DEFAULT NULL,
    `cells_text`   TEXT     NOT NULL,
    `x_max`        SMALLINT NOT NULL,
    `x_min`        SMALLINT NOT NULL,
    `y_max`        SMALLINT NOT NULL,
    `y_min`        SMALLINT NOT NULL,
    `score`        TINYINT  NOT NULL,
    PRIMARY KEY (`row_id`, `table_id`),
    KEY `fk_extracted_table_row_table_id` (`table_id`),
    KEY `fk_extracted_table_row_nta_table_id` (`nta_table_id`),
    CONSTRAINT `fk_extracted_table_row_table_id` FOREIGN KEY (`table_id`)
        REFERENCES `layout_block` (`block_id`) ON DELETE CASCADE,
    CONSTRAINT `fk_extracted_table_row_nta_table_id` FOREIGN KEY (`nta_table_id`) REFERENCES `layout_block` (`block_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

CREATE TABLE extracted_table_header
(
    `header_id` TINYINT  NOT NULL,
    `table_id`  INT      NOT NULL,
    `text`      TEXT     NOT NULL,
    `x_max`     SMALLINT NOT NULL,
    `x_min`     SMALLINT NOT NULL,
    `y_max`     SMALLINT NOT NULL,
    `y_min`     SMALLINT NOT NULL,
    `score`     TINYINT  NOT NULL,
    PRIMARY KEY (`header_id`, `table_id`),
    KEY `fk_extracted_table_header_table_id` (`table_id`),
    CONSTRAINT `fk_extracted_table_header_table_id` FOREIGN KEY (`table_id`)
        REFERENCES `layout_block` (`block_id`) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;
