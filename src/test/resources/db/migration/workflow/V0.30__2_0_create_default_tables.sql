CREATE SCHEMA IF NOT EXISTS ${workflow};
SET SCHEMA ${workflow};
CREATE TABLE entity_name
(
    `entity_id`    INT          NOT NULL AUTO_INCREMENT,
    `name`         VARCHAR(255) NOT NULL,
    `created_by`   VARCHAR(255) NOT NULL,
    `created_time` DATETIME(6)  NOT NULL,
    PRIMARY KEY (`entity_id`),
    UNIQUE KEY `uk_entity_name` (`name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

CREATE TABLE industry
(
    `industry_id`   INT          NOT NULL AUTO_INCREMENT,
    `industry_name` VARCHAR(255) NOT NULL,
    `created_by`    VARCHAR(255) NOT NULL,
    `created_time`  DATETIME(6)  NOT NULL,
    PRIMARY KEY (`industry_id`),
    UNIQUE KEY `uk_industry_name` (`industry_name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

CREATE TABLE region
(
    `region_id`    INT          NOT NULL AUTO_INCREMENT,
    `region_name`  VARCHAR(255) NOT NULL,
    `created_by`   VARCHAR(255) NOT NULL,
    `created_time` DATETIME(6)  NOT NULL,
    PRIMARY KEY (`region_id`),
    UNIQUE KEY `uk_region_name` (`region_name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

CREATE TABLE spreading_task
(
    `spread_id`          INT          NOT NULL AUTO_INCREMENT,
    `entity_name_id`     INT          NOT NULL,
    `industry_id`        INT          NOT NULL,
    `region_id`          INT          NOT NULL,
    `client_name`        VARCHAR(255) DEFAULT 'walnut',
    `created_by`         VARCHAR(255) NOT NULL,
    `created_time`       DATETIME(6)  NOT NULL,
    `last_modified_by`   VARCHAR(255) NOT NULL,
    `last_modified_time` DATETIME(6)  NOT NULL,
    PRIMARY KEY (`spread_id`),
    KEY `fk_spread_entity_name_id` (`entity_name_id`),
    KEY `fk_spread_industry_id` (`industry_id`),
    KEY `fk_spread_region_id` (`region_id`),
    CONSTRAINT `fk_spread_entity_name_id` FOREIGN KEY (`entity_name_id`) REFERENCES `entity_name` (`entity_id`),
    CONSTRAINT `fk_spread_industry_id` FOREIGN KEY (`industry_id`) REFERENCES `industry` (`industry_id`),
    CONSTRAINT `fk_spread_region_id` FOREIGN KEY (`region_id`) REFERENCES `region` (`region_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

CREATE TABLE document
(
    `doc_id`             BINARY(16)                                       NOT NULL,
    `period`             DATE                               DEFAULT NULL,
    `spread_level`       ENUM ('CONSOLIDATED','STANDALONE') DEFAULT NULL,
    `spread_id`          INT                                DEFAULT NULL,
    `file_name`          VARCHAR(255)                                     NOT NULL,
    `file_path`          VARCHAR(255)                                     NOT NULL,
    `file_size`          INT                                              NOT NULL,
    `dpi`                SMALLINT                           DEFAULT NULL,
    `mapped_items`       SMALLINT                           DEFAULT NULL,
    `is_digital`         BOOLEAN                            DEFAULT TRUE,
    `ocr_score`          TINYINT                            DEFAULT 100,
    `denomination`       VARCHAR(50)                        DEFAULT NULL,
    `currency`           VARCHAR(255)                       DEFAULT NULL,
    `status`             ENUM ('COMPLETED','CREATED','DRAFT','PROCESSING',
        'TO_REVIEW','UNDER_REVIEW_CLIENT','UNDER_REVIEW_WALNUT','FAILED') NOT NULL,
    `status_text`        VARCHAR(255)                                     NOT NULL,
    `created_by`         VARCHAR(255)                                     NOT NULL,
    `created_time`       DATETIME(6)                                      NOT NULL,
    `last_modified_by`   VARCHAR(255)                                     NOT NULL,
    `last_modified_time` DATETIME(6)                                      NOT NULL,
    PRIMARY KEY (`doc_id`),
    UNIQUE KEY `uk_file_path` (`file_path`),
    KEY `fk_document_spread_id` (spread_id),
    CONSTRAINT `fk_document_spread_id` FOREIGN KEY (`spread_id`) REFERENCES `spreading_task` (`spread_id`) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;
-- tables for processor

CREATE TABLE identifier_keyword
(
    `kw_id`        INT          NOT NULL AUTO_INCREMENT,
    `created_by`   VARCHAR(255) NOT NULL,
    `created_time` DATETIME(6)  NOT NULL,
    `category`     VARCHAR(20)  NOT NULL,
    `kw_text`      VARCHAR(150) NOT NULL,
    PRIMARY KEY (`kw_id`),
    UNIQUE KEY `uk_identifier_keyword_kw_text_category` (`kw_text`, `category`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

CREATE TABLE wise_service
(
    `name` VARCHAR(50)  NOT NULL,
    `url`  VARCHAR(255) NOT NULL,
    PRIMARY KEY (`name`),
    UNIQUE KEY `wise_service_primary_key_name` (`name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;
