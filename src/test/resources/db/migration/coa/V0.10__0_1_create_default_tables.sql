CREATE SCHEMA IF NOT EXISTS ${coa};
SET SCHEMA ${coa};
CREATE TABLE coa_list
(
    `coa_id`          INT          NOT NULL AUTO_INCREMENT,
    `coa_description` VARCHAR(255) NOT NULL,
    `coa_text`        VARCHAR(255) NOT NULL,
    `client_name`     VARCHAR(255) NOT NULL,
    `lvl1_category`   VARCHAR(255) NOT NULL DEFAULT '',
    `is_active`       BOOLEAN      NOT NULL,
    PRIMARY KEY (`coa_id`),
    UNIQUE KEY `uk_coa_list_client_coa_text` (`coa_text`, `client_name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;


CREATE TABLE coa_task
(
    `id`            INT                                      NOT NULL AUTO_INCREMENT,
    `spread_id`     INT                                      NOT NULL,
    `block_id`      INT                                      NOT NULL,
    `row_id`        TINYINT                                  NOT NULL,
    `table_type`    VARCHAR(255)                             NOT NULL,
    `row_parent`    VARCHAR(255) DEFAULT NULL,
    `text`          VARCHAR(255)                             NOT NULL,
    `fs_header`     VARCHAR(255) DEFAULT NULL,
    `fs_text`       VARCHAR(255)                             NOT NULL,
    `client_name`   VARCHAR(255)                             NOT NULL,
    `coa_id`        INT                                      NOT NULL,
    `instances`     TINYINT      DEFAULT 1,
    `status`        ENUM ('TO_REVIEW','APPROVED','REJECTED') NOT NULL,
    `created_by`    VARCHAR(255)                             NOT NULL,
    `created_time`  DATETIME(6)                              NOT NULL,
    `reviewed_by`   VARCHAR(255) DEFAULT NULL,
    `reviewed_time` DATETIME(6)  DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `fk_coa_mapping_task_coa_id` (`coa_id`),
    CONSTRAINT `fk_coa_mapping_task_coa_id` FOREIGN KEY (`coa_id`) REFERENCES `coa_list` (`coa_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;
