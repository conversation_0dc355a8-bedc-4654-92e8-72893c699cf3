SET SCHEMA ${coa};
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (1, 'NA', 'NA', 'common', '', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (2, '1.BS.CA.Cash', '1.BS.CA.Cash', 'walnut', 'Current Assets', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (7, '1.BS.CA.Accounts Receivable-Trade', '1.BS.CA.Accounts Receivable-Trade', 'walnut', 'Current Assets', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (25, '1.BS.CA.Inventory', '1.BS.CA.Inventory', 'walnut', 'Current Assets', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (34, '1.BS.CA.Operating Current Assets', '1.BS.CA.Operating Current Assets', 'walnut', 'Current Assets', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (44, '2.BS.NCA.Land', '2.BS.NCA.Land', 'walnut', 'Non Current Assets', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (46, '2.BS.NCA.Buildings & Improvements', '2.BS.NCA.Buildings & Improvements', 'walnut', 'Non Current Assets',
        1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (47, '2.BS.NCA.Other Depreciable Fixed Assets', '2.BS.NCA.Other Depreciable Fixed Assets', 'walnut',
        'Non Current Assets', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (48, ' Plant & Equipment', '2.BS.NCA.Machinery', 'walnut', 'Non Current Assets', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (50, '2.BS.NCA.Leasehold Improvements', '2.BS.NCA.Leasehold Improvements', 'walnut', 'Non Current Assets', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (51, '2.BS.NCA.Transportation Assets', '2.BS.NCA.Transportation Assets', 'walnut', 'Non Current Assets', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (55, '2.BS.NCA.Investment Properties', '2.BS.NCA.Investment Properties', 'walnut', 'Non Current Assets', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (61, '2.BS.NCA.less: Accumulated Depreciation', '2.BS.NCA.less: Accumulated Depreciation', 'walnut',
        'Non Current Assets', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (75, '2.BS.NCA.Investments', '2.BS.NCA.Investments', 'walnut', 'Non Current Assets', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (94, '3.BS.CL.Short Term Loans-Bank', '3.BS.CL.Short Term Loans-Bank', 'walnut', 'Current Liabilities', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (99, '3.BS.CL.Trust Receipts/Bills Payable/Factoring Advances',
        '3.BS.CL.Trust Receipts/Bills Payable/Factoring Advances', 'walnut', 'Current Liabilities', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (107, '3.BS.CL.Accounts Payable-Trade', '3.BS.CL.Accounts Payable-Trade', 'walnut', 'Current Liabilities', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (120, '3.BS.CL.Income Taxes Payable', '3.BS.CL.Income Taxes Payable', 'walnut', 'Current Liabilities', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (126, '3.BS.CL.Other Payables-Operating', '3.BS.CL.Other Payables-Operating', 'walnut', 'Current Liabilities',
        1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (163, '3.BS.NCL.Deferred Income Tax Liabs', '3.BS.NCL.Deferred Income Tax Liabs', 'walnut',
        'Non Current Liabilities', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (172, '5.BS.NW.Common Stock', '5.BS.NW.Common Stock', 'walnut', 'Net Worth', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (184, '6.IS.Sales/Revenues', '6.IS.Sales/Revenues', 'walnut', 'Income Statement.Net Sales', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (193, '6.IS.Cost of Sales', '6.IS.Cost of Sales', 'walnut', 'Income Statement.COS', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (207, '6.IS.General & Admin Expense', '6.IS.General & Admin Expense', 'walnut',
        'Income Statement.Operating Expense', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (212, '6.IS.Other Operating Expense', '6.IS.Other Operating Expense', 'walnut',
        'Income Statement.Operating Expense', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (214, '6.IS.Professional and Legal Fees', '6.IS.Professional and Legal Fees', 'walnut',
        'Income Statement.Operating Expense', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (216, '6.IS.Personnel Expense', '6.IS.Personnel Expense', 'walnut', 'Income Statement.Operating Expense', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (217, '6.IS.Lease/Rent Expense', '6.IS.Lease/Rent Expense', 'walnut', 'Income Statement.Operating Expense', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (219, '6.IS.Depreciation', '6.IS.Depreciation', 'walnut', 'Income Statement.Operating Expense', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (223, '6.IS.Advertising & Promotion Expenses', '6.IS.Advertising & Promotion Expenses', 'walnut',
        'Income Statement.Operating Expense', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (227, '6.IS.Repairs and Maintenance', '6.IS.Repairs and Maintenance', 'walnut',
        'Income Statement.Operating Expense', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (228, '6.IS.Bank Charges', '6.IS.Bank Charges', 'walnut', 'Income Statement.Operating Expense', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (230, '6.IS.Other Taxes', '6.IS.Other Taxes', 'walnut', 'Income Statement.Operating Expense', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (239, '6.IS.Interest Expenses', '6.IS.Interest Expenses', 'walnut', 'Income Statement.Interest Expense', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (242, '6.IS.Capitalized Interest', '6.IS.Capitalized Interest', 'walnut', 'Income Statement.Interest Expense',
        1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (250, '6.IS.Other Income', '6.IS.Other Income', 'walnut', 'Income Statement.Other Income (Expense)', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (262, '6.IS.Gain (Loss) on Sale of Fixed Assets', '6.IS.Gain (Loss) on Sale of Fixed Assets', 'walnut',
        'Income Statement.Other Income (Expense)', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (264, '6.IS.Unrealized Gain (Loss) on Financial Assets', '6.IS.Unrealized Gain (Loss) on Financial Assets',
        'walnut', 'Income Statement.Other Income (Expense)', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (271, '6.IS.Current Income Tax', '6.IS.Current Income Tax', 'walnut', 'Income Statement.Tax', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (561, '6.IS.Gross Sales - Trading', '6.IS.Gross Sales - Trading', 'walnut', 'Income Statement.Net Sales', 1);
INSERT INTO coa_list
(coa_id, coa_description, coa_text, client_name, lvl1_category, is_active)
VALUES (603, '1.BS.CA.Provision: Slow Moving/Objects (-)', '1.BS.CA.Provision: Slow Moving/Objects (-)', 'WALNUT',
        'Current Assets', 1);