package com.walnut.vegaspread.coa.resource;

import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.model.CoaEntityDto;
import com.walnut.vegaspread.coa.model.CoaListDto;
import com.walnut.vegaspread.coa.model.CoaUploadBean;
import com.walnut.vegaspread.coa.model.SortDto;
import com.walnut.vegaspread.coa.repository.CoaRepository;
import com.walnut.vegaspread.coa.repository.Lvl1CategoryRepository;
import com.walnut.vegaspread.coa.service.ExchangeService;
import com.walnut.vegaspread.common.model.audit.coa.CoaItemAuditDto;
import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import com.walnut.vegaspread.common.roles.Roles;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.MediaType;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.IntStream;

import static com.walnut.vegaspread.coa.resource.CommonTestUtils.CLIENT_NAME;
import static com.walnut.vegaspread.coa.resource.CommonTestUtils.NAME;
import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@QuarkusTest
@TestHTTPEndpoint(CoaResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CoaResourceTest {

    private static final Logger log = LoggerFactory.getLogger(CoaResourceTest.class);
    @InjectMock
    ExchangeService exchangeService;

    @Inject
    Flyway flyway;

    @Inject
    CoaRepository coaRepository;

    @Inject
    EntityManager entityManager;

    @Inject
    Lvl1CategoryRepository lvl1CategoryRepository;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Transactional
    List<Lvl1CategoryEntity> addNewCategory(List<String> categories) {
        List<Lvl1CategoryEntity> lvl1CategoryEntities = categories.stream().map(category -> Lvl1CategoryEntity.builder()
                .category(category)
                .createdBy("user")
                .createdTime(LocalDateTime.now())
                .lastModifiedBy("user")
                .lastModifiedTime(LocalDateTime.now())
                .build()).toList();
        lvl1CategoryRepository.persist(lvl1CategoryEntities);
        return lvl1CategoryEntities;
    }

    @Transactional
    List<CoaEntity> addCoaEntities() {
        List<Lvl1CategoryEntity> lvl1CategoryEntities = addNewCategory(
                IntStream.range(1, 11).mapToObj(id -> "category" + id).toList());

        List<CoaEntity> coaEntities = IntStream.range(1, 11).mapToObj(id -> CoaEntity.builder()
                .coaText("coaText" + id)
                .clientName(CLIENT_NAME)
                .coaDescription("coaDesc" + id)
                .lvl1Category(lvl1CategoryEntities.get(id - 1))
                .isActive(id % 2 == 0)
                .sign(id % 2 == 0)
                .build()).toList();

        coaRepository.persist(coaEntities);
        return coaEntities;
    }

    @Transactional
    List<CoaEntityDto.Create> createCoaItemDtos() {
        List<CoaEntityDto.Create> coaEntityDtos = new ArrayList<>();
        List<Lvl1CategoryEntity> lvl1CategoryEntities = addNewCategory(
                IntStream.range(1, 3).mapToObj(id -> "category" + id).toList());
        CoaEntityDto.Create dto1 = new CoaEntityDto.Create("coaText1", "coaDesc1", lvl1CategoryEntities.get(0).getId(),
                Boolean.TRUE,
                Boolean.TRUE);
        CoaEntityDto.Create dto2 = new CoaEntityDto.Create("coaText2", "coaDesc2", lvl1CategoryEntities.get(1).getId(),
                Boolean.TRUE,
                Boolean.TRUE);
        CoaEntityDto.Create dto3 = new CoaEntityDto.Create("coaText3", "coaDesc3", lvl1CategoryEntities.get(0).getId(),
                Boolean.TRUE,
                Boolean.TRUE);
        coaEntityDtos.add(dto1);
        coaEntityDtos.add(dto2);
        coaEntityDtos.add(dto3);
        return coaEntityDtos;
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.CREATE_NEW_COA)
    void testCreate() {
        List<CoaEntityDto.Create> coaEntityDtos = createCoaItemDtos();
        List<CoaEntity> insertedEntities = given()
                .contentType(ContentType.JSON).queryParam("clientName", CLIENT_NAME).body(coaEntityDtos)
                .when().post("/create")
                .then().statusCode(200)
                .extract()
                .body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(coaEntityDtos.size(), insertedEntities.size());

        for (int i = 0; i < insertedEntities.size(); i++) {
            CoaEntity entity = insertedEntities.get(i);
            CoaEntityDto.Create dto = coaEntityDtos.get(i);
            Assertions.assertEquals(i + 1, entity.coaId);
            Assertions.assertEquals(dto.coaText(), entity.coaText);
            Assertions.assertEquals(dto.coaDescription(), entity.coaDescription);
            Assertions.assertEquals(CLIENT_NAME, entity.clientName);
            Assertions.assertEquals(dto.lvl1CategoryId(), entity.getLvl1Category().getId());
            Assertions.assertEquals(dto.isActive(), entity.isActive);
        }
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.SUPERADMIN)
    void testUpdateWithNullDtos() {
        List<CoaEntity> updatedEntities = given()
                .contentType(ContentType.JSON)
                .when().patch("/update")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });
        Assertions.assertTrue(updatedEntities.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.SUPERADMIN)
    void testUpdateWithEmptyDtoList() {
        List<CoaEntity> updatedEntities = given()
                .contentType(ContentType.JSON).body(Collections.emptyList())
                .when().patch("/update")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });
        Assertions.assertTrue(updatedEntities.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.SUPERADMIN)
    void testUpdateWithInvalidCoaId() {
        // Reset the mock before the test
        Mockito.reset(exchangeService);
        addCoaEntities();
        Lvl1CategoryEntity lvl1Category = lvl1CategoryRepository.findByCategories(List.of("category1")).get(0);
        CoaEntityDto.Update coaEntityUpdate = new CoaEntityDto.Update(100, Optional.of("Invalid COA Id"),
                Optional.of("Invalid COA id"), Optional.of(lvl1Category.getId()), Optional.of(true), Optional.of(true));
        List<CoaEntityDto.Update> coaEntityUpdateDtos = new ArrayList<>();
        coaEntityUpdateDtos.add(coaEntityUpdate);

        List<CoaEntity> updatedEntities = given()
                .contentType(ContentType.JSON).body(coaEntityUpdateDtos)
                .when().patch("/update")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(updatedEntities.isEmpty());
        verify(exchangeService, times(1)).coaItemAuditForUpdate(Collections.emptyList());
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.SUPERADMIN)
    void testUpdate() {
        int coaId = 1;
        List<CoaEntity> coaEntities = addCoaEntities();
        CoaEntity coaEntityToUpdate = coaEntities.get(coaId - 1);
        Lvl1CategoryEntity lvl1Category = addNewCategory(List.of("New Category")).get(0);
        CoaEntityDto.Update coaEntityUpdate = new CoaEntityDto.Update(coaId, Optional.of("New COA Text"),
                Optional.of("New COA Description"), Optional.of(lvl1Category.getId()), Optional.of(true),
                Optional.of(true));
        List<CoaEntityDto.Update> coaEntityUpdateDtos = new ArrayList<>();
        coaEntityUpdateDtos.add(coaEntityUpdate);

        List<CoaItemAuditDto.Update> coaItemAudits = new ArrayList<>();
        CoaItemAuditDto.Update coaItemAudit1 = new CoaItemAuditDto.Update(coaId, CoaEntity.COA_TEXT_COL_NAME,
                coaEntityToUpdate.coaText, coaEntityUpdate.coaText().get());
        coaItemAudits.add(coaItemAudit1);
        CoaItemAuditDto.Update coaItemAudit2 = new CoaItemAuditDto.Update(coaId, CoaEntity.IS_ACTIVE_COL_NAME,
                coaEntityToUpdate.isActive.toString(), coaEntityUpdate.isActive().get().toString());
        coaItemAudits.add(coaItemAudit2);
        CoaItemAuditDto.Update coaItemAudit3 = new CoaItemAuditDto.Update(coaId, CoaEntity.SIGN_COL_NAME,
                coaEntityToUpdate.getSign().toString(), coaEntityUpdate.sign().get().toString());
        coaItemAudits.add(coaItemAudit3);

        List<CoaEntity> updatedEntities = given()
                .contentType(ContentType.JSON).body(coaEntityUpdateDtos)
                .when().patch("/update")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        verify(exchangeService, times(1)).coaItemAuditForUpdate(coaItemAudits);
        CoaEntity coaEntity = updatedEntities.get(0);

        Assertions.assertEquals(coaEntityUpdate.coaId(), coaEntity.coaId);
        Assertions.assertEquals(coaEntityUpdate.coaText().get(), coaEntity.coaText);
        Assertions.assertEquals(coaEntityUpdate.coaDescription().get(), coaEntity.coaDescription);
        Assertions.assertEquals(coaEntityUpdate.lvl1CategoryId().get(), coaEntity.getLvl1Category().getId());
        Assertions.assertEquals(coaEntityUpdate.isActive().get(), coaEntity.isActive);
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.SUPERADMIN)
    void testUpdateWithNullFields() {
        int coaId = 1;
        List<CoaEntity> coaEntities = addCoaEntities();
        CoaEntity coaEntityToUpdate = coaEntities.get(coaId - 1);

        List<CoaEntityDto.Update> coaEntityUpdateDtos = new ArrayList<>();
        CoaEntityDto.Update coaEntityUpdate = new CoaEntityDto.Update(coaId, null,
                null, null, null, null);
        coaEntityUpdateDtos.add(coaEntityUpdate);

        List<CoaEntity> updatedEntities = given()
                .contentType(ContentType.JSON).body(coaEntityUpdateDtos)
                .when().patch("/update")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        verify(exchangeService, times(1)).coaItemAuditForUpdate(Collections.emptyList());
        CoaEntity updatedCoaEntity = updatedEntities.get(0);

        Assertions.assertEquals(coaEntityToUpdate.coaId, updatedCoaEntity.coaId);
        Assertions.assertEquals(coaEntityToUpdate.coaText, updatedCoaEntity.coaText);
        Assertions.assertEquals(coaEntityToUpdate.coaDescription, updatedCoaEntity.coaDescription);
        Assertions.assertEquals(coaEntityToUpdate.getLvl1Category().getId(),
                updatedCoaEntity.getLvl1Category().getId());
        Assertions.assertEquals(coaEntityToUpdate.isActive, updatedCoaEntity.isActive);
    }

    @Test
    @TestSecurity(user = NAME)
    void testListActive() {
        List<CoaEntity> coaEntities = addCoaEntities();

        List<CoaEntity> activeCoaEntites = coaEntities.stream().filter(CoaEntity::getIsActive).toList();

        List<CoaItemDto> entityList = given()
                .queryParam("clientName", CLIENT_NAME)
                .when().get("/list")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });
        for (int i = 0; i < entityList.size(); i++) {
            Assertions.assertEquals(activeCoaEntites.get(i).getCoaId(), entityList.get(i).coaId());
            Assertions.assertEquals(activeCoaEntites.get(i).getCoaText(), entityList.get(i).coaText());
            Assertions.assertEquals(activeCoaEntites.get(i).getCoaDescription(), entityList.get(i).coaDescription());
            Assertions.assertEquals(activeCoaEntites.get(i).getClientName(), entityList.get(i).clientName());
            Assertions.assertEquals(activeCoaEntites.get(i).getLvl1Category().getCategory(),
                    entityList.get(i).lvl1CategoryName());
            Assertions.assertEquals(activeCoaEntites.get(i).getIsActive(), entityList.get(i).isActive());
            Assertions.assertEquals(activeCoaEntites.get(i).getSign(), entityList.get(i).sign());
        }
    }

    @Test
    @TestSecurity(user = NAME)
    void testListNotActive() {
        List<CoaEntity> coaEntities = addCoaEntities();

        List<CoaItemDto> entityList = given()
                .when()
                .queryParam("clientName", CLIENT_NAME)
                .queryParam("onlyActive", false)
                .get("/list")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        for (int i = 0; i < entityList.size(); i++) {
            Assertions.assertEquals(coaEntities.get(i).getCoaId(), entityList.get(i).coaId());
            Assertions.assertEquals(coaEntities.get(i).getCoaText(), entityList.get(i).coaText());
            Assertions.assertEquals(coaEntities.get(i).getCoaDescription(), entityList.get(i).coaDescription());
            Assertions.assertEquals(coaEntities.get(i).getClientName(), entityList.get(i).clientName());
            Assertions.assertEquals(coaEntities.get(i).getLvl1Category().getCategory(),
                    entityList.get(i).lvl1CategoryName());
            Assertions.assertEquals(coaEntities.get(i).getIsActive(), entityList.get(i).isActive());
            Assertions.assertEquals(coaEntities.get(i).getSign(), entityList.get(i).sign());
        }
    }

    @Test
    @TestSecurity(user = NAME)
    void testListWithDefaultClientName() {
        List<CoaEntity> coaEntities = addCoaEntities();
        List<CoaEntity> defaultClientCoaEntities = coaEntities.stream()
                .filter(coaEntity -> coaEntity.getClientName().equals("walnut"))
                .toList();

        List<CoaEntity> defaultClientCoaList = given()
                .when()
                .get("/list")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<List<CoaEntity>>() {
                });

        Assertions.assertIterableEquals(defaultClientCoaEntities, defaultClientCoaList);
    }

    @Test
    @TestSecurity(user = NAME)
    void testListWithSortAndSearchForAllCoa() {
        List<CoaEntity> coaEntities = addCoaEntities();

        String searchString = "1";
        SortDto sortDto = new SortDto("coaText", "DESC");
        CoaListDto.GetSortAndSearchCoaList getSortAndSearchCoaList = new CoaListDto.GetSortAndSearchCoaList(
                List.of(sortDto), searchString);

        List<CoaItemDto> listEntities = given()
                .contentType(ContentType.JSON)
                .queryParam("clientName", CLIENT_NAME)
                .body(getSortAndSearchCoaList)
                .when()
                .queryParam("onlyActive", false)
                .post("/list")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        List<CoaEntity> filteredAndSortedEntities = coaEntities.stream()
                .filter(coaEntity -> (coaEntity.coaText.contains(searchString) || coaEntity.coaDescription.contains(
                        searchString)))
                .sorted(Comparator.comparing(CoaEntity::getCoaText).reversed()).toList();
        for (int i = 0; i < listEntities.size(); i++) {
            Assertions.assertEquals(filteredAndSortedEntities.get(i).getCoaId(), listEntities.get(i).coaId());
            Assertions.assertEquals(filteredAndSortedEntities.get(i).getCoaText(), listEntities.get(i).coaText());
            Assertions.assertEquals(filteredAndSortedEntities.get(i).getCoaDescription(),
                    listEntities.get(i).coaDescription());
            Assertions.assertEquals(filteredAndSortedEntities.get(i).getClientName(),
                    listEntities.get(i).clientName());
            Assertions.assertEquals(filteredAndSortedEntities.get(i).getLvl1Category().getCategory(),
                    listEntities.get(i).lvl1CategoryName());
            Assertions.assertEquals(filteredAndSortedEntities.get(i).getIsActive(), listEntities.get(i).isActive());
            Assertions.assertEquals(filteredAndSortedEntities.get(i).getSign(), listEntities.get(i).sign());
        }
    }

    @Test
    @TestSecurity(user = NAME)
    void testListWithSortAndSearchWithOnlyActiveCoa() {
        List<CoaEntity> coaEntities = addCoaEntities();

        String searchString = "1";
        SortDto sortDto = new SortDto("coaText", "DESC");
        CoaListDto.GetSortAndSearchCoaList getSortAndSearchCoaList = new CoaListDto.GetSortAndSearchCoaList(
                List.of(sortDto), searchString);

        List<CoaItemDto> listEntities = given()
                .contentType(ContentType.JSON)
                .queryParam("clientName", CLIENT_NAME)
                .body(getSortAndSearchCoaList)
                .when()
                .post("/list")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        List<CoaEntity> filteredAndSortedEntities = coaEntities.stream()
                .filter(coaEntity -> (coaEntity.coaText.contains(searchString) || coaEntity.coaDescription.contains(
                        searchString)) && coaEntity.isActive)
                .sorted(Comparator.comparing(CoaEntity::getCoaText).reversed()).toList();

        for (int i = 0; i < listEntities.size(); i++) {
            Assertions.assertEquals(filteredAndSortedEntities.get(i).getCoaId(), listEntities.get(i).coaId());
            Assertions.assertEquals(filteredAndSortedEntities.get(i).getCoaText(), listEntities.get(i).coaText());
            Assertions.assertEquals(filteredAndSortedEntities.get(i).getCoaDescription(),
                    listEntities.get(i).coaDescription());
            Assertions.assertEquals(filteredAndSortedEntities.get(i).getClientName(),
                    listEntities.get(i).clientName());
            Assertions.assertEquals(filteredAndSortedEntities.get(i).getLvl1Category().getCategory(),
                    listEntities.get(i).lvl1CategoryName());
            Assertions.assertEquals(filteredAndSortedEntities.get(i).getIsActive(), listEntities.get(i).isActive());
            Assertions.assertEquals(filteredAndSortedEntities.get(i).getSign(), listEntities.get(i).sign());
        }
    }

    @Test
    @TestSecurity(user = NAME)
    void testUpload() {
        File file = new File("src/test/resources/CoaResourceUpload.csv");
        List<Lvl1CategoryEntity> lvl1CategoryEntities = addNewCategory(
                IntStream.range(1, 11).mapToObj(id -> "category" + id).toList());
        List<CoaEntity> coaEntities = IntStream.range(1, 11).mapToObj(id -> CoaEntity.builder()
                .coaText("coaText" + id)
                .clientName(CLIENT_NAME)
                .coaDescription("coaDesc" + id)
                .lvl1Category(lvl1CategoryEntities.get(id - 1))
                .isActive(id % 2 == 0)
                .sign(id % 2 == 0)
                .build()).toList();

        List<CoaUploadBean> insertedEntities = given()
                .multiPart("coa-file", file)
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .when().put("upload")
                .then().statusCode(200)
                .extract()
                .body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(coaEntities.size(), insertedEntities.size());
        for (int i = 0; i < insertedEntities.size(); i++) {
            CoaEntity coaEntity = coaEntities.get(i);
            CoaUploadBean insertedEntity = insertedEntities.get(i);
            Assertions.assertEquals(coaEntity.coaText, insertedEntity.getCoaText());
            Assertions.assertEquals(coaEntity.coaDescription, insertedEntity.getCoaDescription());
            Assertions.assertEquals(coaEntity.isActive, insertedEntity.getIsActive());
            Assertions.assertEquals(coaEntity.clientName, insertedEntity.getClientName());
            Assertions.assertEquals(coaEntity.getLvl1Category().getCategory(), insertedEntity.getLvl1Category());
            Assertions.assertEquals(coaEntity.getSign(), insertedEntity.getSign());
        }
    }

    @Test
    @TestSecurity(user = NAME)
    void testUpdateUpload() {
        File file = new File("src/test/resources/CoaResourceUpdateUpload.csv");

        addCoaEntities();

        List<CoaEntity> updatedEntities = given()
                .multiPart("coa-file", file)
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .when().put("/update/upload")
                .then().statusCode(200)
                .extract()
                .body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(2, updatedEntities.size());

        CoaEntity updatedEntity = coaRepository.findById(1);
        Assertions.assertEquals(1, updatedEntity.getCoaId());
        Assertions.assertEquals("coaNewText1", updatedEntity.getCoaText());
        Assertions.assertEquals("coaDesc1", updatedEntity.getCoaDescription());
        Assertions.assertEquals(true, updatedEntity.getIsActive());
        Assertions.assertEquals("category1", updatedEntity.getLvl1Category().getCategory());

        CoaEntity updatedEntity2 = coaRepository.findById(2);
        Assertions.assertEquals(2, updatedEntity2.getCoaId());
        Assertions.assertEquals("coaText2", updatedEntity2.getCoaText());
        Assertions.assertEquals("coaNewDesc2", updatedEntity2.getCoaDescription());
        Assertions.assertEquals(false, updatedEntity2.getIsActive());
        Assertions.assertEquals("category2", updatedEntity2.getLvl1Category().getCategory());
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.ADMIN)
    void testDelete() {

        List<CoaEntity> coaEntities = addCoaEntities();
        CoaEntity entityToDelete = coaEntities.get(1);

        List<CoaItemAuditDto.Delete> coaItemRequests = List.of(
                new CoaItemAuditDto.Delete(entityToDelete.coaId, CoaEntity.COA_TEXT_COL_NAME, entityToDelete.coaText),
                new CoaItemAuditDto.Delete(entityToDelete.coaId, CoaEntity.IS_ACTIVE_COL_NAME,
                        entityToDelete.isActive.toString())
        );

        given()
                .when().delete("/{coaId}", entityToDelete.coaId)
                .then().statusCode(204);

        verify(exchangeService, times(1)).coaItemAuditForDelete(coaItemRequests);
        Assertions.assertFalse(coaRepository.findById(entityToDelete.coaId).isActive);
    }

    @Test
    @TestSecurity(user = NAME)
    void testGet() {
        List<CoaEntity> coaEntities = addCoaEntities();

        List<CoaEntity> coaEntitiesCopy = new ArrayList<>(coaEntities);
        Collections.shuffle(coaEntitiesCopy);
        List<CoaEntity> selectedCoaEntities = coaEntitiesCopy.subList(0, 5)
                .stream()
                .sorted(Comparator.comparing(CoaEntity::getCoaId))
                .toList();
        List<Integer> selectedCoaIds = selectedCoaEntities.stream()
                .map(CoaEntity::getCoaId)
                .toList();

        List<CoaItemDto> coaItemsList = given().contentType(ContentType.JSON)
                .body(selectedCoaIds)
                .when()
                .post("/get")
                .then().statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<>() {
                });
        for (int i = 0; i < selectedCoaEntities.size(); i++) {
            CoaEntity coaEntity = selectedCoaEntities.get(i);
            CoaItemDto response = coaItemsList.get(i);
            Assertions.assertEquals(coaEntity.getCoaId(), response.coaId());
            Assertions.assertEquals(coaEntity.getCoaText(), response.coaText());
            Assertions.assertEquals(coaEntity.getCoaDescription(), response.coaDescription());
            Assertions.assertEquals(coaEntity.getClientName(), response.clientName());
            Assertions.assertEquals(coaEntity.getLvl1Category().getId(), response.lvl1CategoryId());
            Assertions.assertEquals(coaEntity.getLvl1Category().getCategory(), response.lvl1CategoryName());
            Assertions.assertEquals(coaEntity.getIsActive(), response.isActive());
            Assertions.assertEquals(coaEntity.getSign(), response.sign());
        }
    }
}
