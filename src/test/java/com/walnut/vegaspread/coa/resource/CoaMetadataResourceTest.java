package com.walnut.vegaspread.coa.resource;

import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.repository.CoaRepository;
import com.walnut.vegaspread.coa.repository.Lvl1CategoryRepository;
import com.walnut.vegaspread.common.roles.Roles;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.common.mapper.TypeRef;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.IntStream;

import static com.walnut.vegaspread.coa.resource.CommonTestUtils.NAME;
import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.containsString;

@QuarkusTest
@TestHTTPEndpoint(CoaMetadataResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CoaMetadataResourceTest {

    @Inject
    CoaRepository coaRepository;
    @Inject
    Flyway flyway;
    @Inject
    Lvl1CategoryRepository lvl1CategoryRepository;

    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Transactional
    List<Lvl1CategoryEntity> addNewCategory(List<String> categories) {
        List<Lvl1CategoryEntity> lvl1CategoryEntities = categories.stream().map(category -> Lvl1CategoryEntity.builder()
                .category(category)
                .createdBy("user")
                .createdTime(LocalDateTime.now())
                .lastModifiedBy("user")
                .lastModifiedTime(LocalDateTime.now())
                .build()).toList();
        lvl1CategoryRepository.persist(lvl1CategoryEntities);
        return lvl1CategoryEntities;
    }

    @Transactional
    List<CoaEntity> createCoas() {
        List<Lvl1CategoryEntity> lvl1CategoryEntities = addNewCategory(List.of("Category 1", "Category 2"));
        List<CoaEntity> coaEntities = IntStream.range(1, 11).mapToObj(id -> CoaEntity.builder()
                .coaText("Coa text " + id)
                .clientName(id % 2 == 0 ? "ClientName1" : "ClientName2")
                .coaDescription("Coa Description " + id)
                .lvl1Category(lvl1CategoryEntities.get((id % 2)))
                .isActive(id % 2 == 0)
                .sign(id % 2 == 0)
                .build()).toList();

        coaRepository.persist(coaEntities);
        return coaEntities;
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.SUPERADMIN)
    void testGetClientList() {
        List<CoaEntity> coaEntities = createCoas();

        List<String> clientNames = coaEntities.stream().map(CoaEntity::getClientName).distinct().toList();

        List<String> clientNamesResponse = given().when()
                .get("/client/list")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(clientNames.size(), clientNamesResponse.size());
        for (String clientName : clientNames) {
            Assertions.assertTrue(clientNamesResponse.contains(clientName));
        }
    }

    @Test
    @TestSecurity(user = NAME)
    void testGetLvl1CategoriesforClient() {
        List<CoaEntity> coaEntities = createCoas();
        String clientName = "ClientName1";
        List<String> lvl1Categories = coaEntities.stream()
                .filter(coaEntity -> coaEntity.getClientName().equals(clientName))
                .map(CoaEntity::getLvl1Category)
                .map(Lvl1CategoryEntity::getCategory)
                .distinct()
                .toList();

        List<String> lvlCategoriesResponse = given().when()
                .pathParam("clientName", clientName)
                .get("/{clientName}/lvl1Category/list")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertIterableEquals(lvl1Categories, lvlCategoriesResponse);
    }

    @Test
    @TestSecurity(user = NAME)
    void testGetLvl1CategoriesforInvalidClient() {
        String clientName = "InvalidClientName";

        given().when()
                .pathParam("clientName", clientName)
                .get("/{clientName}/lvl1Category/list")
                .then()
                .statusCode(404)
                .body(containsString("Client name " + clientName + " does not exist"));
    }
}
