package com.walnut.vegaspread.coa;

import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.repository.CoaRepository;
import com.walnut.vegaspread.coa.repository.Lvl1CategoryRepository;
import com.walnut.vegaspread.coa.service.CoaService;
import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.common.service.audit.envers.UserContextService;
import io.quarkus.test.InjectMock;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.hibernate.envers.RevisionType;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;
import java.util.List;

import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class Lvl1CategoryEntityRollbackStrategyTest {

    private static final String CLIENT_NAME = "walnut";
    private static final String TEST_USER = "testUser";

    @Inject
    Lvl1CategoryEntityRollbackStrategy rollbackStrategy;

    @Inject
    Lvl1CategoryRepository lvl1CategoryRepository;

    @Inject
    CoaRepository coaRepository;

    @InjectMock
    UserContextService userContextService;

    @InjectMock
    CoaService coaService;

    @Inject
    EntityManager entityManager;

    @Inject
    Flyway flyway;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Test
    void testGetEntityClass() {
        // When/Then
        assertEquals(Lvl1CategoryEntity.class, rollbackStrategy.getEntityClass());
    }

    @Test
    void testGetEntityTypeName() {
        // When/Then
        assertEquals("Lvl1CategoryEntity", rollbackStrategy.getEntityTypeName());
    }

    @Test
    @Transactional
    void testFindById() {
        // Given: Create test entity
        Lvl1CategoryEntity category = createTestCategory("Assets");

        // When: Find by ID
        Lvl1CategoryEntity found = rollbackStrategy.findById(category.getId());

        // Then: Verify entity found
        assertNotNull(found);
        assertEquals(category.getId(), found.getId());
        assertEquals(category.getCategory(), found.getCategory());
    }

    @Test
    @Transactional
    void testFindById_NotFound() {
        // When: Find non-existent entity
        Lvl1CategoryEntity found = rollbackStrategy.findById(99999);

        // Then: Should return null
        assertNull(found);
    }

    @Test
    @Transactional
    void testPersist() {
        // Given: Create test entity
        Lvl1CategoryEntity category = Lvl1CategoryEntity.builder()
                .category("New Category")
                .createdBy(TEST_USER)
                .createdTime(LocalDateTime.now())
                .lastModifiedBy(TEST_USER)
                .lastModifiedTime(LocalDateTime.now())
                .build();

        // When: Persist entity
        rollbackStrategy.persist(category);
        entityManager.flush();

        // Then: Verify entity persisted
        assertNotNull(category.getId());
        Lvl1CategoryEntity found = lvl1CategoryRepository.findById(category.getId());
        assertNotNull(found);
        assertEquals(category.getCategory(), found.getCategory());
    }

    @Test
    @Transactional
    void testDelete() {
        // Given: Create test entity
        Lvl1CategoryEntity category = createTestCategory("Assets");
        Integer entityId = category.getId();

        // When: Delete entity
        rollbackStrategy.delete(entityId);
        entityManager.flush();

        // Then: Verify entity deleted
        Lvl1CategoryEntity found = lvl1CategoryRepository.findById(entityId);
        assertNull(found);
    }

    @Test
    @Transactional
    void testGetEntityId() {
        // Given: Create test entity
        Lvl1CategoryEntity category = createTestCategory("Assets");

        // When: Get entity ID
        Integer entityId = rollbackStrategy.getEntityId(category);

        // Then: Verify correct ID returned
        assertEquals(category.getId(), entityId);
    }

    @Test
    void testCreateNewEntity() {
        // When: Create new entity
        Lvl1CategoryEntity newEntity = rollbackStrategy.createNewEntity();

        // Then: Verify new entity created
        assertNotNull(newEntity);
        assertNull(newEntity.getId()); // Should be null for new entity
    }

    @Test
    void testCopyAuditedFields_EntityCreation() {
        // Given: Mock user context
        when(userContextService.getCurrentUsername()).thenReturn(TEST_USER);

        // Create source and target entities
        Lvl1CategoryEntity source = Lvl1CategoryEntity.builder()
                .category("Source Category")
                .lastModifiedBy("originalUser")
                .build();

        Lvl1CategoryEntity target = rollbackStrategy.createNewEntity();

        // When: Copy audited fields for entity creation
        rollbackStrategy.copyAuditedFields(source, target, RevisionType.ADD, true);

        // Then: Verify audited fields copied and @NotAudited fields set
        assertEquals(source.getCategory(), target.getCategory());
        assertEquals(source.getLastModifiedBy(), target.getLastModifiedBy());
        
        // @NotAudited fields should be set with current context
        assertEquals(TEST_USER, target.getCreatedBy());
        assertEquals(TEST_USER, target.getLastModifiedBy());
        assertNotNull(target.getCreatedTime());
        assertNotNull(target.getLastModifiedTime());
    }

    @Test
    void testCopyAuditedFields_EntityUpdate() {
        // Given: Mock user context
        when(userContextService.getCurrentUsername()).thenReturn(TEST_USER);

        // Create source and target entities
        Lvl1CategoryEntity source = Lvl1CategoryEntity.builder()
                .category("Updated Category")
                .lastModifiedBy("updatedUser")
                .build();

        Lvl1CategoryEntity target = Lvl1CategoryEntity.builder()
                .category("Original Category")
                .createdBy("originalCreator")
                .createdTime(LocalDateTime.now().minusDays(1))
                .lastModifiedBy("originalModifier")
                .lastModifiedTime(LocalDateTime.now().minusDays(1))
                .build();

        LocalDateTime originalCreatedTime = target.getCreatedTime();
        String originalCreatedBy = target.getCreatedBy();

        // When: Copy audited fields for entity update
        rollbackStrategy.copyAuditedFields(source, target, RevisionType.MOD, false);

        // Then: Verify audited fields copied
        assertEquals(source.getCategory(), target.getCategory());
        assertEquals(source.getLastModifiedBy(), target.getLastModifiedBy());
        
        // @NotAudited fields: createdBy and createdTime should remain unchanged
        assertEquals(originalCreatedBy, target.getCreatedBy());
        assertEquals(originalCreatedTime, target.getCreatedTime());
        
        // lastModifiedBy and lastModifiedTime should be updated with current context
        assertEquals(TEST_USER, target.getLastModifiedBy());
        assertNotNull(target.getLastModifiedTime());
    }

    @Test
    @Transactional
    void testIsRollbackAllowed_AddAndModOperations() {
        // Given: Any entity ID
        Integer entityId = 1;

        // When/Then: Should allow ADD and MOD operations
        assertTrue(rollbackStrategy.isRollbackAllowed(entityId, RevisionType.ADD));
        assertTrue(rollbackStrategy.isRollbackAllowed(entityId, RevisionType.MOD));
    }

    @Test
    @Transactional
    void testIsRollbackAllowed_DeleteWithInactiveCoaEntities() {
        // Given: Create category with inactive COA entities
        Lvl1CategoryEntity category = createTestCategory("Assets");
        CoaEntity inactiveCoaEntity = createTestCoaEntity("1.BS.CA.Cash", "Cash Account", category, false);

        // Mock CoaService to return inactive entities
        when(coaService.findWithLvl1CategoryId(category.getId()))
                .thenReturn(List.of(inactiveCoaEntity));

        // When/Then: Should allow deletion when all COA entities are inactive
        assertTrue(rollbackStrategy.isRollbackAllowed(category.getId(), RevisionType.DEL));
    }

    @Test
    @Transactional
    void testIsRollbackAllowed_DeleteWithActiveCoaEntities() {
        // Given: Create category with active COA entities
        Lvl1CategoryEntity category = createTestCategory("Assets");
        CoaEntity activeCoaEntity = createTestCoaEntity("1.BS.CA.Cash", "Cash Account", category, true);

        // Mock CoaService to return active entities
        when(coaService.findWithLvl1CategoryId(category.getId()))
                .thenReturn(List.of(activeCoaEntity));

        // When/Then: Should throw exception when trying to delete category with active COA entities
        assertThrows(ResponseException.class, () -> {
            rollbackStrategy.isRollbackAllowed(category.getId(), RevisionType.DEL);
        });
    }

    @Test
    @Transactional
    void testIsRollbackAllowed_DeleteWithMixedCoaEntities() {
        // Given: Create category with both active and inactive COA entities
        Lvl1CategoryEntity category = createTestCategory("Assets");
        CoaEntity activeCoaEntity = createTestCoaEntity("1.BS.CA.Cash", "Cash Account", category, true);
        CoaEntity inactiveCoaEntity = createTestCoaEntity("1.BS.CA.Bank", "Bank Account", category, false);

        // Mock CoaService to return mixed entities
        when(coaService.findWithLvl1CategoryId(category.getId()))
                .thenReturn(List.of(activeCoaEntity, inactiveCoaEntity));

        // When/Then: Should throw exception when any COA entity is active
        assertThrows(ResponseException.class, () -> {
            rollbackStrategy.isRollbackAllowed(category.getId(), RevisionType.DEL);
        });
    }

    @Test
    @Transactional
    void testIsRollbackAllowed_DeleteWithNoCoaEntities() {
        // Given: Create category with no COA entities
        Lvl1CategoryEntity category = createTestCategory("Assets");

        // Mock CoaService to return empty list
        when(coaService.findWithLvl1CategoryId(category.getId()))
                .thenReturn(List.of());

        // When/Then: Should allow deletion when no COA entities exist
        assertTrue(rollbackStrategy.isRollbackAllowed(category.getId(), RevisionType.DEL));
    }

    @Test
    void testCopyAuditedFields_NullValues() {
        // Given: Mock user context
        when(userContextService.getCurrentUsername()).thenReturn(TEST_USER);

        // Create source entity with null audited field
        Lvl1CategoryEntity source = Lvl1CategoryEntity.builder()
                .category(null) // Null category
                .lastModifiedBy(null) // Null lastModifiedBy
                .build();

        Lvl1CategoryEntity target = rollbackStrategy.createNewEntity();

        // When: Copy audited fields
        rollbackStrategy.copyAuditedFields(source, target, RevisionType.ADD, true);

        // Then: Verify null values copied correctly
        assertNull(target.getCategory());
        assertEquals(TEST_USER, target.getLastModifiedBy()); // Should be overridden by current user
        assertNotNull(target.getCreatedTime());
        assertNotNull(target.getLastModifiedTime());
    }

    // Helper methods
    @Transactional
    private Lvl1CategoryEntity createTestCategory(String categoryName) {
        Lvl1CategoryEntity category = Lvl1CategoryEntity.builder()
                .category(categoryName)
                .createdBy(TEST_USER)
                .createdTime(LocalDateTime.now())
                .lastModifiedBy(TEST_USER)
                .lastModifiedTime(LocalDateTime.now())
                .build();
        lvl1CategoryRepository.persist(category);
        return category;
    }

    @Transactional
    private CoaEntity createTestCoaEntity(String coaText, String description, Lvl1CategoryEntity category, boolean isActive) {
        CoaEntity entity = CoaEntity.builder()
                .coaText(coaText)
                .coaDescription(description)
                .clientName(CLIENT_NAME)
                .isActive(isActive)
                .sign(true)
                .lvl1Category(category)
                .build();
        coaRepository.persist(entity);
        return entity;
    }
}
