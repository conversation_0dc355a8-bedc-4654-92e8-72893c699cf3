package com.walnut.vegaspread.coa.service;

import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.entity.CoaTaskEntity;
import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.model.CoaTaskDto;
import com.walnut.vegaspread.coa.model.ListTaskDto;
import com.walnut.vegaspread.coa.model.TaskListResponseDto;
import com.walnut.vegaspread.coa.model.TaskOutputDto;
import com.walnut.vegaspread.coa.repository.CoaRepository;
import com.walnut.vegaspread.coa.repository.CoaTaskRepository;
import com.walnut.vegaspread.coa.repository.Lvl1CategoryRepository;
import com.walnut.vegaspread.common.model.extraction.TableTagDto;
import io.quarkus.test.InjectMock;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static java.lang.Math.min;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CoaTaskServiceTest {

    private static final String CLIENT_NAME = "walnut";

    @Inject
    Flyway flyway;
    @Inject
    CoaTaskService coaTaskService;
    @Inject
    CoaRepository coaRepository;
    @Inject
    CoaTaskRepository coaTaskRepository;
    @Inject
    EntityManager entityManager;
    @Inject
    Lvl1CategoryRepository lvl1CategoryRepository;
    @InjectMock
    JsonWebToken accessToken;
    @InjectMock
    ExchangeService exchangeService;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @BeforeEach
    void setUp() {
        when(accessToken.getClaim("client_name")).thenReturn(CLIENT_NAME);
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Test
    void testCreate_Success() {
        Lvl1CategoryEntity lvl1Category = addNewCategory(List.of("Assets")).get(0);
        CoaEntity coaEntity = createEntity("1.BS.CA.Cash", "desc", lvl1Category, true, true, CLIENT_NAME);

        CoaTaskDto.NewTask newTask = new CoaTaskDto.NewTask(2, "Text", "FS", coaEntity.coaId, 1, 1, 1);

        CoaTaskEntity result = coaTaskService.create(newTask);

        assertNotNull(result);
        assertEquals(newTask.tableTypeId(), result.tableTypeId);
        assertEquals(newTask.text(), result.text);
        assertEquals(newTask.fsText(), result.fsText);
        assertEquals(CLIENT_NAME, result.clientName);
        assertEquals(coaEntity.coaId, result.coa.coaId);
        assertEquals(newTask.entityNameId(), result.entityNameId);
        assertEquals(newTask.industryId(), result.industryId);
        assertEquals(newTask.regionId(), result.regionId);
    }

    @Test
    void testUpdate_WithCoaChange() {
        Lvl1CategoryEntity lvl1Category = addNewCategory(List.of("Assets")).get(0);
        CoaEntity original = createEntity("1.BS.CA.Original", "desc", lvl1Category, true, true, CLIENT_NAME);
        CoaEntity updated = createEntity("1.BS.CA.Updated", "desc", lvl1Category, true, true, CLIENT_NAME);
        CoaTaskEntity task = createTask(2, "text", "fs", original, 47, 0, 0);

        TaskOutputDto result = coaTaskService.update(new CoaTaskDto.UpdateTask(task.id, updated.coaId));

        assertNotNull(result);
        assertEquals(updated.coaId, result.coaId());
    }

    @Test
    void testUpdate_WithoutCoaChange() {
        Lvl1CategoryEntity lvl1Category = addNewCategory(List.of("Assets")).get(0);
        CoaEntity coa = createEntity("1.BS.CA", "desc", lvl1Category, true, true, CLIENT_NAME);
        CoaTaskEntity task = createTask(2, "text", "fs", coa, 295, 0, 0);

        TaskOutputDto result = coaTaskService.update(new CoaTaskDto.UpdateTask(task.id, coa.coaId));

        assertNotNull(result);
        assertEquals(coa.coaId, result.coaId());
    }

    @Test
    void testUpdate_WithNullCoaId() {
        Lvl1CategoryEntity lvl1Category = addNewCategory(List.of("Assets")).get(0);
        CoaEntity coa = createEntity("1.BS.CA", "desc", lvl1Category, true, true, CLIENT_NAME);
        CoaTaskEntity task = createTask(2, "text", "fs", coa, 78, 0, 0);

        TaskOutputDto result = coaTaskService.update(new CoaTaskDto.UpdateTask(task.id, null));

        assertNotNull(result);
        assertEquals(coa.coaId, result.coaId());
    }

    @Test
    void testGet() {
        Lvl1CategoryEntity lvl1Category = addNewCategory(List.of("Assets")).get(0);
        CoaEntity coa = createEntity("1.BS.CA", "desc", lvl1Category, true, true, CLIENT_NAME);
        CoaTaskEntity task = createTask(2, "text", "fs", coa, 78, 0, 0);

        when(exchangeService.getTableType(2)).thenReturn("TABLE");

        TaskOutputDto result = coaTaskService.get(task.id);

        assertNotNull(result);
        assertEquals(task.id, result.taskId());
    }

    @Test
    void testList_Success() {
        Lvl1CategoryEntity lvl1Category = addNewCategory(List.of("Assets")).get(0);
        CoaEntity coa = createEntity("1.BS.CA", "desc", lvl1Category, true, true, CLIENT_NAME);
        when(exchangeService.getTableTypes(List.of(2, 3))).thenReturn(List.of(
                new TableTagDto.Response(2, "TABLE1", "user", LocalDateTime.now(), "user", LocalDateTime.now()),
                new TableTagDto.Response(3, "TABLE2", "user", LocalDateTime.now(), "user", LocalDateTime.now())
        ));
        List<CoaTaskEntity> tasks = List.of(
                createTask(2, "text1", "fs1", coa, 47, 0, 0),
                createTask(3, "text2", "fs2", coa, 295, 0, 0)
        );
        int pageNumber = 1;
        int pageSize = 10;
        ListTaskDto.GetTaskList listDto = new ListTaskDto.GetTaskList(pageNumber, pageSize, List.of(), List.of(), "");
        TaskListResponseDto result = coaTaskService.list(listDto, false);

        assertNotNull(result);
        assertEquals(tasks.size(), result.totalTasks());
        assertEquals(pageNumber, result.totalPages());
        assertEquals(min(tasks.size(), pageSize), result.tasks().size());
    }

    @Test
    void testList_WithApiKeyAuthenticated() {
        Lvl1CategoryEntity lvl1Category = addNewCategory(List.of("Assets")).get(0);
        CoaEntity coa = createEntity("1.BS.CA", "desc", lvl1Category, true, true, "default");
        when(exchangeService.getTableTypes(List.of(2))).thenReturn(List.of(
                new TableTagDto.Response(2, "TABLE", "user", LocalDateTime.now(), "user", LocalDateTime.now())
        ));
        List<CoaTaskEntity> tasks = List.of(
                createTask(2, "text", "fs", coa, 78, 0, 0)
        );

        int pageNumber = 1;
        int pageSize = 10;
        ListTaskDto.GetTaskList listDto = new ListTaskDto.GetTaskList(pageNumber, pageSize, List.of(), List.of(), "");
        TaskListResponseDto result = coaTaskService.list(listDto, true);

        assertNotNull(result);
        assertEquals(1, result.totalTasks());
        assertEquals(1, result.totalPages());
    }

    @Test
    void testList_EmptyResult() {
        ListTaskDto.GetTaskList listDto = new ListTaskDto.GetTaskList(1, 10, List.of(), List.of(), "");
        TaskListResponseDto result = coaTaskService.list(listDto, false);

        assertNotNull(result);
        assertEquals(0, result.totalTasks());
        assertEquals(0, result.totalPages());
        assertTrue(result.tasks().isEmpty());
    }

    @Test
    void testList_Pagination() {
        Lvl1CategoryEntity lvl1Category = addNewCategory(List.of("Assets")).get(0);
        CoaEntity coa = createEntity("1.BS.CA", "desc", lvl1Category, true, true, CLIENT_NAME);
        int totalTasks = 15;
        for (int i = 0; i < totalTasks; i++) {
            createTask(i + 2, "text" + i, "fs" + i, coa, 0, 0, 0);
        }
        List<Integer> tableTypeIds = new ArrayList<>();
        List<TableTagDto.Response> tableTypes = new ArrayList<>();
        for (int i = 0; i < totalTasks; i++) {
            tableTypeIds.add(i + 2);
            tableTypes.add(new TableTagDto.Response(i + 2, "TABLE" + i, "user", LocalDateTime.now(), "user",
                    LocalDateTime.now()));
        }
        int pageSize = 10;
        ListTaskDto.GetTaskList listDto = new ListTaskDto.GetTaskList(2, pageSize, List.of(), List.of(), "");
        TaskListResponseDto result = coaTaskService.list(listDto, false);

        assertNotNull(result);
        assertEquals(totalTasks, result.totalTasks());
        assertEquals((int) Math.ceil((double) totalTasks / pageSize), result.totalPages());
        assertEquals(pageSize, result.pageSize());
    }

    @Transactional
    CoaEntity createEntity(String coaText, String description, Lvl1CategoryEntity lvl1Category, boolean isActive,
                           boolean sign,
                           String clientName) {

        CoaEntity entity = CoaEntity.builder()
                .coaText(coaText)
                .coaDescription(description)
                .lvl1Category(lvl1Category)
                .isActive(isActive)
                .sign(sign)
                .clientName(clientName)
                .build();
        coaRepository.persist(entity);
        return entity;
    }

    @Transactional
    List<Lvl1CategoryEntity> addNewCategory(List<String> categories) {
        List<Lvl1CategoryEntity> lvl1CategoryEntities = categories.stream().map(category -> Lvl1CategoryEntity.builder()
                .category(category)
                .createdBy("user")
                .createdTime(LocalDateTime.now())
                .lastModifiedBy("user")
                .lastModifiedTime(LocalDateTime.now())
                .build()).toList();
        lvl1CategoryRepository.persist(lvl1CategoryEntities);
        return lvl1CategoryEntities;
    }

    @Transactional
    CoaTaskEntity createTask(Integer tableTypeId, String text, String fsText, CoaEntity coa, int entityNameId,
                             int industryId, int regionId) {
        CoaTaskEntity task = CoaTaskEntity.builder()
                .tableTypeId(tableTypeId)
                .text(text)
                .fsText(fsText)
                .clientName(CLIENT_NAME)
                .entityNameId(entityNameId)
                .industryId(industryId)
                .regionId(regionId)
                .coa(coa)
                .build();
        coaTaskRepository.persist(task);
        return task;
    }
}
