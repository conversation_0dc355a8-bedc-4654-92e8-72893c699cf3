package com.walnut.vegaspread.coa.service;

import com.walnut.vegaspread.coa.Lvl1CategoryEntityRollbackStrategy;
import com.walnut.vegaspread.coa.audit.Lvl1CategoryEntityMapper;
import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.model.Lvl1CategoryAuditDto;
import com.walnut.vegaspread.coa.repository.Lvl1CategoryRepository;
import com.walnut.vegaspread.common.model.audit.envers.AuditFilterDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditRequestDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditSortDto;
import com.walnut.vegaspread.common.service.audit.envers.GenericAuditService;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.hibernate.envers.RevisionType;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;
import java.util.List;

import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static org.junit.jupiter.api.Assertions.*;

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class Lvl1CategoryAuditServiceTest {

    private static final String TEST_USER = "testUser";

    @Inject
    Lvl1CategoryAuditService lvl1CategoryAuditService;

    @Inject
    Lvl1CategoryRepository lvl1CategoryRepository;

    @Inject
    Lvl1CategoryEntityMapper lvl1CategoryEntityMapper;

    @Inject
    Lvl1CategoryEntityRollbackStrategy rollbackStrategy;

    @Inject
    EntityManager entityManager;

    @Inject
    Flyway flyway;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_BasicRequest() {
        // Given: Create test data
        Lvl1CategoryEntity category = createTestCategory("Assets");

        // Modify entity to create audit records
        category.setCategory("Updated Assets");
        category.setLastModifiedBy("updatedUser");
        entityManager.persist(category);
        entityManager.flush();

        // When: Request audit data
        AuditRequestDto request = new AuditRequestDto(
                List.of(),
                List.of(),
                1,
                10
        );

        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<Lvl1CategoryAuditDto>> response =
                lvl1CategoryAuditService.getPaginatedAuditsAsDto(request, lvl1CategoryEntityMapper, entityManager);

        // Then: Verify response
        assertNotNull(response);
        assertTrue(response.totalCount() > 0);
        assertFalse(response.audits().isEmpty());
        assertEquals(1, response.currentPage());
        assertTrue(response.totalPages() >= 1);
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_WithFilters() {
        // Given: Create test data
        Lvl1CategoryEntity category1 = createTestCategory("Assets");
        Lvl1CategoryEntity category2 = createTestCategory("Liabilities");

        // When: Request audit data with filter
        AuditFilterDto filter = new AuditFilterDto(
                "id",
                category1.getId(),
                AuditFilterDto.FilterOperation.EQUALS
        );

        AuditRequestDto request = new AuditRequestDto(
                List.of(filter),
                List.of(),
                1,
                10
        );

        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<Lvl1CategoryAuditDto>> response =
                lvl1CategoryAuditService.getPaginatedAuditsAsDto(request, lvl1CategoryEntityMapper, entityManager);

        // Then: Verify filtered results
        assertNotNull(response);
        response.audits().forEach(audit -> {
            assertEquals(category1.getId(), audit.entity().id());
        });
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_WithSorting() {
        // Given: Create test data with multiple revisions
        Lvl1CategoryEntity category = createTestCategory("Assets");

        // Create multiple revisions
        category.setCategory("Assets V2");
        entityManager.persist(category);
        entityManager.flush();

        category.setCategory("Assets V3");
        entityManager.persist(category);
        entityManager.flush();

        // When: Request audit data with sorting
        AuditSortDto sort = new AuditSortDto("timestamp", AuditSortDto.SortDirection.DESC);
        AuditRequestDto request = new AuditRequestDto(
                List.of(),
                List.of(sort),
                1,
                10
        );

        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<Lvl1CategoryAuditDto>> response =
                lvl1CategoryAuditService.getPaginatedAuditsAsDto(request, lvl1CategoryEntityMapper, entityManager);

        // Then: Verify sorted results (most recent first)
        assertNotNull(response);
        assertTrue(response.audits().size() >= 2);
        
        // Verify descending order by revision number (proxy for timestamp)
        for (int i = 0; i < response.audits().size() - 1; i++) {
            assertTrue(response.audits().get(i).revisionNumber() >= 
                      response.audits().get(i + 1).revisionNumber());
        }
    }

    @Test
    @Transactional
    void testGetAuditForLvl1CategoryId() {
        // Given: Create test data
        Lvl1CategoryEntity category = createTestCategory("Assets");

        // Modify entity to create audit records
        category.setCategory("Updated Assets");
        entityManager.persist(category);
        entityManager.flush();

        // When: Get audit for specific category ID
        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<Lvl1CategoryAuditDto>> response =
                lvl1CategoryAuditService.getAuditForLvl1CategoryId(category.getId(), lvl1CategoryEntityMapper, entityManager);

        // Then: Verify results
        assertNotNull(response);
        assertTrue(response.totalCount() > 0);
        response.audits().forEach(audit -> {
            assertEquals(category.getId(), audit.entity().id());
        });
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_Pagination() {
        // Given: Create multiple entities to ensure multiple audit records
        for (int i = 1; i <= 5; i++) {
            createTestCategory("Category " + i);
        }

        // When: Request first page with page size 2
        AuditRequestDto request = new AuditRequestDto(
                List.of(),
                List.of(),
                1,
                2
        );

        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<Lvl1CategoryAuditDto>> response =
                lvl1CategoryAuditService.getPaginatedAuditsAsDto(request, lvl1CategoryEntityMapper, entityManager);

        // Then: Verify pagination
        assertNotNull(response);
        assertEquals(1, response.currentPage());
        assertTrue(response.totalPages() >= 1);
        assertTrue(response.audits().size() <= 2);
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_EmptyResult() {
        // Given: No audit data exists

        // When: Request audit data
        AuditRequestDto request = new AuditRequestDto(
                List.of(),
                List.of(),
                1,
                10
        );

        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<Lvl1CategoryAuditDto>> response =
                lvl1CategoryAuditService.getPaginatedAuditsAsDto(request, lvl1CategoryEntityMapper, entityManager);

        // Then: Verify empty response
        assertNotNull(response);
        assertEquals(0, response.totalCount());
        assertTrue(response.audits().isEmpty());
        assertEquals(1, response.currentPage());
        assertEquals(0, response.totalPages());
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_ComplexFilters() {
        // Given: Create test data
        Lvl1CategoryEntity category1 = createTestCategory("Current Assets");
        Lvl1CategoryEntity category2 = createTestCategory("Non-Current Assets");
        Lvl1CategoryEntity category3 = createTestCategory("Current Liabilities");

        // When: Request audit data with LIKE filter
        AuditFilterDto filter = new AuditFilterDto(
                "category",
                "Assets",
                AuditFilterDto.FilterOperation.LIKE
        );

        AuditRequestDto request = new AuditRequestDto(
                List.of(filter),
                List.of(),
                1,
                10
        );

        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<Lvl1CategoryAuditDto>> response =
                lvl1CategoryAuditService.getPaginatedAuditsAsDto(request, lvl1CategoryEntityMapper, entityManager);

        // Then: Verify filtered results
        assertNotNull(response);
        response.audits().forEach(audit -> {
            assertTrue(audit.entity().category().contains("Assets"));
        });
    }

    @Test
    @Transactional
    void testRollback_Success() {
        // Given: Create entity and modify it
        Lvl1CategoryEntity category = createTestCategory("Assets");
        
        String originalCategory = category.getCategory();
        String originalLastModifiedBy = category.getLastModifiedBy();
        
        // Modify entity
        category.setCategory("Modified Assets");
        category.setLastModifiedBy("modifiedUser");
        entityManager.persist(category);
        entityManager.flush();
        
        // When: Rollback (Note: This is a simplified test - actual rollback needs traceId correlation)
        lvl1CategoryAuditService.rollback("test-trace-id", entityManager);
        entityManager.clear();
        
        // Then: Verify rollback method was called (actual rollback verification would need more complex setup)
        Lvl1CategoryEntity rolledBackEntity = lvl1CategoryRepository.findById(category.getId());
        assertNotNull(rolledBackEntity);
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_WithInFilter() {
        // Given: Create multiple entities
        Lvl1CategoryEntity category1 = createTestCategory("Assets");
        Lvl1CategoryEntity category2 = createTestCategory("Liabilities");
        Lvl1CategoryEntity category3 = createTestCategory("Equity");

        // When: Request audit data with IN filter
        AuditFilterDto filter = new AuditFilterDto(
                "id",
                List.of(category1.getId(), category2.getId()),
                AuditFilterDto.FilterOperation.IN
        );

        AuditRequestDto request = new AuditRequestDto(
                List.of(filter),
                List.of(),
                1,
                10
        );

        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<Lvl1CategoryAuditDto>> response =
                lvl1CategoryAuditService.getPaginatedAuditsAsDto(request, lvl1CategoryEntityMapper, entityManager);

        // Then: Verify filtered results
        assertNotNull(response);
        response.audits().forEach(audit -> {
            assertTrue(List.of(category1.getId(), category2.getId()).contains(audit.entity().id()));
        });
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_WithDateRangeFilter() {
        // Given: Create test data
        Lvl1CategoryEntity category = createTestCategory("Assets");

        // When: Request audit data with date range filter
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime yesterday = now.minusDays(1);
        LocalDateTime tomorrow = now.plusDays(1);

        AuditFilterDto filter = new AuditFilterDto(
                "timestamp",
                List.of(yesterday, tomorrow),
                AuditFilterDto.FilterOperation.BETWEEN
        );

        AuditRequestDto request = new AuditRequestDto(
                List.of(filter),
                List.of(),
                1,
                10
        );

        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<Lvl1CategoryAuditDto>> response =
                lvl1CategoryAuditService.getPaginatedAuditsAsDto(request, lvl1CategoryEntityMapper, entityManager);

        // Then: Verify results within date range
        assertNotNull(response);
        // Results should include today's audit records
        assertTrue(response.totalCount() >= 0);
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_InvalidRequest() {
        // Given: Invalid request with page number 0
        AuditRequestDto invalidRequest = new AuditRequestDto(
                List.of(),
                List.of(),
                0, // Invalid page number
                10
        );

        // When/Then: Should handle invalid request gracefully
        assertFalse(invalidRequest.isValid());
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_LargePageSize() {
        // Given: Request with maximum page size
        AuditRequestDto request = new AuditRequestDto(
                List.of(),
                List.of(),
                1,
                1000 // Maximum allowed page size
        );

        // When: Request audit data
        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<Lvl1CategoryAuditDto>> response =
                lvl1CategoryAuditService.getPaginatedAuditsAsDto(request, lvl1CategoryEntityMapper, entityManager);

        // Then: Should handle large page size
        assertNotNull(response);
        assertTrue(request.isValid());
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_RevisionTypeFiltering() {
        // Given: Create and modify entity
        Lvl1CategoryEntity category = createTestCategory("Assets");
        
        // Modify to create MOD revision
        category.setCategory("Modified Assets");
        entityManager.persist(category);
        entityManager.flush();

        // When: Filter by revision type
        AuditFilterDto filter = new AuditFilterDto(
                "revisionType",
                RevisionType.ADD,
                AuditFilterDto.FilterOperation.EQUALS
        );

        AuditRequestDto request = new AuditRequestDto(
                List.of(filter),
                List.of(),
                1,
                10
        );

        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<Lvl1CategoryAuditDto>> response =
                lvl1CategoryAuditService.getPaginatedAuditsAsDto(request, lvl1CategoryEntityMapper, entityManager);

        // Then: Verify only ADD revisions returned
        assertNotNull(response);
        response.audits().forEach(audit -> {
            assertEquals(RevisionType.ADD, audit.revisionType());
        });
    }

    // Helper methods
    @Transactional
    private Lvl1CategoryEntity createTestCategory(String categoryName) {
        Lvl1CategoryEntity category = Lvl1CategoryEntity.builder()
                .category(categoryName)
                .createdBy(TEST_USER)
                .createdTime(LocalDateTime.now())
                .lastModifiedBy(TEST_USER)
                .lastModifiedTime(LocalDateTime.now())
                .build();
        lvl1CategoryRepository.persist(category);
        return category;
    }
}
