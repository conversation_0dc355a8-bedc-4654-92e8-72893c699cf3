package com.walnut.vegaspread.coa.service;

import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.repository.CoaRepository;
import com.walnut.vegaspread.coa.repository.Lvl1CategoryRepository;
import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.common.utils.Jwt;
import io.quarkus.test.InjectMock;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.MockedStatic;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Stream;

import static com.walnut.vegaspread.common.utils.Constants.DEFAULT_CLIENT_NAME;
import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CoaMetadataServiceTest {

    private static final String CLIENT_NAME = "walnut";

    @Inject
    Flyway flyway;
    @Inject
    CoaRepository coaRepository;
    @Inject
    EntityManager entityManager;
    @Inject
    Lvl1CategoryRepository lvl1CategoryRepository;
    @InjectMock
    JsonWebToken accessToken;

    CoaMetadataService coaMetadataService;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @BeforeEach
    void setUp() {
        when(accessToken.getClaim("client_name")).thenReturn(CLIENT_NAME);
        coaMetadataService = new CoaMetadataService(coaRepository, accessToken);
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Test
    void testGetClientList_NonDefaultClient() {
        try (MockedStatic<Jwt> jwtMock = mockStatic(Jwt.class)) {
            jwtMock.when(() -> Jwt.getClientName(accessToken)).thenReturn("rcbc");

            List<String> result = coaMetadataService.getClientList();

            assertEquals(List.of("rcbc"), result);
        }
    }

    @Test
    void testGetClientList_DefaultClient() {
        when(accessToken.getClaim("client_name")).thenReturn(DEFAULT_CLIENT_NAME);
        List<CoaEntity> coaEntities = List.of(
                createEntity("(c ) Right - of - use assets", "desc1", "Balance Sheet", true, true, "walnut"),
                createEntity("Remeasurement gain (loss) on defined benefit obligation - net of tax (Notes 26 and 27)",
                        "desc2",
                        "Income Statement", false, true, "walnut"),
                createEntity("Lease liabilities", "desc3", "BS.Assets.Non-Current Assets.Deferred Tax Assets", true,
                        false,
                        "client2,client3")
        );

        List<String> expectedClients = coaEntities.stream()
                .map(CoaEntity::getClientName)
                .flatMap(x -> Stream.of(x.split(",")))
                .distinct()
                .sorted()
                .toList();
        List<String> clients = coaMetadataService.getClientList();

        assertEquals(3, clients.size());
        assertEquals(expectedClients, clients);
    }

    @Test
    void testGetLvl1CategoriesForClient_ValidClient() {
        List<CoaEntity> coaEntities = List.of(
                createEntity("(c ) Right - of - use assets", "desc", "Balance Sheet", true, true, CLIENT_NAME),
                createEntity("Remeasurement gain (loss) on defined benefit obligation - net of tax (Notes 26 and 27)",
                        "desc",
                        "Income Statement", true, true, CLIENT_NAME)
        );
        List<String> expectedCategories = coaEntities.stream()
                .map(CoaEntity::getLvl1Category)
                .map(Lvl1CategoryEntity::getCategory)
                .distinct()
                .sorted()
                .toList();

        List<String> results = coaMetadataService.getLvl1CategoriesforClient(CLIENT_NAME);

        assertEquals(2, results.size());
        assertEquals(expectedCategories, results);
    }

    @Test
    void testGetLvl1CategoriesForClient_InvalidClient() {

        WebApplicationException ex = assertThrows(WebApplicationException.class, () ->
                coaMetadataService.getLvl1CategoriesforClient("invalid"));

        assertEquals(Response.Status.NOT_FOUND.getStatusCode(), ex.getResponse().getStatus());
        ResponseException.ErrorResponse error = (ResponseException.ErrorResponse) ex.getResponse().getEntity();
        assertEquals("Client name invalid does not exist", error.message());
    }

    @Transactional
    CoaEntity createEntity(String coaText, String description, String category, boolean isActive, boolean sign,
                           String clientName) {
        Lvl1CategoryEntity lvl1CategoryEntity = Lvl1CategoryEntity.builder()
                .category(category)
                .createdBy("user")
                .createdTime(LocalDateTime.now())
                .lastModifiedBy("user")
                .lastModifiedTime(LocalDateTime.now())
                .build();
        lvl1CategoryRepository.persist(lvl1CategoryEntity);

        CoaEntity entity = CoaEntity.builder()
                .coaText(coaText)
                .coaDescription(description)
                .lvl1Category(lvl1CategoryEntity)
                .isActive(isActive)
                .sign(sign)
                .clientName(clientName)
                .build();
        coaRepository.persist(entity);
        return entity;
    }
}