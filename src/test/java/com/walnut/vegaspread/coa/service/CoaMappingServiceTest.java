package com.walnut.vegaspread.coa.service;

import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.entity.CoaTaskEntity;
import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.model.CoaMappingDto;
import com.walnut.vegaspread.coa.repository.CoaRepository;
import com.walnut.vegaspread.coa.repository.CoaTaskRepository;
import com.walnut.vegaspread.coa.repository.Lvl1CategoryRepository;
import com.walnut.vegaspread.common.model.extraction.TableTagDto;
import io.quarkus.test.InjectMock;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;
import java.util.List;

import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CoaMappingServiceTest {

    private static final String CLIENT_NAME = "walnut";

    @Inject
    Flyway flyway;
    @Inject
    CoaTaskRepository coaTaskRepository;
    @Inject
    CoaRepository coaRepository;
    @Inject
    EntityManager entityManager;
    @InjectMock
    JsonWebToken accessToken;
    @Inject
    Lvl1CategoryRepository lvl1CategoryRepository;
    @InjectMock
    ExchangeService exchangeService;

    CoaMappingService coaMappingService;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @BeforeEach
    void setUp() {
        when(accessToken.getClaim("client_name")).thenReturn(CLIENT_NAME);
        coaMappingService = new CoaMappingService(accessToken, coaTaskRepository, exchangeService);
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Test
    @Transactional
    void testGetCoaMapping_WithClientName() {
        List<CoaEntity> coaEntities = List.of(
                createCoaEntity("Balance Sheet", 100),
                createCoaEntity("Income Statement", 200)
        );
        when(exchangeService.getTableTypes(List.of(2, 3))).thenReturn(List.of(
                new TableTagDto.Response(2, "Table 2", "user", LocalDateTime.now(), "user", LocalDateTime.now()),
                new TableTagDto.Response(3, "Table 3", "user", LocalDateTime.now(), "user", LocalDateTime.now())
        ));
        List<CoaTaskEntity> taskEntities = List.of(
                createTaskEntity(2, "Assets", "FS Assets", coaEntities.get(0)),
                createTaskEntity(3, "Revenue", "FS Revenue", coaEntities.get(1))
        );

        List<CoaMappingDto> results = coaMappingService.getCoaMapping(CLIENT_NAME);

        assertEquals(2, results.size());
        for (int i = 0; i < results.size(); i++) {
            assertEquals(taskEntities.get(i).tableTypeId, results.get(i).tableTypeId());
            assertEquals(taskEntities.get(i).text, results.get(i).text());
            assertEquals(taskEntities.get(i).fsText, results.get(i).fsText());
            assertEquals(taskEntities.get(i).coa.coaId, results.get(i).coaId());
        }
    }

    @Test
    @Transactional
    void testGetCoaMapping_NullClientName_UsesToken() {
        CoaEntity coa = createCoaEntity("Balance Sheet", 100);
        when(exchangeService.getTableTypes(List.of(2))).thenReturn(List.of(
                new TableTagDto.Response(2, "Balance Sheet", "user", LocalDateTime.now(), "user", LocalDateTime.now())
        ));
        CoaTaskEntity task = createTaskEntity(2, "Assets", "FS Assets", coa);

        List<CoaMappingDto> results = coaMappingService.getCoaMapping(null);

        assertEquals(1, results.size());
        for (int i = 0; i < results.size(); i++) {
            assertEquals(task.tableTypeId, results.get(i).tableTypeId());
            assertEquals(task.text, results.get(i).text());
            assertEquals(task.fsText, results.get(i).fsText());
            assertEquals(task.coa.coaId, results.get(i).coaId());
        }
    }

    @Test
    @Transactional
    void testGetCoaMapping_ReturnsEmptyIfNoData() {
        List<CoaMappingDto> result = coaMappingService.getCoaMapping(CLIENT_NAME);
        assertTrue(result.isEmpty());
    }

    @Transactional
    CoaTaskEntity createTaskEntity(Integer tableTypeId, String text, String fsText, CoaEntity coa) {

        CoaTaskEntity task = CoaTaskEntity.builder()
                .coa(coa)
                .text(text)
                .fsText(fsText)
                .clientName(CLIENT_NAME)
                .entityNameId(1)
                .industryId(1)
                .regionId(1)
                .tableTypeId(tableTypeId)
                .build();

        entityManager.persist(task);
        return task;
    }

    @Transactional
    CoaEntity createCoaEntity(String category, Integer coaId) {
        Lvl1CategoryEntity lvl1Category = Lvl1CategoryEntity.builder()
                .category(category)
                .createdBy("user")
                .createdTime(LocalDateTime.now())
                .lastModifiedBy("user")
                .lastModifiedTime(LocalDateTime.now())
                .build();
        lvl1CategoryRepository.persist(lvl1Category);
        CoaEntity coa = CoaEntity.builder()
                .coaText("COA" + coaId)
                .coaDescription("desc")
                .lvl1Category(lvl1Category)
                .clientName(CLIENT_NAME)
                .sign(true)
                .isActive(true)
                .build();

        entityManager.persist(coa);
        return coa;
    }
}
