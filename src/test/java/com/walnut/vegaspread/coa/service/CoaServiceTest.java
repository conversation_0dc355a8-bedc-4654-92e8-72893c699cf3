package com.walnut.vegaspread.coa.service;

import com.fasterxml.jackson.databind.MappingIterator;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;
import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.model.CoaEntityDto;
import com.walnut.vegaspread.coa.model.CoaListDto;
import com.walnut.vegaspread.coa.model.CoaUpdateUploadBean;
import com.walnut.vegaspread.coa.model.CoaUploadBean;
import com.walnut.vegaspread.coa.repository.CoaRepository;
import com.walnut.vegaspread.coa.repository.Lvl1CategoryRepository;
import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import io.quarkus.test.InjectMock;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.quarkus.test.security.oidc.Claim;
import io.quarkus.test.security.oidc.OidcSecurity;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.flywaydb.core.Flyway;
import org.jboss.resteasy.reactive.multipart.FileUpload;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CoaServiceTest {

    private static final String CLIENT_NAME = "walnut";

    @Inject
    Flyway flyway;
    @Inject
    CoaService coaService;
    @Inject
    CoaRepository coaRepository;
    @InjectMock
    JsonWebToken accessToken;
    @InjectMock
    ExchangeService exchangeService;
    @Inject
    EntityManager entityManager;
    @Inject
    Lvl1CategoryRepository lvl1CategoryRepository;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @BeforeEach
    void setUp() {
        when(accessToken.getClaim("client_name")).thenReturn(CLIENT_NAME);
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Transactional
    List<Lvl1CategoryEntity> addNewCategory(List<String> categories) {
        List<Lvl1CategoryEntity> lvl1CategoryEntities = categories.stream().map(category -> Lvl1CategoryEntity.builder()
                .category(category)
                .createdBy("user")
                .createdTime(LocalDateTime.now())
                .lastModifiedBy("user")
                .lastModifiedTime(LocalDateTime.now())
                .build()).toList();
        lvl1CategoryRepository.persist(lvl1CategoryEntities);
        return lvl1CategoryEntities;
    }

    @Test
    @Transactional
    void testCreate_Success() {
        Lvl1CategoryEntity lvl1Category = addNewCategory(List.of("Current Assets")).get(0);
        List<CoaEntityDto.Create> createDtos = List.of(
                new CoaEntityDto.Create("1.BS.CA.Cash", "1.BS.CA.Cash", lvl1Category.getId(), true, true)
        );

        List<CoaEntity> results = coaService.create(createDtos, CLIENT_NAME);

        assertNotNull(results);
        assertEquals(1, results.size());
        for (int i = 0; i < results.size(); i++) {
            assertEquals(i + 1, results.get(i).coaId);
            assertEquals(CLIENT_NAME, results.get(i).clientName);
            assertEquals(createDtos.get(i).coaText(), results.get(i).coaText);
            assertEquals(createDtos.get(i).coaDescription(), results.get(i).coaDescription);
            assertEquals(createDtos.get(i).lvl1CategoryId(), results.get(i).getLvl1Category().getId());
            assertEquals(createDtos.get(i).isActive(), results.get(i).isActive);
            assertEquals(createDtos.get(i).sign(), results.get(i).getSign());
        }
    }

    @Test
    void testCreate_NullClientName_ThrowsException() {
        Lvl1CategoryEntity lvl1Category = addNewCategory(List.of("Current Assets")).get(0);
        List<CoaEntityDto.Create> createDtos = List.of(
                new CoaEntityDto.Create("1.BS.CA.Cash", "1.BS.CA.Cash", lvl1Category.getId(), true, true)
        );

        WebApplicationException ex = assertThrows(WebApplicationException.class,
                () -> coaService.create(createDtos, null));
        assertEquals(Response.Status.BAD_REQUEST.getStatusCode(), ex.getResponse().getStatus());
        ResponseException.ErrorResponse error = (ResponseException.ErrorResponse) ex.getResponse().getEntity();
        assertEquals("Client name cannot be null", error.message());
    }

    @Test
    @Transactional
    void testUpdate_Success() {
        Lvl1CategoryEntity currentAssetsCategory = addNewCategory(List.of("Current Assets")).get(0);
        CoaEntity entity = createEntity("1.BS.CA.Cash", "1.BS.CA.Cash", currentAssetsCategory, true, true, CLIENT_NAME);

        List<CoaEntityDto.Update> updates = List.of(
                new CoaEntityDto.Update(entity.coaId, Optional.of("1.BS.CA.Cash - Updated"),
                        Optional.of("1.BS.CA.Cash Updated Desc"), Optional.of(currentAssetsCategory.getId()),
                        Optional.of(false), Optional.of(false))
        );

        List<CoaEntity> updatedEntities = coaService.update(updates);
        assertEquals(1, updatedEntities.size());
        for (int i = 0; i < updatedEntities.size(); i++) {
            assertEquals(updates.get(i).coaId(), updatedEntities.get(i).coaId);
            assertEquals(updates.get(i).coaText().get(), updatedEntities.get(i).coaText);
            assertEquals(updates.get(i).coaDescription().get(), updatedEntities.get(i).coaDescription);
            assertEquals(updates.get(i).lvl1CategoryId().get(), updatedEntities.get(i).getLvl1Category().getId());
            assertEquals(updates.get(i).isActive().get(), updatedEntities.get(i).isActive);
            assertEquals(updates.get(i).sign().get(), updatedEntities.get(i).getSign());
            assertEquals(CLIENT_NAME, updatedEntities.get(i).clientName);
        }
    }

    @Test
    @Transactional
    void testUpdate_EntityNotFound() {
        List<CoaEntityDto.Update> updates = List.of(
                new CoaEntityDto.Update(999, Optional.of("text"), Optional.empty(), Optional.empty(), Optional.empty(),
                        Optional.empty())
        );

        List<CoaEntity> updated = coaService.update(updates);
        assertTrue(updated.isEmpty());
    }

    @Test
    @Transactional
    void testList_WithClientName() {
        Lvl1CategoryEntity currentAssetsCategory = addNewCategory(List.of("Current Assets")).get(0);
        List<CoaEntity> coaEntities = List.of(
                createEntity("1.BS.CA.Cash", "1.BS.CA.Cash", currentAssetsCategory, true, true, CLIENT_NAME),
                createEntity("1.BS.CA.Fixed Deposits", "1.BS.CA.Fixed Deposits", currentAssetsCategory, true, true,
                        "rcbc")
        );

        List<CoaEntity> coaList = coaService.list(CLIENT_NAME, true);
        assertFalse(coaList.isEmpty());
        assertEquals(coaEntities.stream().filter(e -> e.clientName.equals(CLIENT_NAME)).count(), coaList.size());
        assertTrue(coaList.stream().allMatch(e -> e.clientName.equals(CLIENT_NAME)));
    }

    @Test
    @Transactional
    void testDelete_SoftDeletes() {
        Lvl1CategoryEntity currentAssetsCategory = addNewCategory(List.of("Current Assets")).get(0);
        CoaEntity entity = createEntity("1.BS.CA.Cash", "1.BS.CA.Cash", currentAssetsCategory, true, true, CLIENT_NAME);

        coaService.delete(entity.coaId);
        CoaEntity found = coaRepository.findById(entity.coaId);
        assertFalse(found.isActive);
    }

    @Test
    void testDelete_NotFound() {
        WebApplicationException ex = assertThrows(WebApplicationException.class, () -> coaService.delete(999));
        assertEquals(Response.Status.NOT_FOUND.getStatusCode(), ex.getResponse().getStatus());
        ResponseException.ErrorResponse error = (ResponseException.ErrorResponse) ex.getResponse().getEntity();
        assertEquals("No COA found with id 999", error.message());
    }

    @Test
    @Transactional
    void testGet_ValidIds() {
        Lvl1CategoryEntity currentAssetsCategory = addNewCategory(List.of("Current Assets")).get(0);
        List<CoaEntity> entities = List.of(
                createEntity("1.BS.CA.Cash", "1.BS.CA.Cash", currentAssetsCategory, true, true, CLIENT_NAME),
                createEntity("1.BS.CA.Fixed Deposits", "1.BS.CA.Fixed Deposits", currentAssetsCategory, true, true,
                        CLIENT_NAME)
        );

        List<CoaItemDto> dtos = coaService.get(entities.stream().map(CoaEntity::getCoaId).toList());
        assertEquals(entities.size(), dtos.size());
    }

    @Test
    void testGet_EmptyIds_ReturnsEmptyList() {
        assertTrue(coaService.get(Collections.emptyList()).isEmpty());
        assertTrue(coaService.get(null).isEmpty());
    }

    @Test
    @TestSecurity(user = "test-user", roles = "SUPERADMIN")
    @OidcSecurity(claims = {@Claim(key = "client_name", value = "walnut")})
    void testUploadCsv_Success() throws IOException {
        FileUpload fileUpload = mock(FileUpload.class);
        Path path = Paths.get("src/test/resources/coa-upload.csv");
        addNewCategory(List.of("Current Assets"));

        when(fileUpload.uploadedFile()).thenReturn(path);
        when(fileUpload.filePath()).thenReturn(path);

        List<CoaUploadBean> results = coaService.uploadCsv(fileUpload);
        List<CoaUploadBean> expected = readExpectedCoaBeans("coa-upload.csv");
        assertNotNull(results);
        assertFalse(results.isEmpty());
        for (int i = 0; i < expected.size(); i++) {
            assertEquals(expected.get(i).getCoaText(), results.get(i).getCoaText());
            assertEquals(expected.get(i).getCoaDescription(), results.get(i).getCoaDescription());
            assertEquals(expected.get(i).getClientName(), results.get(i).getClientName());
            assertEquals(expected.get(i).getLvl1Category(), results.get(i).getLvl1Category());
            assertEquals(expected.get(i).getIsActive(), results.get(i).getIsActive());
            assertEquals(expected.get(i).getSign(), results.get(i).getSign());
        }
    }

    private List<CoaUploadBean> readExpectedCoaBeans(String fileName) throws IOException {
        CsvMapper mapper = new CsvMapper();
        CsvSchema schema = CsvSchema.emptySchema().withHeader();
        File file = Paths.get("src/test/resources/" + fileName).toFile();
        return mapper.readerFor(CoaUploadBean.class).with(schema).<CoaUploadBean>readValues(file).readAll();
    }

    @Test
    void testUploadCsv_IOException() {
        FileUpload fileUpload = mock(FileUpload.class);
        Path invalidPath = Paths.get("nonexistent.csv");
        when(fileUpload.uploadedFile()).thenReturn(invalidPath);
        when(fileUpload.filePath()).thenReturn(invalidPath);

        assertThrows(IOException.class, () -> coaService.uploadCsv(fileUpload));
    }

    @Test
    void testUploadUpdateCsv_Success() throws IOException {
        Lvl1CategoryEntity currentAssetsCategory = addNewCategory(List.of("Current Assets")).get(0);
        CoaEntity entity1 = createEntity("1.BS.CA.Cash", "1.BS.CA.Cash", currentAssetsCategory, true, true,
                CLIENT_NAME);
        CoaEntity entity2 = createEntity("1.BS.CA.Fixed Deposits", "1.BS.CA.Fixed Deposits", currentAssetsCategory,
                true,
                true,
                CLIENT_NAME);
        CoaEntity entity3 = createEntity("1.BS.CA.Inventory", "1.BS.CA.Inventory", currentAssetsCategory, true, true,
                CLIENT_NAME);

        Path path = Paths.get("src/test/resources/coa-update.csv");
        FileUpload fileUpload = mock(FileUpload.class);
        when(fileUpload.uploadedFile()).thenReturn(path);
        when(fileUpload.filePath()).thenReturn(path);

        List<CoaEntity> updatedEntities = coaService.uploadUpdateCsv(fileUpload);

        List<CoaUpdateUploadBean> expectedBeans = readExpectedUpdatedBeans("coa-update.csv");

        assertNotNull(updatedEntities);
        assertEquals(expectedBeans.size(), updatedEntities.size());

        for (int i = 0; i < updatedEntities.size(); i++) {
            CoaEntity updatedEntity = updatedEntities.get(i);
            CoaUpdateUploadBean expectedBean = expectedBeans.get(i);
            assertEquals(expectedBean.getCoaId(), updatedEntity.coaId);
            assertEquals(expectedBean.getCoaText(), updatedEntity.coaText);
            assertEquals(expectedBean.getCoaDescription(), updatedEntity.coaDescription);
            assertEquals(expectedBean.getLvl1Category(), updatedEntity.getLvl1Category().getCategory());
            assertEquals(expectedBean.getIsActive(), updatedEntity.isActive);
            assertEquals(expectedBean.getSign(), updatedEntity.getSign());
        }
    }

    private List<CoaUpdateUploadBean> readExpectedUpdatedBeans(String fileName) throws IOException {
        CsvMapper mapper = new CsvMapper();
        CsvSchema schema = CsvSchema.emptySchema().withHeader();
        File file = Paths.get("src/test/resources/" + fileName).toFile();

        MappingIterator<CoaUpdateUploadBean> iterator =
                mapper.readerFor(CoaUpdateUploadBean.class)
                        .with(schema)
                        .readValues(file);

        return iterator.readAll();
    }

    @Test
    void testListWithSortAndSearch_AllItems() {
        Lvl1CategoryEntity lvl1Category = addNewCategory(List.of("Current Assets")).get(0);
        List<CoaEntity> entities = List.of(
                createEntity("1.BS.CA.Cash", "1.BS.CA.Cash", lvl1Category, true, true, CLIENT_NAME),
                createEntity("1.BS.CA.Fixed Deposits", "1.BS.CA.Fixed Deposits", lvl1Category, true, true,
                        CLIENT_NAME)
        );

        CoaListDto.GetSortAndSearchCoaList search = new CoaListDto.GetSortAndSearchCoaList(List.of(), "");
        List<CoaEntity> result = coaService.listWithSortAndSearch(search, CLIENT_NAME, false);
        assertNotNull(result);
        assertEquals(entities.size(), result.size());
    }

    @Transactional
    CoaEntity createEntity(String coaText, String description, Lvl1CategoryEntity lvl1Category, boolean isActive,
                           boolean sign,
                           String clientName) {
        CoaEntity entity = CoaEntity.builder()
                .coaText(coaText)
                .coaDescription(description)
                .lvl1Category(lvl1Category)
                .isActive(isActive)
                .sign(sign)
                .clientName(clientName)
                .build();
        coaRepository.persist(entity);
        return entity;
    }
}
