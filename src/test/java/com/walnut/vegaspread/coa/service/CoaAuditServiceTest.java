package com.walnut.vegaspread.coa.service;

import com.walnut.vegaspread.coa.audit.CoaEntityMapper;
import com.walnut.vegaspread.coa.audit.CoaEntityRollbackStrategy;
import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.repository.CoaRepository;
import com.walnut.vegaspread.coa.repository.Lvl1CategoryRepository;
import com.walnut.vegaspread.common.model.audit.envers.AuditFilterDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditRequestDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditSortDto;
import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import com.walnut.vegaspread.common.service.audit.envers.GenericAuditService;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.RevisionType;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;
import java.util.List;

import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static org.junit.jupiter.api.Assertions.*;

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CoaAuditServiceTest {

    private static final String CLIENT_NAME = "walnut";
    private static final String TEST_USER = "testUser";

    @Inject
    CoaAuditService coaAuditService;

    @Inject
    CoaRepository coaRepository;

    @Inject
    Lvl1CategoryRepository lvl1CategoryRepository;

    @Inject
    CoaEntityMapper coaEntityMapper;

    @Inject
    CoaEntityRollbackStrategy rollbackStrategy;

    @Inject
    EntityManager entityManager;

    @Inject
    Flyway flyway;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_BasicRequest() {
        // Given: Create test data
        Lvl1CategoryEntity category = createTestCategory("Assets");
        CoaEntity coaEntity = createTestCoaEntity("1.BS.CA.Cash", "Cash Account", category);

        // Modify entity to create audit records
        coaEntity.setCoaText("1.BS.CA.Cash.Updated");
        coaEntity.setCoaDescription("Updated Cash Account");
        entityManager.persist(coaEntity);
        entityManager.flush();

        // When: Request audit data
        AuditRequestDto request = new AuditRequestDto(
                List.of(),
                List.of(),
                1,
                10
        );

        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> response =
                coaAuditService.getPaginatedAuditsAsDto(request, coaEntityMapper, entityManager);

        // Then: Verify response
        assertNotNull(response);
        assertTrue(response.totalCount() > 0);
        assertFalse(response.audits().isEmpty());
        assertEquals(1, response.currentPage());
        assertTrue(response.totalPages() >= 1);
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_WithFilters() {
        // Given: Create test data
        Lvl1CategoryEntity category = createTestCategory("Assets");
        CoaEntity coaEntity1 = createTestCoaEntity("1.BS.CA.Cash", "Cash Account", category);
        CoaEntity coaEntity2 = createTestCoaEntity("1.BS.CA.Bank", "Bank Account", category);

        // When: Request audit data with filter
        AuditFilterDto filter = new AuditFilterDto(
                "coaId",
                coaEntity1.getCoaId(),
                AuditFilterDto.FilterOperation.EQUALS
        );

        AuditRequestDto request = new AuditRequestDto(
                List.of(filter),
                List.of(),
                1,
                10
        );

        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> response =
                coaAuditService.getPaginatedAuditsAsDto(request, coaEntityMapper, entityManager);

        // Then: Verify filtered results
        assertNotNull(response);
        response.audits().forEach(audit -> {
            assertEquals(coaEntity1.getCoaId(), audit.entity().coaId());
        });
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_WithSorting() {
        // Given: Create test data with multiple revisions
        Lvl1CategoryEntity category = createTestCategory("Assets");
        CoaEntity coaEntity = createTestCoaEntity("1.BS.CA.Cash", "Cash Account", category);

        // Create multiple revisions
        coaEntity.setCoaText("1.BS.CA.Cash.V2");
        entityManager.persist(coaEntity);
        entityManager.flush();

        coaEntity.setCoaText("1.BS.CA.Cash.V3");
        entityManager.persist(coaEntity);
        entityManager.flush();

        // When: Request audit data with sorting
        AuditSortDto sort = new AuditSortDto("timestamp", AuditSortDto.SortDirection.DESC);
        AuditRequestDto request = new AuditRequestDto(
                List.of(),
                List.of(sort),
                1,
                10
        );

        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> response =
                coaAuditService.getPaginatedAuditsAsDto(request, coaEntityMapper, entityManager);

        // Then: Verify sorted results (most recent first)
        assertNotNull(response);
        assertTrue(response.audits().size() >= 2);
        
        // Verify descending order by revision number (proxy for timestamp)
        for (int i = 0; i < response.audits().size() - 1; i++) {
            assertTrue(response.audits().get(i).revisionNumber() >= 
                      response.audits().get(i + 1).revisionNumber());
        }
    }

    @Test
    @Transactional
    void testGetAuditForCoaId() {
        // Given: Create test data
        Lvl1CategoryEntity category = createTestCategory("Assets");
        CoaEntity coaEntity = createTestCoaEntity("1.BS.CA.Cash", "Cash Account", category);

        // Modify entity to create audit records
        coaEntity.setCoaDescription("Updated Cash Account");
        entityManager.persist(coaEntity);
        entityManager.flush();

        // When: Get audit for specific COA ID
        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> response =
                coaAuditService.getAuditForCoaId(coaEntity.getCoaId(), coaEntityMapper, entityManager);

        // Then: Verify results
        assertNotNull(response);
        assertTrue(response.totalCount() > 0);
        response.audits().forEach(audit -> {
            assertEquals(coaEntity.getCoaId(), audit.entity().coaId());
        });
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_Pagination() {
        // Given: Create multiple entities to ensure multiple audit records
        Lvl1CategoryEntity category = createTestCategory("Assets");
        for (int i = 1; i <= 5; i++) {
            createTestCoaEntity("1.BS.CA.Cash" + i, "Cash Account " + i, category);
        }

        // When: Request first page with page size 2
        AuditRequestDto request = new AuditRequestDto(
                List.of(),
                List.of(),
                1,
                2
        );

        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> response =
                coaAuditService.getPaginatedAuditsAsDto(request, coaEntityMapper, entityManager);

        // Then: Verify pagination
        assertNotNull(response);
        assertEquals(1, response.currentPage());
        assertTrue(response.totalPages() >= 1);
        assertTrue(response.audits().size() <= 2);
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_EmptyResult() {
        // Given: No audit data exists

        // When: Request audit data
        AuditRequestDto request = new AuditRequestDto(
                List.of(),
                List.of(),
                1,
                10
        );

        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> response =
                coaAuditService.getPaginatedAuditsAsDto(request, coaEntityMapper, entityManager);

        // Then: Verify empty response
        assertNotNull(response);
        assertEquals(0, response.totalCount());
        assertTrue(response.audits().isEmpty());
        assertEquals(1, response.currentPage());
        assertEquals(0, response.totalPages());
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_ComplexFilters() {
        // Given: Create test data
        Lvl1CategoryEntity category1 = createTestCategory("Assets");
        Lvl1CategoryEntity category2 = createTestCategory("Liabilities");
        
        CoaEntity coaEntity1 = createTestCoaEntity("1.BS.CA.Cash", "Cash Account", category1);
        CoaEntity coaEntity2 = createTestCoaEntity("2.BS.CL.Payable", "Accounts Payable", category2);

        // When: Request audit data with multiple filters
        List<AuditFilterDto> filters = List.of(
                new AuditFilterDto("coaText", "Cash", AuditFilterDto.FilterOperation.LIKE),
                new AuditFilterDto("isActive", true, AuditFilterDto.FilterOperation.EQUALS)
        );

        AuditRequestDto request = new AuditRequestDto(
                filters,
                List.of(),
                1,
                10
        );

        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> response =
                coaAuditService.getPaginatedAuditsAsDto(request, coaEntityMapper, entityManager);

        // Then: Verify filtered results
        assertNotNull(response);
        response.audits().forEach(audit -> {
            assertTrue(audit.entity().coaText().contains("Cash"));
            assertTrue(audit.entity().isActive());
        });
    }

    @Test
    @Transactional
    void testRollback_Success() {
        // Given: Create entity and modify it
        Lvl1CategoryEntity category = createTestCategory("Assets");
        CoaEntity coaEntity = createTestCoaEntity("1.BS.CA.Cash", "Cash Account", category);

        String originalText = coaEntity.getCoaText();
        String originalDescription = coaEntity.getCoaDescription();

        // Modify entity
        coaEntity.setCoaText("1.BS.CA.Cash.Modified");
        coaEntity.setCoaDescription("Modified Cash Account");
        entityManager.persist(coaEntity);
        entityManager.flush();

        // Get the revision number for rollback
        var auditReader = AuditReaderFactory.get(entityManager);
        var revisions = auditReader.getRevisions(CoaEntity.class, coaEntity.getCoaId());
        Number targetRevision = revisions.get(0); // First revision (creation)

        // When: Rollback to original state
        coaAuditService.rollback("test-trace-id", entityManager);
        entityManager.clear();

        // Then: Verify rollback (Note: This is a simplified test - actual rollback needs traceId correlation)
        CoaEntity rolledBackEntity = coaRepository.findById(coaEntity.getCoaId());
        assertNotNull(rolledBackEntity);
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_WithInFilter() {
        // Given: Create multiple entities
        Lvl1CategoryEntity category = createTestCategory("Assets");
        CoaEntity coaEntity1 = createTestCoaEntity("1.BS.CA.Cash", "Cash Account", category);
        CoaEntity coaEntity2 = createTestCoaEntity("1.BS.CA.Bank", "Bank Account", category);
        CoaEntity coaEntity3 = createTestCoaEntity("2.BS.CL.Payable", "Accounts Payable", category);

        // When: Request audit data with IN filter
        AuditFilterDto filter = new AuditFilterDto(
                "coaId",
                List.of(coaEntity1.getCoaId(), coaEntity2.getCoaId()),
                AuditFilterDto.FilterOperation.IN
        );

        AuditRequestDto request = new AuditRequestDto(
                List.of(filter),
                List.of(),
                1,
                10
        );

        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> response =
                coaAuditService.getPaginatedAuditsAsDto(request, coaEntityMapper, entityManager);

        // Then: Verify filtered results
        assertNotNull(response);
        response.audits().forEach(audit -> {
            assertTrue(List.of(coaEntity1.getCoaId(), coaEntity2.getCoaId()).contains(audit.entity().coaId()));
        });
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_WithDateRangeFilter() {
        // Given: Create test data
        Lvl1CategoryEntity category = createTestCategory("Assets");
        CoaEntity coaEntity = createTestCoaEntity("1.BS.CA.Cash", "Cash Account", category);

        // When: Request audit data with date range filter
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime yesterday = now.minusDays(1);
        LocalDateTime tomorrow = now.plusDays(1);

        AuditFilterDto filter = new AuditFilterDto(
                "timestamp",
                List.of(yesterday, tomorrow),
                AuditFilterDto.FilterOperation.BETWEEN
        );

        AuditRequestDto request = new AuditRequestDto(
                List.of(filter),
                List.of(),
                1,
                10
        );

        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> response =
                coaAuditService.getPaginatedAuditsAsDto(request, coaEntityMapper, entityManager);

        // Then: Verify results within date range
        assertNotNull(response);
        // Results should include today's audit records
        assertTrue(response.totalCount() >= 0);
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_InvalidRequest() {
        // Given: Invalid request with page number 0
        AuditRequestDto invalidRequest = new AuditRequestDto(
                List.of(),
                List.of(),
                0, // Invalid page number
                10
        );

        // When/Then: Should handle invalid request gracefully
        assertFalse(invalidRequest.isValid());
    }

    @Test
    @Transactional
    void testGetPaginatedAuditsAsDto_LargePageSize() {
        // Given: Request with maximum page size
        AuditRequestDto request = new AuditRequestDto(
                List.of(),
                List.of(),
                1,
                1000 // Maximum allowed page size
        );

        // When: Request audit data
        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> response =
                coaAuditService.getPaginatedAuditsAsDto(request, coaEntityMapper, entityManager);

        // Then: Should handle large page size
        assertNotNull(response);
        assertTrue(request.isValid());
    }

    // Helper methods
    @Transactional
    private Lvl1CategoryEntity createTestCategory(String categoryName) {
        Lvl1CategoryEntity category = Lvl1CategoryEntity.builder()
                .category(categoryName)
                .createdBy(TEST_USER)
                .createdTime(LocalDateTime.now())
                .lastModifiedBy(TEST_USER)
                .lastModifiedTime(LocalDateTime.now())
                .build();
        lvl1CategoryRepository.persist(category);
        return category;
    }

    @Transactional
    private CoaEntity createTestCoaEntity(String coaText, String description, Lvl1CategoryEntity category) {
        CoaEntity entity = CoaEntity.builder()
                .coaText(coaText)
                .coaDescription(description)
                .clientName(CLIENT_NAME)
                .isActive(true)
                .sign(true)
                .lvl1Category(category)
                .build();
        coaRepository.persist(entity);
        return entity;
    }
}
