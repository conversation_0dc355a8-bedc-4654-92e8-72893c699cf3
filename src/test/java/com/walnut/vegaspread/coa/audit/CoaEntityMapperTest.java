package com.walnut.vegaspread.coa.audit;

import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.repository.Lvl1CategoryRepository;
import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;

import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static org.junit.jupiter.api.Assertions.*;

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CoaEntityMapperTest {

    private static final String CLIENT_NAME = "walnut";
    private static final String TEST_USER = "testUser";

    @Inject
    CoaEntityMapper coaEntityMapper;

    @Inject
    Lvl1CategoryRepository lvl1CategoryRepository;

    @Inject
    EntityManager entityManager;

    @Inject
    Flyway flyway;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Test
    @Transactional
    void testToDto_CompleteEntity() {
        // Given: Create complete entity with all fields
        Lvl1CategoryEntity category = createTestCategory("Assets");
        CoaEntity entity = CoaEntity.builder()
                .coaId(1)
                .coaText("1.BS.CA.Cash")
                .coaDescription("Cash and Cash Equivalents")
                .clientName(CLIENT_NAME)
                .isActive(true)
                .sign(true)
                .lvl1Category(category)
                .build();

        // When: Map to DTO
        CoaItemDto dto = coaEntityMapper.toDto(entity);

        // Then: Verify all fields mapped correctly
        assertNotNull(dto);
        assertEquals(entity.getCoaId(), dto.coaId());
        assertEquals(entity.getCoaText(), dto.coaText());
        assertEquals(entity.getCoaDescription(), dto.coaDescription());
        assertEquals(entity.getIsActive(), dto.isActive());
        assertEquals(entity.getSign(), dto.sign());
        assertEquals(category.getId(), dto.lvl1CategoryId());
        assertEquals(category.getCategory(), dto.lvl1CategoryName());
    }

    @Test
    @Transactional
    void testToDto_MinimalEntity() {
        // Given: Create entity with minimal required fields
        Lvl1CategoryEntity category = createTestCategory("Assets");
        CoaEntity entity = CoaEntity.builder()
                .coaId(2)
                .coaText("1.BS.CA.Bank")
                .coaDescription("Bank Account")
                .clientName(CLIENT_NAME)
                .lvl1Category(category)
                .build();
        // isActive and sign are null

        // When: Map to DTO
        CoaItemDto dto = coaEntityMapper.toDto(entity);

        // Then: Verify mapping with null values
        assertNotNull(dto);
        assertEquals(entity.getCoaId(), dto.coaId());
        assertEquals(entity.getCoaText(), dto.coaText());
        assertEquals(entity.getCoaDescription(), dto.coaDescription());
        assertNull(dto.isActive());
        assertNull(dto.sign());
        assertEquals(category.getId(), dto.lvl1CategoryId());
        assertEquals(category.getCategory(), dto.lvl1CategoryName());
    }

    @Test
    @Transactional
    void testToDto_NullCategory() {
        // Given: Create entity with null category
        CoaEntity entity = CoaEntity.builder()
                .coaId(3)
                .coaText("1.BS.CA.Investment")
                .coaDescription("Investment Account")
                .clientName(CLIENT_NAME)
                .isActive(true)
                .sign(false)
                .lvl1Category(null) // Null category
                .build();

        // When: Map to DTO
        CoaItemDto dto = coaEntityMapper.toDto(entity);

        // Then: Verify mapping with null category
        assertNotNull(dto);
        assertEquals(entity.getCoaId(), dto.coaId());
        assertEquals(entity.getCoaText(), dto.coaText());
        assertEquals(entity.getCoaDescription(), dto.coaDescription());
        assertEquals(entity.getIsActive(), dto.isActive());
        assertEquals(entity.getSign(), dto.sign());
        assertNull(dto.lvl1CategoryId());
        assertNull(dto.lvl1CategoryName());
    }

    @Test
    void testToDto_NullEntity() {
        // When: Map null entity
        CoaItemDto dto = coaEntityMapper.toDto(null);

        // Then: Should return null
        assertNull(dto);
    }

    @Test
    @Transactional
    void testMapLvl1CategoryId_ValidCategory() {
        // Given: Create category
        Lvl1CategoryEntity category = createTestCategory("Assets");

        // When: Map category ID
        Integer categoryId = CoaEntityMapper.mapLvl1CategoryId(category);

        // Then: Verify correct ID returned
        assertEquals(category.getId(), categoryId);
    }

    @Test
    void testMapLvl1CategoryId_NullCategory() {
        // When: Map null category
        Integer categoryId = CoaEntityMapper.mapLvl1CategoryId(null);

        // Then: Should return null
        assertNull(categoryId);
    }

    @Test
    @Transactional
    void testMapLvl1CategoryName_ValidCategory() {
        // Given: Create category
        Lvl1CategoryEntity category = createTestCategory("Liabilities");

        // When: Map category name
        String categoryName = CoaEntityMapper.mapLvl1CategoryName(category);

        // Then: Verify correct name returned
        assertEquals(category.getCategory(), categoryName);
    }

    @Test
    void testMapLvl1CategoryName_NullCategory() {
        // When: Map null category
        String categoryName = CoaEntityMapper.mapLvl1CategoryName(null);

        // Then: Should return null
        assertNull(categoryName);
    }

    @Test
    @Transactional
    void testToDto_BooleanValues() {
        // Given: Create entity with different boolean combinations
        Lvl1CategoryEntity category = createTestCategory("Assets");
        
        // Test case 1: Both true
        CoaEntity entity1 = CoaEntity.builder()
                .coaId(4)
                .coaText("1.BS.CA.Cash1")
                .coaDescription("Cash Account 1")
                .clientName(CLIENT_NAME)
                .isActive(true)
                .sign(true)
                .lvl1Category(category)
                .build();

        // Test case 2: Both false
        CoaEntity entity2 = CoaEntity.builder()
                .coaId(5)
                .coaText("1.BS.CA.Cash2")
                .coaDescription("Cash Account 2")
                .clientName(CLIENT_NAME)
                .isActive(false)
                .sign(false)
                .lvl1Category(category)
                .build();

        // Test case 3: Mixed values
        CoaEntity entity3 = CoaEntity.builder()
                .coaId(6)
                .coaText("1.BS.CA.Cash3")
                .coaDescription("Cash Account 3")
                .clientName(CLIENT_NAME)
                .isActive(true)
                .sign(false)
                .lvl1Category(category)
                .build();

        // When: Map to DTOs
        CoaItemDto dto1 = coaEntityMapper.toDto(entity1);
        CoaItemDto dto2 = coaEntityMapper.toDto(entity2);
        CoaItemDto dto3 = coaEntityMapper.toDto(entity3);

        // Then: Verify boolean values mapped correctly
        assertTrue(dto1.isActive());
        assertTrue(dto1.sign());

        assertFalse(dto2.isActive());
        assertFalse(dto2.sign());

        assertTrue(dto3.isActive());
        assertFalse(dto3.sign());
    }

    @Test
    @Transactional
    void testToDto_SpecialCharacters() {
        // Given: Create entity with special characters
        Lvl1CategoryEntity category = createTestCategory("Assets & Investments");
        CoaEntity entity = CoaEntity.builder()
                .coaId(7)
                .coaText("1.BS.CA.Cash & Equivalents")
                .coaDescription("Cash, Bank & Short-term Investments (USD $)")
                .clientName(CLIENT_NAME)
                .isActive(true)
                .sign(true)
                .lvl1Category(category)
                .build();

        // When: Map to DTO
        CoaItemDto dto = coaEntityMapper.toDto(entity);

        // Then: Verify special characters preserved
        assertNotNull(dto);
        assertEquals("1.BS.CA.Cash & Equivalents", dto.coaText());
        assertEquals("Cash, Bank & Short-term Investments (USD $)", dto.coaDescription());
        assertEquals("Assets & Investments", dto.lvl1CategoryName());
    }

    @Test
    @Transactional
    void testToDto_LongStrings() {
        // Given: Create entity with long strings
        String longText = "1.BS.CA.".repeat(50) + "VeryLongAccountName";
        String longDescription = "This is a very long description that contains many words and should test the mapping of long strings properly. ".repeat(10);
        String longCategoryName = "Very Long Category Name That Exceeds Normal Length";

        Lvl1CategoryEntity category = createTestCategory(longCategoryName);
        CoaEntity entity = CoaEntity.builder()
                .coaId(8)
                .coaText(longText)
                .coaDescription(longDescription)
                .clientName(CLIENT_NAME)
                .isActive(true)
                .sign(true)
                .lvl1Category(category)
                .build();

        // When: Map to DTO
        CoaItemDto dto = coaEntityMapper.toDto(entity);

        // Then: Verify long strings mapped correctly
        assertNotNull(dto);
        assertEquals(longText, dto.coaText());
        assertEquals(longDescription, dto.coaDescription());
        assertEquals(longCategoryName, dto.lvl1CategoryName());
    }

    @Test
    @Transactional
    void testToDto_CategoryWithNullFields() {
        // Given: Create category with minimal fields and entity
        Lvl1CategoryEntity category = Lvl1CategoryEntity.builder()
                .category("Test Category")
                .createdBy(TEST_USER)
                .createdTime(LocalDateTime.now())
                .lastModifiedBy(TEST_USER)
                .lastModifiedTime(LocalDateTime.now())
                .build();
        lvl1CategoryRepository.persist(category);

        CoaEntity entity = CoaEntity.builder()
                .coaId(9)
                .coaText("1.BS.CA.Test")
                .coaDescription("Test Account")
                .clientName(CLIENT_NAME)
                .isActive(true)
                .sign(true)
                .lvl1Category(category)
                .build();

        // When: Map to DTO
        CoaItemDto dto = coaEntityMapper.toDto(entity);

        // Then: Verify mapping works with category having all required fields
        assertNotNull(dto);
        assertEquals(category.getId(), dto.lvl1CategoryId());
        assertEquals(category.getCategory(), dto.lvl1CategoryName());
    }

    // Helper methods
    @Transactional
    private Lvl1CategoryEntity createTestCategory(String categoryName) {
        Lvl1CategoryEntity category = Lvl1CategoryEntity.builder()
                .category(categoryName)
                .createdBy(TEST_USER)
                .createdTime(LocalDateTime.now())
                .lastModifiedBy(TEST_USER)
                .lastModifiedTime(LocalDateTime.now())
                .build();
        lvl1CategoryRepository.persist(category);
        return category;
    }
}
