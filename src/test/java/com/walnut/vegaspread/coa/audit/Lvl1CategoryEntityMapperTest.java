package com.walnut.vegaspread.coa.audit;

import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.model.Lvl1CategoryAuditDto;
import com.walnut.vegaspread.coa.repository.Lvl1CategoryRepository;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;

import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static org.junit.jupiter.api.Assertions.*;

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class Lvl1CategoryEntityMapperTest {

    private static final String TEST_USER = "testUser";

    @Inject
    Lvl1CategoryEntityMapper lvl1CategoryEntityMapper;

    @Inject
    Lvl1CategoryRepository lvl1CategoryRepository;

    @Inject
    EntityManager entityManager;

    @Inject
    Flyway flyway;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Test
    @Transactional
    void testToDto_CompleteEntity() {
        // Given: Create complete entity with all fields
        Lvl1CategoryEntity entity = createTestCategory("Assets");

        // When: Map to DTO
        Lvl1CategoryAuditDto dto = lvl1CategoryEntityMapper.toDto(entity);

        // Then: Verify all fields mapped correctly
        assertNotNull(dto);
        assertEquals(entity.getId(), dto.id());
        assertEquals(entity.getCategory(), dto.category());
    }

    @Test
    @Transactional
    void testToDto_MinimalEntity() {
        // Given: Create entity with minimal required fields
        Lvl1CategoryEntity entity = Lvl1CategoryEntity.builder()
                .category("Minimal Category")
                .createdBy(TEST_USER)
                .createdTime(LocalDateTime.now())
                .lastModifiedBy(TEST_USER)
                .lastModifiedTime(LocalDateTime.now())
                .build();
        lvl1CategoryRepository.persist(entity);

        // When: Map to DTO
        Lvl1CategoryAuditDto dto = lvl1CategoryEntityMapper.toDto(entity);

        // Then: Verify mapping
        assertNotNull(dto);
        assertEquals(entity.getId(), dto.id());
        assertEquals("Minimal Category", dto.category());
    }

    @Test
    void testToDto_NullEntity() {
        // When: Map null entity
        Lvl1CategoryAuditDto dto = lvl1CategoryEntityMapper.toDto(null);

        // Then: Should return null
        assertNull(dto);
    }

    @Test
    @Transactional
    void testToDto_NullCategoryName() {
        // Given: Create entity with null category name
        Lvl1CategoryEntity entity = Lvl1CategoryEntity.builder()
                .category(null) // Null category name
                .createdBy(TEST_USER)
                .createdTime(LocalDateTime.now())
                .lastModifiedBy(TEST_USER)
                .lastModifiedTime(LocalDateTime.now())
                .build();
        lvl1CategoryRepository.persist(entity);

        // When: Map to DTO
        Lvl1CategoryAuditDto dto = lvl1CategoryEntityMapper.toDto(entity);

        // Then: Verify mapping with null category
        assertNotNull(dto);
        assertEquals(entity.getId(), dto.id());
        assertNull(dto.category());
    }

    @Test
    @Transactional
    void testToDto_EmptyStringCategory() {
        // Given: Create entity with empty string category
        Lvl1CategoryEntity entity = Lvl1CategoryEntity.builder()
                .category("") // Empty string
                .createdBy(TEST_USER)
                .createdTime(LocalDateTime.now())
                .lastModifiedBy(TEST_USER)
                .lastModifiedTime(LocalDateTime.now())
                .build();
        lvl1CategoryRepository.persist(entity);

        // When: Map to DTO
        Lvl1CategoryAuditDto dto = lvl1CategoryEntityMapper.toDto(entity);

        // Then: Verify mapping with empty string
        assertNotNull(dto);
        assertEquals(entity.getId(), dto.id());
        assertEquals("", dto.category());
    }

    @Test
    @Transactional
    void testToDto_SpecialCharacters() {
        // Given: Create entity with special characters in category name
        String categoryWithSpecialChars = "Assets & Investments (USD $) - Level 1";
        Lvl1CategoryEntity entity = createTestCategory(categoryWithSpecialChars);

        // When: Map to DTO
        Lvl1CategoryAuditDto dto = lvl1CategoryEntityMapper.toDto(entity);

        // Then: Verify special characters preserved
        assertNotNull(dto);
        assertEquals(entity.getId(), dto.id());
        assertEquals(categoryWithSpecialChars, dto.category());
    }

    @Test
    @Transactional
    void testToDto_LongCategoryName() {
        // Given: Create entity with long category name
        String longCategoryName = "Very Long Category Name That Exceeds Normal Length And Contains Many Words To Test The Mapping Functionality With Extended Text Content";
        Lvl1CategoryEntity entity = createTestCategory(longCategoryName);

        // When: Map to DTO
        Lvl1CategoryAuditDto dto = lvl1CategoryEntityMapper.toDto(entity);

        // Then: Verify long string mapped correctly
        assertNotNull(dto);
        assertEquals(entity.getId(), dto.id());
        assertEquals(longCategoryName, dto.category());
    }

    @Test
    @Transactional
    void testToDto_UnicodeCharacters() {
        // Given: Create entity with Unicode characters
        String unicodeCategoryName = "资产 Assets Активы أصول";
        Lvl1CategoryEntity entity = createTestCategory(unicodeCategoryName);

        // When: Map to DTO
        Lvl1CategoryAuditDto dto = lvl1CategoryEntityMapper.toDto(entity);

        // Then: Verify Unicode characters preserved
        assertNotNull(dto);
        assertEquals(entity.getId(), dto.id());
        assertEquals(unicodeCategoryName, dto.category());
    }

    @Test
    @Transactional
    void testToDto_WhitespaceHandling() {
        // Given: Create entities with various whitespace scenarios
        Lvl1CategoryEntity entity1 = createTestCategory("  Leading Spaces");
        Lvl1CategoryEntity entity2 = createTestCategory("Trailing Spaces  ");
        Lvl1CategoryEntity entity3 = createTestCategory("  Both Sides  ");
        Lvl1CategoryEntity entity4 = createTestCategory("Multiple   Internal   Spaces");

        // When: Map to DTOs
        Lvl1CategoryAuditDto dto1 = lvl1CategoryEntityMapper.toDto(entity1);
        Lvl1CategoryAuditDto dto2 = lvl1CategoryEntityMapper.toDto(entity2);
        Lvl1CategoryAuditDto dto3 = lvl1CategoryEntityMapper.toDto(entity3);
        Lvl1CategoryAuditDto dto4 = lvl1CategoryEntityMapper.toDto(entity4);

        // Then: Verify whitespace preserved (no trimming by mapper)
        assertEquals("  Leading Spaces", dto1.category());
        assertEquals("Trailing Spaces  ", dto2.category());
        assertEquals("  Both Sides  ", dto3.category());
        assertEquals("Multiple   Internal   Spaces", dto4.category());
    }

    @Test
    @Transactional
    void testToDto_NumericIds() {
        // Given: Create multiple entities to test different ID values
        Lvl1CategoryEntity entity1 = createTestCategory("Category 1");
        Lvl1CategoryEntity entity2 = createTestCategory("Category 2");
        Lvl1CategoryEntity entity3 = createTestCategory("Category 3");

        // When: Map to DTOs
        Lvl1CategoryAuditDto dto1 = lvl1CategoryEntityMapper.toDto(entity1);
        Lvl1CategoryAuditDto dto2 = lvl1CategoryEntityMapper.toDto(entity2);
        Lvl1CategoryAuditDto dto3 = lvl1CategoryEntityMapper.toDto(entity3);

        // Then: Verify IDs are correctly mapped and different
        assertNotNull(dto1.id());
        assertNotNull(dto2.id());
        assertNotNull(dto3.id());
        
        assertNotEquals(dto1.id(), dto2.id());
        assertNotEquals(dto2.id(), dto3.id());
        assertNotEquals(dto1.id(), dto3.id());
    }

    @Test
    @Transactional
    void testToDto_ConsistentMapping() {
        // Given: Create entity
        Lvl1CategoryEntity entity = createTestCategory("Consistent Test Category");

        // When: Map to DTO multiple times
        Lvl1CategoryAuditDto dto1 = lvl1CategoryEntityMapper.toDto(entity);
        Lvl1CategoryAuditDto dto2 = lvl1CategoryEntityMapper.toDto(entity);

        // Then: Verify consistent mapping results
        assertNotNull(dto1);
        assertNotNull(dto2);
        assertEquals(dto1.id(), dto2.id());
        assertEquals(dto1.category(), dto2.category());
    }

    @Test
    @Transactional
    void testToDto_CommonCategoryNames() {
        // Given: Create entities with common accounting category names
        String[] commonCategories = {
                "Assets",
                "Liabilities", 
                "Equity",
                "Revenue",
                "Expenses",
                "Current Assets",
                "Non-Current Assets",
                "Current Liabilities",
                "Long-term Liabilities",
                "Retained Earnings"
        };

        // When/Then: Test mapping for each common category
        for (String categoryName : commonCategories) {
            Lvl1CategoryEntity entity = createTestCategory(categoryName);
            Lvl1CategoryAuditDto dto = lvl1CategoryEntityMapper.toDto(entity);
            
            assertNotNull(dto, "DTO should not be null for category: " + categoryName);
            assertEquals(entity.getId(), dto.id(), "ID should match for category: " + categoryName);
            assertEquals(categoryName, dto.category(), "Category name should match for: " + categoryName);
        }
    }

    // Helper methods
    @Transactional
    private Lvl1CategoryEntity createTestCategory(String categoryName) {
        Lvl1CategoryEntity category = Lvl1CategoryEntity.builder()
                .category(categoryName)
                .createdBy(TEST_USER)
                .createdTime(LocalDateTime.now())
                .lastModifiedBy(TEST_USER)
                .lastModifiedTime(LocalDateTime.now())
                .build();
        lvl1CategoryRepository.persist(category);
        return category;
    }
}
