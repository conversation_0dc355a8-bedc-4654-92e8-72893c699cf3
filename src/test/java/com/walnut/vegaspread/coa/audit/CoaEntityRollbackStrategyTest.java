package com.walnut.vegaspread.coa.audit;

import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.repository.CoaRepository;
import com.walnut.vegaspread.coa.repository.Lvl1CategoryRepository;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.hibernate.envers.RevisionType;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;

import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static org.junit.jupiter.api.Assertions.*;

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CoaEntityRollbackStrategyTest {

    private static final String CLIENT_NAME = "walnut";
    private static final String TEST_USER = "testUser";

    @Inject
    CoaEntityRollbackStrategy rollbackStrategy;

    @Inject
    CoaRepository coaRepository;

    @Inject
    Lvl1CategoryRepository lvl1CategoryRepository;

    @Inject
    EntityManager entityManager;

    @Inject
    Flyway flyway;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Test
    void testGetEntityClass() {
        // When/Then
        assertEquals(CoaEntity.class, rollbackStrategy.getEntityClass());
    }

    @Test
    void testGetEntityTypeName() {
        // When/Then
        assertEquals("CoaEntity", rollbackStrategy.getEntityTypeName());
    }

    @Test
    @Transactional
    void testFindById() {
        // Given: Create test entity
        Lvl1CategoryEntity category = createTestCategory("Assets");
        CoaEntity coaEntity = createTestCoaEntity("1.BS.CA.Cash", "Cash Account", category);

        // When: Find by ID
        CoaEntity found = rollbackStrategy.findById(coaEntity.getCoaId());

        // Then: Verify entity found
        assertNotNull(found);
        assertEquals(coaEntity.getCoaId(), found.getCoaId());
        assertEquals(coaEntity.getCoaText(), found.getCoaText());
    }

    @Test
    @Transactional
    void testFindById_NotFound() {
        // When: Find non-existent entity
        CoaEntity found = rollbackStrategy.findById(99999);

        // Then: Should return null
        assertNull(found);
    }

    @Test
    @Transactional
    void testPersist() {
        // Given: Create test entity
        Lvl1CategoryEntity category = createTestCategory("Assets");
        CoaEntity coaEntity = CoaEntity.builder()
                .coaText("1.BS.CA.Cash")
                .coaDescription("Cash Account")
                .clientName(CLIENT_NAME)
                .isActive(true)
                .sign(true)
                .lvl1Category(category)
                .build();

        // When: Persist entity
        rollbackStrategy.persist(coaEntity);
        entityManager.flush();

        // Then: Verify entity persisted
        assertNotNull(coaEntity.getCoaId());
        CoaEntity found = coaRepository.findById(coaEntity.getCoaId());
        assertNotNull(found);
        assertEquals(coaEntity.getCoaText(), found.getCoaText());
    }

    @Test
    @Transactional
    void testDelete() {
        // Given: Create test entity
        Lvl1CategoryEntity category = createTestCategory("Assets");
        CoaEntity coaEntity = createTestCoaEntity("1.BS.CA.Cash", "Cash Account", category);
        Integer entityId = coaEntity.getCoaId();

        // When: Delete entity
        rollbackStrategy.delete(entityId);
        entityManager.flush();

        // Then: Verify entity deleted
        CoaEntity found = coaRepository.findById(entityId);
        assertNull(found);
    }

    @Test
    @Transactional
    void testGetEntityId() {
        // Given: Create test entity
        Lvl1CategoryEntity category = createTestCategory("Assets");
        CoaEntity coaEntity = createTestCoaEntity("1.BS.CA.Cash", "Cash Account", category);

        // When: Get entity ID
        Integer entityId = rollbackStrategy.getEntityId(coaEntity);

        // Then: Verify correct ID returned
        assertEquals(coaEntity.getCoaId(), entityId);
    }

    @Test
    void testCreateNewEntity() {
        // When: Create new entity
        CoaEntity newEntity = rollbackStrategy.createNewEntity();

        // Then: Verify new entity created
        assertNotNull(newEntity);
        assertNull(newEntity.getCoaId()); // Should be null for new entity
    }

    @Test
    @Transactional
    void testCopyAuditedFields_EntityCreation() {
        // Given: Create source and target entities
        Lvl1CategoryEntity category = createTestCategory("Assets");
        CoaEntity source = createTestCoaEntity("1.BS.CA.Cash", "Cash Account", category);
        source.setIsActive(false);
        source.setSign(false);

        CoaEntity target = rollbackStrategy.createNewEntity();

        // When: Copy audited fields for entity creation
        rollbackStrategy.copyAuditedFields(source, target, RevisionType.ADD, true);

        // Then: Verify all audited fields copied
        assertEquals(source.getCoaText(), target.getCoaText());
        assertEquals(source.getCoaDescription(), target.getCoaDescription());
        assertEquals(source.getIsActive(), target.getIsActive());
        assertEquals(source.getLvl1Category(), target.getLvl1Category());
        assertEquals(source.getSign(), target.getSign());
        
        // For entity creation, clientName should be copied
        assertEquals(source.getClientName(), target.getClientName());
    }

    @Test
    @Transactional
    void testCopyAuditedFields_EntityUpdate() {
        // Given: Create source and target entities
        Lvl1CategoryEntity category = createTestCategory("Assets");
        CoaEntity source = createTestCoaEntity("1.BS.CA.Cash.Updated", "Updated Cash Account", category);
        source.setIsActive(false);
        source.setSign(false);

        CoaEntity target = createTestCoaEntity("1.BS.CA.Cash", "Cash Account", category);
        String originalClientName = target.getClientName();

        // When: Copy audited fields for entity update
        rollbackStrategy.copyAuditedFields(source, target, RevisionType.MOD, false);

        // Then: Verify audited fields copied
        assertEquals(source.getCoaText(), target.getCoaText());
        assertEquals(source.getCoaDescription(), target.getCoaDescription());
        assertEquals(source.getIsActive(), target.getIsActive());
        assertEquals(source.getLvl1Category(), target.getLvl1Category());
        assertEquals(source.getSign(), target.getSign());
        
        // For entity update, clientName should NOT be copied (it's @NotAudited)
        assertEquals(originalClientName, target.getClientName());
    }

    @Test
    void testIsRollbackAllowed_DefaultBehavior() {
        // Given: Any entity ID and revision type
        Integer entityId = 1;

        // When/Then: Should allow all rollbacks by default
        assertTrue(rollbackStrategy.isRollbackAllowed(entityId, RevisionType.ADD));
        assertTrue(rollbackStrategy.isRollbackAllowed(entityId, RevisionType.MOD));
        assertTrue(rollbackStrategy.isRollbackAllowed(entityId, RevisionType.DEL));
    }

    @Test
    @Transactional
    void testCopyAuditedFields_NullValues() {
        // Given: Source entity with null values
        Lvl1CategoryEntity category = createTestCategory("Assets");
        CoaEntity source = CoaEntity.builder()
                .coaText("1.BS.CA.Cash")
                .coaDescription("Cash Account")
                .clientName(CLIENT_NAME)
                .isActive(null) // Null value
                .sign(null) // Null value
                .lvl1Category(category)
                .build();

        CoaEntity target = rollbackStrategy.createNewEntity();

        // When: Copy audited fields
        rollbackStrategy.copyAuditedFields(source, target, RevisionType.ADD, true);

        // Then: Verify null values copied correctly
        assertEquals(source.getCoaText(), target.getCoaText());
        assertEquals(source.getCoaDescription(), target.getCoaDescription());
        assertEquals(source.getIsActive(), target.getIsActive()); // Should be null
        assertEquals(source.getSign(), target.getSign()); // Should be null
        assertEquals(source.getLvl1Category(), target.getLvl1Category());
    }

    @Test
    @Transactional
    void testCopyAuditedFields_DifferentCategories() {
        // Given: Source and target with different categories
        Lvl1CategoryEntity category1 = createTestCategory("Assets");
        Lvl1CategoryEntity category2 = createTestCategory("Liabilities");
        
        CoaEntity source = createTestCoaEntity("1.BS.CA.Cash", "Cash Account", category1);
        CoaEntity target = createTestCoaEntity("2.BS.CL.Payable", "Accounts Payable", category2);

        // When: Copy audited fields
        rollbackStrategy.copyAuditedFields(source, target, RevisionType.MOD, false);

        // Then: Verify category updated
        assertEquals(source.getLvl1Category(), target.getLvl1Category());
        assertEquals(category1.getId(), target.getLvl1Category().getId());
    }

    // Helper methods
    @Transactional
    private Lvl1CategoryEntity createTestCategory(String categoryName) {
        Lvl1CategoryEntity category = Lvl1CategoryEntity.builder()
                .category(categoryName)
                .createdBy(TEST_USER)
                .createdTime(LocalDateTime.now())
                .lastModifiedBy(TEST_USER)
                .lastModifiedTime(LocalDateTime.now())
                .build();
        lvl1CategoryRepository.persist(category);
        return category;
    }

    @Transactional
    private CoaEntity createTestCoaEntity(String coaText, String description, Lvl1CategoryEntity category) {
        CoaEntity entity = CoaEntity.builder()
                .coaText(coaText)
                .coaDescription(description)
                .clientName(CLIENT_NAME)
                .isActive(true)
                .sign(true)
                .lvl1Category(category)
                .build();
        coaRepository.persist(entity);
        return entity;
    }
}
