package com.walnut.vegaspread.extraction.service;

import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import com.walnut.vegaspread.extraction.entity.TableTagEntity;
import com.walnut.vegaspread.extraction.model.MappedRowDto;
import com.walnut.vegaspread.extraction.repository.CoaMappingRepository;
import com.walnut.vegaspread.extraction.repository.TableTagRepository;
import io.quarkus.test.InjectMock;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.WebApplicationException;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CoaMappingServiceTest {

    @Inject
    Flyway flyway;
    @Inject
    CoaMappingService coaMappingService;
    @Inject
    CoaMappingRepository coaMappingRepository;
    @Inject
    TableTagRepository tableTagRepository;
    @Inject
    EntityManager entityManager;
    @InjectMock
    TableTagService tableTagService;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Transactional
    TableTagEntity createTableTagEntity(String tag) {
        TableTagEntity tableTag = TableTagEntity.builder()
                .tag(tag)
                .createdBy("user")
                .createdTime(LocalDateTime.now())
                .lastModifiedBy("user")
                .lastModifiedTime(LocalDateTime.now())
                .build();
        tableTagRepository.persist(tableTag);
        return tableTag;
    }

    @Transactional
    CoaMappingEntity createCoaMappingEntity(UUID docId, Integer tableId, Integer rowId, Integer coaId,
                                            Integer tableTagId,
                                            String fsHeader, String text) {

        CoaMappingEntity entity = CoaMappingEntity.builder()
                .coaId(coaId)
                .tableTypeId(tableTagId)
                .fsHeader(fsHeader)
                .text(text)
                .docId(docId)
                .tableId(tableId)
                .rowId(rowId.shortValue())
                .rowParent("")
                .fsText("")
                .isApproved(false)
                .build();
        coaMappingRepository.persist(entity);
        return entity;
    }

    @Test
    void testFindById_Success() {
        UUID docId = UUID.randomUUID();
        TableTagEntity tableTag = createTableTagEntity("Balance Sheet");
        CoaMappingEntity entity = createCoaMappingEntity(docId, 1, 1, 100, tableTag.getId(), "Assets",
                "Cash and Cash Equivalents");

        CoaMappingEntity result = coaMappingService.findById(entity.getId());

        assertNotNull(result);
        assertEquals(entity.getId(), result.getId());
        assertEquals(entity.getCoaId(), result.getCoaId());
        assertEquals(tableTag.getId(), result.getTableTypeId());
        assertEquals(entity.getText(), result.getText());
        assertEquals(entity.getFsHeader(), result.getFsHeader());
    }

    @Test
    void testFindByIdOptional_Found() {
        UUID docId = UUID.randomUUID();
        TableTagEntity tableTag = createTableTagEntity("Balance Sheet");
        CoaMappingEntity entity = createCoaMappingEntity(docId, 1, 1, 100, tableTag.getId(), "Assets",
                "Cash and Cash Equivalents");

        Optional<CoaMappingEntity> result = coaMappingService.findByIdOptional(entity.getId());

        assertTrue(result.isPresent());
        assertEquals(entity.getId(), result.get().getId());
        assertEquals(entity.getCoaId(), result.get().getCoaId());
    }

    @Test
    void testFindByIdOptional_NotFound() {
        Optional<CoaMappingEntity> result = coaMappingService.findByIdOptional(999);

        assertFalse(result.isPresent());
    }

    @Test
    void testGetCoaIdsForTableTypeOrFsHeader_Success() {
        UUID docId = UUID.randomUUID();
        TableTagEntity balanceSheetTag = createTableTagEntity("Balance Sheet");
        TableTagEntity balanceSheetTag2 = createTableTagEntity("New Balance Sheet 2");
        TableTagEntity incomeStatementTag = createTableTagEntity("Income Statement");

        createCoaMappingEntity(docId, 1, 1, 100, balanceSheetTag.getId(), "Assets", "Cash");
        createCoaMappingEntity(docId, 1, 2, 200, balanceSheetTag2.getId(), "Assets", "Inventory");
        createCoaMappingEntity(docId, 2, 1, 300, incomeStatementTag.getId(), "Assets", "Sales");
        createCoaMappingEntity(docId, 2, 2, 400, incomeStatementTag.getId(), "Revenue", "Cash");

        when(tableTagService.findByTableTagLike("Balance Sheet")).thenReturn(
                List.of(balanceSheetTag, balanceSheetTag2));
        List<Integer> result = coaMappingService.getCoaIdsForTableTypeOrFsHeader("Balance Sheet", "Assets");

        assertEquals(3, result.size());
        assertTrue(result.containsAll(List.of(100, 200, 300)));
    }

    @Test
    void testGetCoaIdsForTableTypeOrFsHeader_WithDuplicates() {
        UUID docId = UUID.randomUUID();
        TableTagEntity balanceSheetTag = createTableTagEntity("Balance Sheet");
        createCoaMappingEntity(docId, 1, 2, 100, balanceSheetTag.getId(), "Assets", "Cash");
        createCoaMappingEntity(docId, 2, 3, 100, balanceSheetTag.getId(), "Assets",
                "Cash Equivalents");
        createCoaMappingEntity(docId, 4, 5, 200, balanceSheetTag.getId(), "Assets", "Inventory");

        List<Integer> result = coaMappingService.getCoaIdsForTableTypeOrFsHeader("Balance Sheet", "Assets");

        assertEquals(2, result.size());
        assertTrue(result.contains(100));
        assertTrue(result.contains(200));
    }

    @Test
    void testGetCoaIdsForTableTypeAndFsHeader_NoMatches() {
        List<Integer> result = coaMappingService.getCoaIdsForTableTypeOrFsHeader("Non-existent", "Non-existent");
        assertTrue(result.isEmpty());
    }

    @Test
    void testToDtoList_Success() {
        TableTagEntity tableTag1 = createTableTagEntity("Balance Sheet");
        TableTagEntity tableTag2 = createTableTagEntity("Income Statement");

        UUID docId1 = UUID.randomUUID();
        UUID docId2 = UUID.randomUUID();

        CoaMappingEntity coaMapping1 = createCoaMappingEntity(docId1, 1, 1, 101, tableTag1.getId(), "Header1", "Text1");
        CoaMappingEntity coaMapping2 = createCoaMappingEntity(docId2, 2, 2, 102, tableTag2.getId(), "Header2", "Text2");
        CoaMappingEntity coaMapping3 = createCoaMappingEntity(docId1, 3, 3, 103, tableTag1.getId(), "Header3", "Text3");

        List<CoaMappingEntity> coaMappingEntities = List.of(coaMapping1, coaMapping2, coaMapping3);

        when(tableTagService.getTableTagByIds(List.of(tableTag1.getId(), tableTag2.getId()))).thenReturn(
                List.of(tableTag1, tableTag2));

        List<MappedRowDto> result = coaMappingService.toDtoList(coaMappingEntities);

        assertNotNull(result);
        assertEquals(3, result.size());

        for (int i = 0; i < result.size(); i++) {
            assertEquals(coaMappingEntities.get(i).getId(), result.get(i).id());
            assertEquals(coaMappingEntities.get(i).getTableId(), result.get(i).blockId());
            assertEquals(coaMappingEntities.get(i).getRowId().intValue(), result.get(i).rowId());
            assertEquals(coaMappingEntities.get(i).getDocId(), result.get(i).docId());
            assertEquals(coaMappingEntities.get(i).getTableTypeId(), result.get(i).tableTypeId());
            assertEquals(tableTagRepository.findById(coaMappingEntities.get(i).getTableTypeId()).getTag(),
                    result.get(i).tableType());
            assertEquals(coaMappingEntities.get(i).getRowParent(), result.get(i).rowParent());
            assertEquals(coaMappingEntities.get(i).getText(), result.get(i).text());
            assertEquals(coaMappingEntities.get(i).getFsHeader(), result.get(i).fsHeader());
            assertEquals(coaMappingEntities.get(i).getFsText(), result.get(i).fsText());
            assertEquals(coaMappingEntities.get(i).getCoaId(), result.get(i).coaId());
        }
    }

    @Test
    void testToDtoList_EmptyList() {
        List<CoaMappingEntity> coaMappingEntities = Collections.emptyList();
        List<MappedRowDto> result = coaMappingService.toDtoList(coaMappingEntities);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testToDtoList_TableTagNotFound() {
        TableTagEntity tableTag1 = createTableTagEntity("Balance Sheet");
        UUID docId1 = UUID.randomUUID();
        CoaMappingEntity coaMapping1 = createCoaMappingEntity(docId1, 1, 1, 101, tableTag1.getId(), "Header1", "Text1");
        // Create a COA mapping with a non-existent tableTypeId
        CoaMappingEntity coaMappingWithMissingTag = createCoaMappingEntity(UUID.randomUUID(), 2, 2, 102, 999, "Header2",
                "Text2");

        List<CoaMappingEntity> coaMappingEntities = List.of(coaMapping1, coaMappingWithMissingTag);

        when(tableTagService.getTableTagByIds(anyList())).thenReturn(List.of(tableTag1)); // Only return tableTag1

        WebApplicationException exception = assertThrows(WebApplicationException.class, () -> {
            coaMappingService.toDtoList(coaMappingEntities);
        });

        assertEquals(404, exception.getResponse().getStatus());
        assertTrue(exception.getResponse().getEntity().toString().contains("Table tag not found for id 999"));
    }

    @Test
    void testFindByDocIdsAndTableTypeId_Success() {
        TableTagEntity tableTag = createTableTagEntity("Balance Sheet");
        UUID docId1 = UUID.randomUUID();
        UUID docId2 = UUID.randomUUID();

        CoaMappingEntity entity1 = createCoaMappingEntity(docId1, 1, 1, 100, tableTag.getId(), "Assets", "Cash");
        CoaMappingEntity entity2 = createCoaMappingEntity(docId1, 2, 2, 101, tableTag.getId(), "Liabilities", "Debt");
        CoaMappingEntity entity3 = createCoaMappingEntity(docId2, 3, 3, 102, tableTag.getId(), "Equity", "Capital");
        // This one should not be returned due to different tableTypeId
        createCoaMappingEntity(docId1, 4, 4, 103, createTableTagEntity("Income Statement").getId(), "Revenue", "Sales");

        List<UUID> docIds = List.of(docId1, docId2);
        List<CoaMappingEntity> result = coaMappingService.findByDocIdsAndTableTypeId(docIds, tableTag.getId());

        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.stream()
                .map(CoaMappingEntity::getId)
                .toList()
                .containsAll(List.of(entity1.getId(), entity2.getId(),
                        entity3.getId())));
    }

    @Test
    void testFindByDocIdsAndTableTypeId_NoMatches() {
        TableTagEntity tableTag = createTableTagEntity("Balance Sheet");
        UUID docId = UUID.randomUUID();
        createCoaMappingEntity(docId, 1, 1, 100, tableTag.getId(), "Assets", "Cash");

        List<UUID> nonExistentDocIds = List.of(UUID.randomUUID());
        List<CoaMappingEntity> result = coaMappingService.findByDocIdsAndTableTypeId(nonExistentDocIds,
                tableTag.getId());

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByDocIdsAndTableTypeId_EmptyDocIdsList() {
        TableTagEntity tableTag = createTableTagEntity("Balance Sheet");
        UUID docId = UUID.randomUUID();
        createCoaMappingEntity(docId, 1, 1, 100, tableTag.getId(), "Assets", "Cash");

        List<UUID> emptyDocIds = Collections.emptyList();
        List<CoaMappingEntity> result = coaMappingService.findByDocIdsAndTableTypeId(emptyDocIds, tableTag.getId());

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByDocIdsAndTableTypeId_NullTableTypeId() {
        UUID docId1 = UUID.randomUUID();
        UUID docId2 = UUID.randomUUID();
        TableTagEntity tableTag1 = createTableTagEntity("Balance Sheet");
        TableTagEntity tableTag2 = createTableTagEntity("Income Statement");

        CoaMappingEntity entity1 = createCoaMappingEntity(docId1, 1, 1, 100, tableTag1.getId(), "Assets", "Cash");
        CoaMappingEntity entity2 = createCoaMappingEntity(docId1, 2, 2, 101, tableTag2.getId(), "Revenue", "Sales");
        CoaMappingEntity entity3 = createCoaMappingEntity(docId2, 3, 3, 102, tableTag1.getId(), "Equity", "Capital");

        List<UUID> docIds = List.of(docId1, docId2);
        List<CoaMappingEntity> result = coaMappingService.findByDocIdsAndTableTypeId(docIds, null);

        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.stream()
                .map(CoaMappingEntity::getId)
                .toList()
                .containsAll(List.of(entity1.getId(), entity2.getId(),
                        entity3.getId())));
    }

    @Test
    void testFindByDocIdsAndTableTypeId_NullTableTypeIdNoMatches() {
        createCoaMappingEntity(UUID.randomUUID(), 1, 1, 100, createTableTagEntity("Balance Sheet").getId(), "Assets",
                "Cash");

        List<UUID> nonExistentDocIds = List.of(UUID.randomUUID(), UUID.randomUUID());
        List<CoaMappingEntity> result = coaMappingService.findByDocIdsAndTableTypeId(nonExistentDocIds, null);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}

