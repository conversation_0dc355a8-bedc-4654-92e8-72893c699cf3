package com.walnut.vegaspread.extraction.service;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.common.model.audit.extraction.LayoutBlockAuditDto;
import com.walnut.vegaspread.extraction.entity.Bbox;
import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.entity.TableTagEntity;
import com.walnut.vegaspread.extraction.model.BlockTypeEnum;
import com.walnut.vegaspread.extraction.model.DbBlock;
import com.walnut.vegaspread.extraction.model.DbRow;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import com.walnut.vegaspread.extraction.repository.LayoutBlockRepository;
import com.walnut.vegaspread.extraction.repository.TableRowRepository;
import com.walnut.vegaspread.extraction.repository.TableTagRepository;
import io.quarkus.test.InjectMock;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.mockito.InjectSpy;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import jakarta.validation.ConstraintViolationException;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Response;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static com.walnut.vegaspread.extraction.utils.Config.NA_TAG_EXPLAINABILITY_ID;
import static com.walnut.vegaspread.extraction.utils.Config.NA_TAG_ID;
import static io.smallrye.common.constraint.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class LayoutServiceTest {

    private static final int BALANCE_SHEET_TAG_ID = 2;
    @InjectSpy
    LayoutService layoutService;
    @InjectSpy
    LayoutBlockRepository layoutBlockRepository;
    @Inject
    TableTagRepository tableTagRepository;
    @Inject
    TableRowRepository tableRowRepository;
    @InjectMock
    ExchangeService exchangeService;
    @InjectMock
    TableService tableService;
    @InjectMock
    ExtractedRowCoaDataService extractedRowCoaDataService;
    @InjectMock
    SubtotalMappingService subtotalMappingService;
    @InjectMock
    TableTagService tableTagService;
    @InjectMock
    JsonWebToken accessToken;
    @Inject
    EntityManager entityManager;
    @Inject
    Flyway flyway;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @BeforeEach
    @Transactional
    void setUp() {

        setupDefaultTableTags();

        Mockito.reset(exchangeService, tableService, extractedRowCoaDataService, subtotalMappingService,
                tableTagService);

        when(tableTagService.getTableTagById(anyInt())).thenAnswer(invocation -> {
            Integer id = invocation.getArgument(0);
            return tableTagRepository.findById(id);
        });
        when(tableTagService.getTableTags()).thenReturn(
                tableTagRepository.listAll()
        );

        when(tableService.deleteRows(anyList(), anyBoolean())).thenReturn(0L);
        when(tableService.deleteHeaders(anyList())).thenReturn(0L);
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Transactional
    void setupDefaultTableTags() {
        if (tableTagRepository.findById(NA_TAG_ID) == null) {
            TableTagEntity naTag = TableTagEntity.builder()
                    .tag("NA")
                    .createdBy("test")
                    .createdTime(LocalDateTime.now())
                    .lastModifiedBy("test")
                    .lastModifiedTime(LocalDateTime.now())
                    .build();
            tableTagRepository.persist(naTag);
        }
        if (tableTagRepository.findById(BALANCE_SHEET_TAG_ID) == null) {
            TableTagEntity balanceSheetTag = TableTagEntity.builder()
                    .tag("Balance Sheet")
                    .createdBy("test")
                    .createdTime(LocalDateTime.now())
                    .lastModifiedBy("test")
                    .lastModifiedTime(LocalDateTime.now())
                    .build();
            tableTagRepository.persist(balanceSheetTag);
        }
    }

    @Transactional
    LayoutBlockEntity createLayoutBlockForUser(UUID docId, Integer pageNum, Bbox bbox) {
        LayoutBlockEntity block = LayoutBlockEntity.builder()
                .docId(docId)
                .blockType(BlockTypeEnum.TABLE)
                .pageNum(pageNum.shortValue())
                .bbox(bbox)
                .score((byte) 100)
                .tag(tableTagRepository.findById(NA_TAG_ID))
                .comment(StringUtils.EMPTY)
                .tagExplainabilityId(NA_TAG_EXPLAINABILITY_ID)
                .build();

        layoutBlockRepository.persist(block);
        return block;
    }

    @Transactional
    LayoutBlockEntity createLayoutBlock(UUID docId, Integer pageNum, Bbox bbox, byte score, Integer tagId,
                                        String comment, Integer tagExplainabilityId) {
        LayoutBlockEntity block = LayoutBlockEntity.builder()
                .docId(docId)
                .blockType(BlockTypeEnum.TABLE)
                .pageNum(pageNum.shortValue())
                .bbox(bbox)
                .score(score)
                .tag(tableTagRepository.findById(tagId))
                .comment(comment)
                .tagExplainabilityId(tagExplainabilityId)
                .build();

        layoutBlockRepository.persist(block);
        return block;
    }

    @Transactional
    List<TableRowEntity> getTableRows(Integer blockId, LayoutBlockEntity ntaTable, int count) {
        LayoutBlockEntity block = layoutBlockRepository.findById(blockId);
        return IntStream.range(0, count).mapToObj(rowNum -> {
            TableRowPkId pkId = new TableRowPkId(blockId, rowNum);
            return TableRowEntity.builder()
                    .layoutBlock(block)
                    .cellsText(List.of("Cell 1", "Cell 2"))
                    .bbox(new Bbox(0, 100, 0, 20))
                    .score((byte) 85)
                    .parentText("Parent Text")
                    .comment("Comment")
                    .headerIds(List.of(1, 2))
                    .tableRowPkId(pkId)
                    .ntaTable(ntaTable)
                    .pos(rowNum)
                    .build();
        }).toList();
    }

    private DbBlock.BlockDto createDefaultDbBlockDto(UUID docId, BlockTypeEnum blockType, Integer blockId,
                                                     Integer xMin, Integer xMax, Integer yMin, Integer yMax,
                                                     Integer pageNum, String comment) {
        return DbBlock.BlockDto.builder()
                .docId(docId)
                .blockId(blockId)
                .blockType(blockType)
                .pageNum(pageNum)
                .xMin(xMin)
                .xMax(xMax)
                .yMin(yMin)
                .yMax(yMax)
                .score(100)
                .tagId(NA_TAG_ID)
                .comment(comment)
                .tagExplainabilityId(NA_TAG_EXPLAINABILITY_ID)
                .build();
    }

    private DbBlock.BlockDto createDbBlockDto(UUID docId, BlockTypeEnum blockType, Integer blockId,
                                              Integer xMin, Integer xMax, Integer yMin, Integer yMax,
                                              Integer score, Integer tagId,
                                              Integer pageNum, String comment, Integer tagExplainabilityId) {
        return DbBlock.BlockDto.builder()
                .docId(docId)
                .blockId(blockId)
                .blockType(blockType)
                .pageNum(pageNum)
                .xMin(xMin)
                .xMax(xMax)
                .yMin(yMin)
                .yMax(yMax)
                .score(score)
                .tagId(tagId)
                .comment(comment)
                .tagExplainabilityId(tagExplainabilityId)
                .build();
    }

    @Test
    void testCreateBlocks_CreateSuccess() {
        UUID docId = UUID.randomUUID();
        DbBlock.CreateBlockDto blockDto1 = new DbBlock.CreateBlockDto(
                1,
                BlockTypeEnum.TABLE,
                0,
                100,
                0,
                50,
                100,
                NA_TAG_ID,
                "Test comment"
        );
        DbBlock.CreateBlockDto blockDto2 = new DbBlock.CreateBlockDto(
                2,
                BlockTypeEnum.TABLE,
                5,
                95,
                5,
                45,
                99,
                NA_TAG_ID,
                "Test comment 2"
        );

        List<DbBlock.CreateBlockDto> blockDtos = List.of(blockDto1, blockDto2);

        List<LayoutBlockEntity> createdBlocks = layoutService.createBlocks(blockDtos, docId);

        assertEquals(2, layoutBlockRepository.count());
        for (int i = 0; i < blockDtos.size(); i++) {
            LayoutBlockEntity block = createdBlocks.get(i);
            assertEquals(docId, block.getDocId());
            assertEquals(blockDtos.get(i).blockType(), block.getBlockType());
            assertEquals(blockDtos.get(i).pageNum().shortValue(), block.getPageNum());
            assertEquals(blockDtos.get(i).xMin().shortValue(), block.getBbox().getXMin());
            assertEquals(blockDtos.get(i).xMax().shortValue(), block.getBbox().getXMax());
            assertEquals(blockDtos.get(i).yMin().shortValue(), block.getBbox().getYMin());
            assertEquals(blockDtos.get(i).yMax().shortValue(), block.getBbox().getYMax());
            assertEquals(blockDtos.get(i).score().byteValue(), block.getScore());
            assertEquals(blockDtos.get(i).tagId(), block.getTag().getId());
            assertEquals(blockDtos.get(i).comment(), block.getComment());
            assertEquals(NA_TAG_EXPLAINABILITY_ID, block.getTagExplainabilityId());
        }
    }

    @Test
    void testCreateBlocks_CreateTagNotFound_ThrowsException() {
        UUID docId = UUID.randomUUID();
        DbBlock.CreateBlockDto blockDto1 = new DbBlock.CreateBlockDto(
                1,
                BlockTypeEnum.TABLE,
                0,
                100,
                0,
                50,
                100,
                999,
                "Invalid tag"
        );

        List<DbBlock.CreateBlockDto> blockDtos = List.of(blockDto1);

        WebApplicationException exception = assertThrows(WebApplicationException.class, () ->
                layoutService.createBlocks(blockDtos, docId));

        assertEquals(Response.Status.NOT_FOUND.getStatusCode(), exception.getResponse().getStatus());
        assertTrue(exception.getResponse().getEntity().toString().contains("Invalid table tag id 999"));
        assertEquals(0, layoutBlockRepository.count());
        // No save should occur if tag is not found
    }

    @Test
    void testUpdateBlocks_UpdatePageNumSuccessForUser() {
        UUID docId = UUID.randomUUID();

        LayoutBlockEntity existingBlock = createLayoutBlockForUser(docId, 1,
                new Bbox(0, 100, 0, 50));

        Integer updatedPageNum = 5;
        List<DbBlock.BlockDto> blockDtos = List.of(
                createDefaultDbBlockDto(existingBlock.getDocId(), BlockTypeEnum.TABLE, existingBlock.getBlockId(),
                        existingBlock.getBbox().getXMin().intValue(), existingBlock.getBbox().getXMax().intValue(),
                        existingBlock.getBbox().getYMin().intValue(), existingBlock.getBbox().getYMax().intValue(),
                        updatedPageNum,
                        existingBlock.getComment())
        );

        layoutService.updateBlocks(blockDtos, false);

        LayoutBlockEntity updatedBlock = layoutBlockRepository.findById(existingBlock.getBlockId());
        assertEquals(existingBlock.getBlockId(), updatedBlock.getBlockId());
        assertEquals(existingBlock.getDocId(), updatedBlock.getDocId());
        assertEquals(existingBlock.getBlockType(), updatedBlock.getBlockType());
        assertEquals(existingBlock.getBbox(), updatedBlock.getBbox());
        assertEquals(existingBlock.getScore(), updatedBlock.getScore());
        assertEquals(existingBlock.getTag().getId(), updatedBlock.getTag().getId());
        assertEquals(existingBlock.getComment(), updatedBlock.getComment());
        assertEquals(existingBlock.getTagExplainabilityId(), updatedBlock.getTagExplainabilityId());
        assertEquals(updatedPageNum.shortValue(), updatedBlock.getPageNum());
        verify(exchangeService, times(0)).auditUpdateBlocks(anyList(), eq(false));
    }

    @Test
    void testUpdateBlocks_UpdateBlockTypeSuccess() {
        UUID docId = UUID.randomUUID();
        LayoutBlockEntity existingBlock = createLayoutBlockForUser(docId, 1,
                new Bbox(0, 100, 0, 50));

        BlockTypeEnum updatedBlockType = BlockTypeEnum.TEXT;
        List<DbBlock.BlockDto> blockDtos = List.of(
                createDefaultDbBlockDto(existingBlock.getDocId(), updatedBlockType, existingBlock.getBlockId(),
                        existingBlock.getBbox().getXMin().intValue(), existingBlock.getBbox().getXMax().intValue(),
                        existingBlock.getBbox().getYMin().intValue(), existingBlock.getBbox().getYMax().intValue(),
                        existingBlock.getPageNum().intValue(),
                        existingBlock.getComment())
        );

        layoutService.updateBlocks(blockDtos, false);

        LayoutBlockEntity updatedBlock = layoutBlockRepository.findById(existingBlock.getBlockId());

        assertEquals(existingBlock.getBlockId(), updatedBlock.getBlockId());
        assertEquals(existingBlock.getDocId(), updatedBlock.getDocId());
        assertEquals(updatedBlockType, updatedBlock.getBlockType());
        assertEquals(existingBlock.getBbox(), updatedBlock.getBbox());
        assertEquals(existingBlock.getScore(), updatedBlock.getScore());
        assertEquals(existingBlock.getTag().getId(), updatedBlock.getTag().getId());
        assertEquals(existingBlock.getComment(), updatedBlock.getComment());
        assertEquals(existingBlock.getTagExplainabilityId(), updatedBlock.getTagExplainabilityId());
        assertEquals(existingBlock.getPageNum(), updatedBlock.getPageNum());
        verify(exchangeService, times(0)).auditUpdateBlocks(anyList(), eq(false));
    }

    @Test
    void testCreateOrUpdateBlocks_UpdateBboxSuccess() {
        UUID docId = UUID.randomUUID();
        LayoutBlockEntity existingBlock = createLayoutBlockForUser(docId, 1,
                new Bbox(0, 100, 0, 50));

        Bbox updatedBbox = new Bbox(10, 90, 10, 40);
        List<DbBlock.BlockDto> blockDtos = List.of(
                createDefaultDbBlockDto(existingBlock.getDocId(), BlockTypeEnum.TABLE, existingBlock.getBlockId(),
                        updatedBbox.getXMin().intValue(),
                        updatedBbox.getXMax().intValue(), updatedBbox.getYMin().intValue(),
                        updatedBbox.getYMax().intValue(), existingBlock.getPageNum().intValue(),
                        existingBlock.getComment())
        );

        layoutService.updateBlocks(blockDtos, false);

        LayoutBlockEntity updatedBlock = layoutBlockRepository.findById(existingBlock.getBlockId());

        assertEquals(existingBlock.getBlockId(), updatedBlock.getBlockId());
        assertEquals(existingBlock.getDocId(), updatedBlock.getDocId());
        assertEquals(existingBlock.getBlockType(), updatedBlock.getBlockType());
        assertEquals(updatedBbox, updatedBlock.getBbox());
        assertEquals(existingBlock.getScore(), updatedBlock.getScore());
        assertEquals(existingBlock.getTag().getId(), updatedBlock.getTag().getId());
        assertEquals(existingBlock.getComment(), updatedBlock.getComment());
        assertEquals(existingBlock.getTagExplainabilityId(), updatedBlock.getTagExplainabilityId());
        assertEquals(existingBlock.getPageNum(), updatedBlock.getPageNum());
        verify(exchangeService, times(1)).auditUpdateBlocks(
                argThat(auditDtos -> {
                    if (auditDtos.size() != 4) return false;

                    Integer blockId = existingBlock.getBlockId();
                    Bbox oldBbox = existingBlock.getBbox();

                    List<LayoutBlockAuditDto.Update> expected = List.of(
                            new LayoutBlockAuditDto.Update(blockId, Bbox.X_MIN_COL_NAME, oldBbox.getXMin().toString(),
                                    updatedBbox.getXMin().toString()),
                            new LayoutBlockAuditDto.Update(blockId, Bbox.X_MAX_COL_NAME, oldBbox.getXMax().toString(),
                                    updatedBbox.getXMax().toString()),
                            new LayoutBlockAuditDto.Update(blockId, Bbox.Y_MIN_COL_NAME, oldBbox.getYMin().toString(),
                                    updatedBbox.getYMin().toString()),
                            new LayoutBlockAuditDto.Update(blockId, Bbox.Y_MAX_COL_NAME, oldBbox.getYMax().toString(),
                                    updatedBbox.getYMax().toString())
                    );

                    return expected.stream().allMatch(exp ->
                            auditDtos.stream().anyMatch(actual ->
                                    actual.blockId().equals(exp.blockId()) &&
                                            actual.colName().equals(exp.colName()) &&
                                            Objects.equals(actual.prevValue(), exp.prevValue()) &&
                                            Objects.equals(actual.newValue(), exp.newValue())
                            )
                    );
                }), eq(false));
    }

    @Test
    void testUpdateBlocks_UpdateBboxWithNullCoordinates() {
        UUID docId = UUID.randomUUID();
        LayoutBlockEntity existingBlock = createLayoutBlockForUser(docId, 1,
                new Bbox(0, 100, 0, 50));

        List<DbBlock.BlockDto> blockDtos = List.of(
                createDefaultDbBlockDto(existingBlock.getDocId(), BlockTypeEnum.TABLE, existingBlock.getBlockId(),
                        null, null, null, null, existingBlock.getPageNum().intValue(),
                        existingBlock.getComment())
        );

        layoutService.updateBlocks(blockDtos, false);

        LayoutBlockEntity updatedBlock = layoutBlockRepository.findById(existingBlock.getBlockId());

        assertEquals(existingBlock.getBlockId(), updatedBlock.getBlockId());
        assertEquals(existingBlock.getDocId(), updatedBlock.getDocId());
        assertEquals(existingBlock.getBlockType(), updatedBlock.getBlockType());
        assertEquals(existingBlock.getBbox(), updatedBlock.getBbox());
        assertEquals(existingBlock.getScore(), updatedBlock.getScore());
        assertEquals(existingBlock.getTag().getId(), updatedBlock.getTag().getId());
        assertEquals(existingBlock.getComment(), updatedBlock.getComment());
        assertEquals(existingBlock.getTagExplainabilityId(), updatedBlock.getTagExplainabilityId());
        assertEquals(existingBlock.getPageNum(), updatedBlock.getPageNum());
        verify(exchangeService, times(0)).auditUpdateBlocks(anyList(), eq(false));
    }

    @Test
    void testUpdateBlocks_UpdateTagSuccessForUser() {
        UUID docId = UUID.randomUUID();
        LayoutBlockEntity existingBlock = createLayoutBlockForUser(docId, 1,
                new Bbox(0, 100, 0, 50));

        TableTagEntity balanceSheetTag = tableTagRepository.findById(BALANCE_SHEET_TAG_ID);

        List<DbBlock.BlockDto> blockDtos = List.of(
                createDbBlockDto(existingBlock.getDocId(), existingBlock.getBlockType(), existingBlock.getBlockId(),
                        existingBlock.getBbox().getXMin().intValue(), existingBlock.getBbox().getXMax().intValue(),
                        existingBlock.getBbox().getYMin().intValue(), existingBlock.getBbox().getYMax().intValue(),
                        existingBlock.getScore().intValue(), balanceSheetTag.getId(),
                        existingBlock.getPageNum().intValue(),
                        existingBlock.getComment(),
                        existingBlock.getTagExplainabilityId())
        );

        layoutService.updateBlocks(blockDtos, false);

        LayoutBlockEntity updatedBlock = layoutBlockRepository.findById(existingBlock.getBlockId());
        assertEquals(existingBlock.getBlockId(), updatedBlock.getBlockId());
        assertEquals(existingBlock.getDocId(), updatedBlock.getDocId());
        assertEquals(existingBlock.getBlockType(), updatedBlock.getBlockType());
        assertEquals(existingBlock.getBbox(), updatedBlock.getBbox());
        assertEquals(existingBlock.getScore(), updatedBlock.getScore());
        assertEquals(existingBlock.getComment(), updatedBlock.getComment());
        assertEquals(existingBlock.getTagExplainabilityId(), updatedBlock.getTagExplainabilityId());
        assertEquals(balanceSheetTag.getId(), updatedBlock.getTag().getId());
        assertEquals(existingBlock.getPageNum(), updatedBlock.getPageNum());

        verify(exchangeService, times(1)).auditUpdateBlocks(argThat(auditDtos ->
                auditDtos.size() == 1 &&
                        auditDtos.stream().anyMatch(dto ->
                                dto.blockId().equals(existingBlock.getBlockId()) &&
                                        dto.colName().equals(LayoutBlockEntity.TAG_COL_NAME) &&
                                        dto.prevValue().equals(existingBlock.getTag().getId().toString()) &&
                                        dto.newValue().equals(updatedBlock.getTag().getId().toString())
                        )
        ), eq(false));
        verify(tableService, times(0)).createOrUpdateRows(anyList(), eq(true), eq(false), eq(false));
        verify(extractedRowCoaDataService, times(0)).deleteTableCoaDataMappings(anyList(), eq(false));
    }

    @Test
    void testUpdateBlocks_UpdateTagNotFound_ThrowsException() {
        UUID docId = UUID.randomUUID();
        LayoutBlockEntity existingBlock = createLayoutBlockForUser(docId, 1,
                new Bbox(0, 100, 0, 50));

        int invalidTagId = 999;

        List<DbBlock.BlockDto> blockDtos = List.of(
                createDbBlockDto(existingBlock.getDocId(), existingBlock.getBlockType(), existingBlock.getBlockId(),
                        existingBlock.getBbox().getXMin().intValue(), existingBlock.getBbox().getXMax().intValue(),
                        existingBlock.getBbox().getYMin().intValue(), existingBlock.getBbox().getYMax().intValue(),
                        existingBlock.getScore().intValue(), invalidTagId,
                        existingBlock.getPageNum().intValue(),
                        existingBlock.getComment(),
                        existingBlock.getTagExplainabilityId())
        );

        WebApplicationException exception = assertThrows(WebApplicationException.class, () ->
                layoutService.updateBlocks(blockDtos, false));

        assertEquals(Response.Status.NOT_FOUND.getStatusCode(), exception.getResponse().getStatus());
        assertTrue(exception.getResponse().getEntity().toString().contains("Invalid or non-existent tag id 999"));
        LayoutBlockEntity unchangedBlock = layoutBlockRepository.findById(existingBlock.getBlockId());

        assertEquals(existingBlock.getBlockId(), unchangedBlock.getBlockId());
        assertEquals(existingBlock.getDocId(), unchangedBlock.getDocId());
        assertEquals(existingBlock.getBlockType(), unchangedBlock.getBlockType());
        assertEquals(existingBlock.getBbox(), unchangedBlock.getBbox());
        assertEquals(existingBlock.getScore(), unchangedBlock.getScore());
        assertEquals(existingBlock.getComment(), unchangedBlock.getComment());
        assertEquals(existingBlock.getTagExplainabilityId(), unchangedBlock.getTagExplainabilityId());
        assertEquals(existingBlock.getPageNum(), unchangedBlock.getPageNum());
        assertEquals(existingBlock.getTag().getId(), unchangedBlock.getTag().getId());

        verify(exchangeService, times(0)).auditUpdateBlocks(anyList(), eq(false));
        verify(tableService, times(0)).createOrUpdateRows(anyList(), eq(true), eq(false), eq(false));
        verify(extractedRowCoaDataService, times(0)).deleteTableCoaDataMappings(anyList(), eq(false));
    }

    @Test
    void testCreateOrUpdateBlocks_UpdateToNATag_RemovesNTAMappingAndCoaData() {
        UUID docId = UUID.randomUUID();
        LayoutBlockEntity existingBlock = createLayoutBlock(docId, 1,
                new Bbox(0, 100, 0, 50), (byte) 90, BALANCE_SHEET_TAG_ID, "Balance Sheet", NA_TAG_EXPLAINABILITY_ID);
        LayoutBlockEntity ntaBlock1 = createLayoutBlockForUser(docId, 1,
                new Bbox(0, 10, 30, 60));
        LayoutBlockEntity ntaBlock2 = createLayoutBlockForUser(docId, 1,
                new Bbox(0, 10, 30, 60));

        List<TableRowEntity> ntaLikedRow1 = getTableRows(existingBlock.getBlockId(), ntaBlock1, 1);
        List<TableRowEntity> ntaLikedRow2 = getTableRows(existingBlock.getBlockId(), ntaBlock2, 1);
        List<TableRowEntity> ntaLikedRows = Stream.of(ntaLikedRow1, ntaLikedRow2)
                .flatMap(Collection::stream)
                .toList();

        when(tableService.findByNtaTableId(existingBlock.getBlockId())).thenReturn(ntaLikedRows);

        List<DbRow.RowDto> updatedNtaRows = ntaLikedRows.stream().map(row -> DbRow.RowDto.builder()
                        .tableId(row.getTableRowPkId().getTableId())
                        .rowId(row.getTableRowPkId().getRowId())
                        .ntaTableId(0)
                        .build())
                .toList();

        //Return empty list since the return value is not used and the service is mocked.
        when(tableService.createOrUpdateRows(updatedNtaRows, true, false, false)).thenReturn(Collections.emptyList());

        List<DbBlock.BlockDto> blockDtos = List.of(
                createDbBlockDto(existingBlock.getDocId(), existingBlock.getBlockType(), existingBlock.getBlockId(),
                        existingBlock.getBbox().getXMin().intValue(), existingBlock.getBbox().getXMax().intValue(),
                        existingBlock.getBbox().getYMin().intValue(), existingBlock.getBbox().getYMax().intValue(),
                        existingBlock.getScore().intValue(), NA_TAG_ID,
                        existingBlock.getPageNum().intValue(),
                        existingBlock.getComment(),
                        existingBlock.getTagExplainabilityId())
        );

        layoutService.updateBlocks(blockDtos, false);

        verify(tableService, times(1)).findByNtaTableId(existingBlock.getBlockId());
        verify(tableService, times(1)).createOrUpdateRows(argThat(rows ->
                        rows.size() == ntaLikedRows.size() && (rows.stream().allMatch(row -> row.getNtaTableId() == 0))),
                eq(true), eq(false), eq(false));
        // Fix: Changed anyList() to anySetOf(Integer.class)
        verify(extractedRowCoaDataService, times(1)).deleteTableCoaDataMappings(
                argThat(blockIdsToDelete -> blockIdsToDelete.size() == 1 && blockIdsToDelete.contains(
                        existingBlock.getBlockId())),
                eq(false));

        LayoutBlockEntity updatedBlock = layoutBlockRepository.findById(existingBlock.getBlockId());

        assertEquals(existingBlock.getBlockId(), updatedBlock.getBlockId());
        assertEquals(existingBlock.getDocId(), updatedBlock.getDocId());
        assertEquals(existingBlock.getBlockType(), updatedBlock.getBlockType());
        assertEquals(existingBlock.getBbox(), updatedBlock.getBbox());
        assertEquals(existingBlock.getScore(), updatedBlock.getScore());
        assertEquals(existingBlock.getComment(), updatedBlock.getComment());
        assertEquals(existingBlock.getPageNum(), updatedBlock.getPageNum());
        assertEquals(existingBlock.getTagExplainabilityId(), updatedBlock.getTagExplainabilityId());
        assertEquals(NA_TAG_ID, updatedBlock.getTag().getId());
    }

    @Test
    void testCreateOrUpdateBlocks_UpdateCommentSuccess() {
        UUID docId = UUID.randomUUID();
        LayoutBlockEntity existingBlock = createLayoutBlockForUser(docId, 1,
                new Bbox(0, 100, 0, 50));

        String newComment = "New comment";
        List<DbBlock.BlockDto> blockDtos = List.of(
                createDefaultDbBlockDto(existingBlock.getDocId(), BlockTypeEnum.TABLE, existingBlock.getBlockId(),
                        existingBlock.getBbox().getXMin().intValue(), existingBlock.getBbox().getXMax().intValue(),
                        existingBlock.getBbox().getYMin().intValue(), existingBlock.getBbox().getYMax().intValue(),
                        existingBlock.getPageNum().intValue(),
                        newComment)
        );

        layoutService.updateBlocks(blockDtos, false);

        LayoutBlockEntity updatedBlock = layoutBlockRepository.findById(existingBlock.getBlockId());
        assertEquals(existingBlock.getBlockId(), updatedBlock.getBlockId());
        assertEquals(existingBlock.getDocId(), updatedBlock.getDocId());
        assertEquals(existingBlock.getBlockType(), updatedBlock.getBlockType());
        assertEquals(existingBlock.getBbox(), updatedBlock.getBbox());
        assertEquals(existingBlock.getScore(), updatedBlock.getScore());
        assertEquals(existingBlock.getTag().getId(), updatedBlock.getTag().getId());
        assertEquals(newComment, updatedBlock.getComment());
        assertEquals(existingBlock.getTagExplainabilityId(), updatedBlock.getTagExplainabilityId());
        assertEquals(newComment, updatedBlock.getComment());

        verify(exchangeService, times(1)).auditUpdateBlocks(argThat(auditDtos ->
                auditDtos.size() == 1 &&
                        auditDtos.stream().anyMatch(dto ->
                                dto.blockId().equals(existingBlock.getBlockId()) &&
                                        dto.colName().equals(LayoutBlockEntity.COMMENT_COL_NAME) &&
                                        dto.prevValue().equals(existingBlock.getComment()) &&
                                        dto.newValue().equals(newComment)
                        )
        ), eq(false));
    }

    @Test
    void testCreateOrUpdateBlocks_UpdateTagExplainabilityId_ApiKeyAuthenticated() {
        UUID docId = UUID.randomUUID();
        LayoutBlockEntity existingBlock = createLayoutBlockForUser(docId, 1,
                new Bbox(0, 100, 0, 50));

        int newTagExplainabilityId = 123;
        List<DbBlock.BlockDto> blockDtos = List.of(
                createDbBlockDto(existingBlock.getDocId(), existingBlock.getBlockType(), existingBlock.getBlockId(),
                        existingBlock.getBbox().getXMin().intValue(), existingBlock.getBbox().getXMax().intValue(),
                        existingBlock.getBbox().getYMin().intValue(), existingBlock.getBbox().getYMax().intValue(),
                        existingBlock.getScore().intValue(),
                        existingBlock.getTag().getId(),
                        existingBlock.getPageNum().intValue(),
                        existingBlock.getComment(),
                        newTagExplainabilityId)
        );

        layoutService.updateBlocks(blockDtos, true);

        LayoutBlockEntity updatedBlock = layoutBlockRepository.findById(existingBlock.getBlockId());

        assertEquals(existingBlock.getBlockId(), updatedBlock.getBlockId());
        assertEquals(existingBlock.getDocId(), updatedBlock.getDocId());
        assertEquals(existingBlock.getBlockType(), updatedBlock.getBlockType());
        assertEquals(existingBlock.getBbox(), updatedBlock.getBbox());
        assertEquals(existingBlock.getScore(), updatedBlock.getScore());
        assertEquals(existingBlock.getTag().getId(), updatedBlock.getTag().getId());
        assertEquals(existingBlock.getComment(), updatedBlock.getComment());
        assertEquals(newTagExplainabilityId, updatedBlock.getTagExplainabilityId());
        assertEquals(existingBlock.getPageNum(), updatedBlock.getPageNum());

        verify(exchangeService, times(1)).auditUpdateBlocks(argThat(auditDtos ->
                auditDtos.size() == 1 &&
                        auditDtos.stream().anyMatch(d ->
                                d.blockId().equals(existingBlock.getBlockId()) &&
                                        d.colName().equals(LayoutBlockEntity.TAG_EXPLAINABILITY_ID_COL_NAME) &&
                                        d.prevValue().equals(existingBlock.getTagExplainabilityId() == null ? null :
                                                existingBlock.getTagExplainabilityId()
                                                        .toString()) &&
                                        d.newValue().equals(String.valueOf(newTagExplainabilityId))
                        )
        ), eq(true));
    }

    @Test
    void testCreateOrUpdateBlocks_UpdateTagExplainabilityId_UserAuthenticated() {
        UUID docId = UUID.randomUUID();
        LayoutBlockEntity existingBlock = createLayoutBlockForUser(docId, 1,
                new Bbox(0, 100, 0, 50));

        int newTagExplainabilityId = 123;
        List<DbBlock.BlockDto> blockDtos = List.of(
                createDbBlockDto(existingBlock.getDocId(), existingBlock.getBlockType(), existingBlock.getBlockId(),
                        existingBlock.getBbox().getXMin().intValue(), existingBlock.getBbox().getXMax().intValue(),
                        existingBlock.getBbox().getYMin().intValue(), existingBlock.getBbox().getYMax().intValue(),
                        existingBlock.getScore().intValue(),
                        existingBlock.getTag().getId(),
                        existingBlock.getPageNum().intValue(),
                        existingBlock.getComment(),
                        newTagExplainabilityId)
        );

        layoutService.updateBlocks(blockDtos, false);

        LayoutBlockEntity updatedBlock = layoutBlockRepository.findById(existingBlock.getBlockId());

        assertEquals(existingBlock.getBlockId(), updatedBlock.getBlockId());
        assertEquals(existingBlock.getDocId(), updatedBlock.getDocId());
        assertEquals(existingBlock.getBlockType(), updatedBlock.getBlockType());
        assertEquals(existingBlock.getBbox(), updatedBlock.getBbox());
        assertEquals(existingBlock.getScore(), updatedBlock.getScore());
        assertEquals(existingBlock.getTag().getId(), updatedBlock.getTag().getId());
        assertEquals(existingBlock.getComment(), updatedBlock.getComment());
        assertEquals(existingBlock.getTagExplainabilityId(), updatedBlock.getTagExplainabilityId());
        assertEquals(existingBlock.getPageNum(), updatedBlock.getPageNum());

        verify(exchangeService, times(0)).auditUpdateBlocks(anyList(), eq(false));
    }

    @Test
    void testDeleteBlockById_Success() {
        LayoutBlockEntity blockToDelete = createLayoutBlockForUser(UUID.randomUUID(), 1,
                new Bbox(0, 100, 0, 50));

        layoutService.deleteBlockById(blockToDelete.getBlockId());

        assertNull(layoutBlockRepository.findById(blockToDelete.getBlockId()));
    }

    @Test
    void testDeleteBlocksByIds_Success() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = new ArrayList<>();
        LayoutBlockEntity block1 = createLayoutBlockForUser(docId, 1,
                new Bbox(0, 5, 11, 50));
        blocks.add(block1);
        LayoutBlockEntity block2 = createLayoutBlockForUser(docId, 2,
                new Bbox(0, 100, 0, 50));
        blocks.add(block2);
        LayoutBlockEntity block3 = createLayoutBlockForUser(docId, 3,
                new Bbox(0, 100, 0, 50));
        blocks.add(block3);

        List<Integer> blockIds = blocks.stream().map(LayoutBlockEntity::getBlockId).toList();

        layoutService.deleteBlocksByIds(blockIds, false);

        assertEquals(0, layoutBlockRepository.count());
        verify(exchangeService, times(1)).auditFirstAndLastBlocksForDelete(blockIds, false);
        verify(tableService, times(1)).deleteRows(blockIds, false);
        verify(tableService, times(1)).deleteHeaders(blockIds);
    }

    @Test
    void testDeleteBlocksByIds_DeletionFailed_ThrowsException() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = new ArrayList<>();
        LayoutBlockEntity block1 = createLayoutBlockForUser(docId, 1,
                new Bbox(0, 5, 11, 50));
        blocks.add(block1);
        LayoutBlockEntity block2 = createLayoutBlockForUser(docId, 2,
                new Bbox(0, 100, 0, 50));
        blocks.add(block2);
        LayoutBlockEntity block3 = createLayoutBlockForUser(docId, 3,
                new Bbox(0, 100, 0, 50));
        blocks.add(block3);

        List<Integer> blockIds = new ArrayList<>(blocks.stream().map(LayoutBlockEntity::getBlockId).toList());

        blockIds.add(999); // Add a non-existent block id to simulate a deletion failure
        doNothing().when(exchangeService).auditFirstAndLastBlocksForDelete(anyList(), anyBoolean());
        WebApplicationException exception = assertThrows(WebApplicationException.class, () ->
                layoutService.deleteBlocksByIds(blockIds, false));

        assertEquals(Response.Status.CONFLICT.getStatusCode(), exception.getResponse().getStatus());
        ResponseException.ErrorResponse error = (ResponseException.ErrorResponse) exception.getResponse()
                .getEntity();
        assertEquals(error.message(), "All blocks with ids " + blockIds + " could not be deleted.");
       
        verify(exchangeService, times(1)).auditFirstAndLastBlocksForDelete(blockIds, false);
        verify(tableService, times(1)).deleteRows(blockIds, false);
        verify(tableService, times(1)).deleteHeaders(blockIds);
    }

    @Test
    void testDeleteBlocksByIds_ConstraintViolationException() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = new ArrayList<>();
        LayoutBlockEntity block1 = createLayoutBlockForUser(docId, 1,
                new Bbox(0, 5, 11, 50));
        blocks.add(block1);
        LayoutBlockEntity block2 = createLayoutBlockForUser(docId, 2,
                new Bbox(0, 100, 0, 50));
        blocks.add(block2);
        LayoutBlockEntity block3 = createLayoutBlockForUser(docId, 3,
                new Bbox(0, 100, 0, 50));
        blocks.add(block3);

        List<Integer> blockIds = blocks.stream().map(LayoutBlockEntity::getBlockId).toList();

        doThrow(new ConstraintViolationException("Mock Constraint Violation", null))
                .when(layoutBlockRepository).deleteAllByBlockIds(anyList());

        layoutService.deleteBlocksByIds(blockIds, false);

        assertEquals(3, layoutBlockRepository.count());
        verify(exchangeService, times(1)).auditFirstAndLastBlocksForDelete(anyList(), anyBoolean());
        verify(tableService, times(1)).deleteRows(anyList(), anyBoolean());
        verify(tableService, times(1)).deleteHeaders(anyList());
        reset(layoutBlockRepository);
    }

    @Test
    void testDeleteBlocksByDocId_Success() {

        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = new ArrayList<>();
        LayoutBlockEntity block1 = createLayoutBlockForUser(docId, 1,
                new Bbox(0, 5, 11, 50));
        blocks.add(block1);
        LayoutBlockEntity block2 = createLayoutBlockForUser(docId, 2,
                new Bbox(0, 100, 0, 50));
        blocks.add(block2);
        LayoutBlockEntity block3 = createLayoutBlockForUser(docId, 3,
                new Bbox(0, 100, 0, 50));
        blocks.add(block3);

        layoutService.deleteBlocksByDocId(docId, false);

        assertEquals(0, layoutBlockRepository.count());
        verify(exchangeService, times(1)).auditFirstAndLastBlocksForDelete(anyList(), eq(false));
        verify(tableService, times(1)).deleteRows(anyList(), eq(false));
        verify(tableService, times(1)).deleteHeaders(anyList());
    }

    @Test
    void testDeleteBlocksByDocId_ConstraintViolationException() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = new ArrayList<>();
        LayoutBlockEntity block1 = createLayoutBlockForUser(docId, 1,
                new Bbox(0, 5, 11, 50));
        blocks.add(block1);
        LayoutBlockEntity block2 = createLayoutBlockForUser(docId, 2,
                new Bbox(0, 100, 0, 50));
        blocks.add(block2);
        LayoutBlockEntity block3 = createLayoutBlockForUser(docId, 3,
                new Bbox(0, 100, 0, 50));
        blocks.add(block3);

        doThrow(new ConstraintViolationException("Mock Constraint Violation", null))
                .when(layoutBlockRepository).deleteAllByBlockIds(anyList());

        layoutService.deleteBlocksByDocId(docId, true);

        assertEquals(3, layoutBlockRepository.count());
        verify(exchangeService, times(1)).auditFirstAndLastBlocksForDelete(anyList(), eq(true));
        verify(tableService, times(1)).deleteRows(anyList(), eq(true));
        verify(tableService, times(1)).deleteHeaders(anyList());
    }

    @Test
    void testDuplicateBlocks_EmptySourceBlocks() {
        UUID srcDocId = UUID.randomUUID();
        UUID destDocId = UUID.randomUUID();
        assertEquals(0, layoutBlockRepository.count());
        layoutService.duplicateBlocks(srcDocId, destDocId);

        verify(layoutService, never()).duplicateBlock(any(LayoutBlockEntity.class), any(UUID.class));
        verify(extractedRowCoaDataService, never()).duplicateCoaDataMappings(anyMap());
        verify(subtotalMappingService, never()).duplicateSubtotalMappings(anyMap(), any(), any());
    }

    @Test
    void testGetBlock_Success() {
        UUID docId = UUID.randomUUID();
        LayoutBlockEntity block = createLayoutBlockForUser(docId, 1, new Bbox(0, 100, 0, 50));

        LayoutBlockEntity retrievedBlock = layoutService.getBlock(block.getBlockId());

        assertNotNull(retrievedBlock);
        assertEquals(block.getBlockId(), retrievedBlock.getBlockId());
        verify(layoutBlockRepository, times(1)).findById(block.getBlockId());
    }

    @Test
    void testGetBlock_NotFound() {
        int nonExistentBlockId = 999;

        LayoutBlockEntity retrievedBlock = layoutService.getBlock(nonExistentBlockId);

        assertNull(retrievedBlock);
        verify(layoutBlockRepository, times(1)).findById(nonExistentBlockId);
    }

    @Test
    void testGetBlocks_Success() {
        UUID docId = UUID.randomUUID();
        LayoutBlockEntity block1 = createLayoutBlockForUser(docId, 1, new Bbox(0, 100, 0, 50));
        LayoutBlockEntity block2 = createLayoutBlockForUser(docId, 2, new Bbox(10, 110, 10, 60));
        List<Integer> blockIds = List.of(block1.getBlockId(), block2.getBlockId());

        List<LayoutBlockEntity> retrievedBlocks = layoutService.getBlocks(blockIds);

        assertNotNull(retrievedBlocks);
        assertEquals(2, retrievedBlocks.size());
        assertTrue(retrievedBlocks.stream().map(LayoutBlockEntity::getBlockId).toList().containsAll(blockIds));
        verify(layoutBlockRepository, times(1)).findAllByIds(blockIds);
    }

    @Test
    void testGetBlocks_EmptyList() {
        List<Integer> emptyBlockIds = Collections.emptyList();

        List<LayoutBlockEntity> retrievedBlocks = layoutService.getBlocks(emptyBlockIds);

        assertNotNull(retrievedBlocks);
        assertTrue(retrievedBlocks.isEmpty());
        verify(layoutBlockRepository, times(1)).findAllByIds(emptyBlockIds);
    }

    @Test
    void testGetBlocks_SomeFoundSomeNotFound() {
        UUID docId = UUID.randomUUID();
        LayoutBlockEntity block1 = createLayoutBlockForUser(docId, 1, new Bbox(0, 100, 0, 50));
        int nonExistentBlockId = 999;
        List<Integer> blockIds = List.of(block1.getBlockId(), nonExistentBlockId);

        List<LayoutBlockEntity> retrievedBlocks = layoutService.getBlocks(blockIds);

        assertNotNull(retrievedBlocks);
        assertEquals(1, retrievedBlocks.size());
        assertTrue(
                retrievedBlocks.stream().map(LayoutBlockEntity::getBlockId).toList().contains(block1.getBlockId()));
        verify(layoutBlockRepository, times(1)).findAllByIds(blockIds);
    }

    @Test
    void testDuplicateBlock_Success() {
        UUID srcDocId = UUID.randomUUID();
        UUID destDocId = UUID.randomUUID();
        LayoutBlockEntity srcBlock = createLayoutBlockForUser(srcDocId, 1, new Bbox(0, 100, 0, 50));

        // Mock tableService calls for duplicating headers and rows
        when(tableService.duplicateHeadersForTable(any(LayoutBlockEntity.class), any(LayoutBlockEntity.class)))
                .thenReturn(Collections.emptyList());
        when(tableService.duplicateRowsForTable(any(LayoutBlockEntity.class), any(LayoutBlockEntity.class)))
                .thenReturn(Collections.emptyList());

        LayoutBlockEntity duplicatedBlock = layoutService.duplicateBlock(srcBlock, destDocId);

        assertNotNull(duplicatedBlock);
        assertEquals(destDocId, duplicatedBlock.getDocId());
        assertEquals(srcBlock.getPageNum(), duplicatedBlock.getPageNum());
        assertEquals(srcBlock.getBlockType(), duplicatedBlock.getBlockType());
        assertEquals(srcBlock.getBbox(), duplicatedBlock.getBbox());
        assertEquals(srcBlock.getScore(), duplicatedBlock.getScore());
        assertEquals(srcBlock.getTag(), duplicatedBlock.getTag());
        assertEquals(srcBlock.getComment(), duplicatedBlock.getComment());
        assertEquals(srcBlock.getTagExplainabilityId(), duplicatedBlock.getTagExplainabilityId());

        verify(tableService, times(1)).duplicateHeadersForTable(eq(srcBlock), any(LayoutBlockEntity.class));
        verify(tableService, times(1)).duplicateRowsForTable(eq(srcBlock), any(LayoutBlockEntity.class));
        verify(layoutBlockRepository, times(1)).persist(duplicatedBlock);
    }

    @Test
    void testGetTags_Success() {
        UUID docId = UUID.randomUUID();
        createLayoutBlock(docId, 1, new Bbox(0, 10, 0, 10), (byte) 90, 2, "TagA", 1);
        createLayoutBlock(docId, 2, new Bbox(10, 20, 10, 20), (byte) 90, 2, "TagB", 1);
        createLayoutBlockForUser(docId, 3, new Bbox(20, 30, 20, 30));

        List<DbBlock.BlockTagOnly> result = layoutService.getTags(docId, BlockTypeEnum.TABLE);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.stream().map(DbBlock.BlockTagOnly::getTag).toList().contains("Balance Sheet"));
        verify(layoutBlockRepository, times(1)).findBlocksWithTag(docId, BlockTypeEnum.TABLE);
    }

    @Test
    void testGetTags_NoTagsFound() {
        UUID docId = UUID.randomUUID();
        createLayoutBlock(docId, 2, new Bbox(10, 20, 10, 20), (byte) 90,
                NA_TAG_ID, "No tag", 1);

        List<DbBlock.BlockTagOnly> result = layoutService.getTags(docId, BlockTypeEnum.TABLE);

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(layoutBlockRepository, times(1)).findBlocksWithTag(docId, BlockTypeEnum.TABLE);
    }

    @Test
    void testFindAllBlocksInDocWithTag_Success() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = new ArrayList<>();
        String tag = "Balance Sheet";
        LayoutBlockEntity block1 = createLayoutBlock(docId, 1, new Bbox(0, 10, 0, 10), (byte) 90,
                BALANCE_SHEET_TAG_ID, tag,
                1);
        blocks.add(block1);
        LayoutBlockEntity block2 = createLayoutBlock(docId, 2, new Bbox(10, 20, 10, 20), (byte) 90,
                BALANCE_SHEET_TAG_ID, tag,
                1);
        blocks.add(block2);
        LayoutBlockEntity block3 = createLayoutBlock(docId, 3, new Bbox(20, 30, 20, 30), (byte) 90, NA_TAG_ID,
                "No tag", 1);
        blocks.add(block3);

        List<Integer> balanceSheetBlockIds = blocks.stream()
                .filter(block -> block.getTag().getTag().equals(tag))
                .map(LayoutBlockEntity::getBlockId)
                .toList();
        List<LayoutBlockEntity> result = layoutService.findAllBlocksInDocWithTag(docId, tag);
        List<Integer> resultBlockIds = result.stream().map(LayoutBlockEntity::getBlockId).toList();

        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(resultBlockIds.containsAll(balanceSheetBlockIds));
        verify(layoutBlockRepository, times(1)).findAllBlocksByDocIdAndTag(docId, tag);
    }

    @Test
    void testFindAllBlocksInDocWithTag_NoBlocksFound() {
        UUID docId = UUID.randomUUID();

        createLayoutBlock(docId, 3, new Bbox(20, 30, 10, 20), (byte) 90, NA_TAG_ID,
                "No tag", 1);

        List<LayoutBlockEntity> result = layoutService.findAllBlocksInDocWithTag(docId, "Balance Sheet");

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(layoutBlockRepository, times(1)).findAllBlocksByDocIdAndTag(docId, "Balance Sheet");
    }

    @Test
    void testGetBlockIdsForDoc_Success() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = new ArrayList<>();
        LayoutBlockEntity block1 = createLayoutBlockForUser(docId, 101, new Bbox(0, 10, 0, 10));
        blocks.add(block1);
        LayoutBlockEntity block2 = createLayoutBlockForUser(docId, 102, new Bbox(10, 20, 10, 20));
        blocks.add(block2);
        LayoutBlockEntity block3 = createLayoutBlockForUser(docId, 103, new Bbox(20, 30, 20, 30));
        blocks.add(block3);

        List<Integer> blockIds = blocks.stream().map(LayoutBlockEntity::getBlockId).toList();

        List<Integer> result = layoutService.getBlockIdsForDoc(docId);

        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.containsAll(blockIds));
        verify(layoutBlockRepository, times(1)).findAllBlockIdsByDocId(docId);
    }

    @Test
    void testGetBlockIdsForDoc_NoBlocksFound() {
        UUID docId = UUID.randomUUID();

        List<Integer> result = layoutService.getBlockIdsForDoc(docId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(layoutBlockRepository, times(1)).findAllBlockIdsByDocId(docId);
    }

    @Test
    void testGetTaggedTables_Success() {
        UUID docId = UUID.randomUUID();

        // Create a list of tagged tables
        List<LayoutBlockEntity> taggedTables = List.of(
                createLayoutBlock(docId, 1, new Bbox(0, 10, 0, 10), (byte) 90, BALANCE_SHEET_TAG_ID, "Comment1", 1),
                createLayoutBlock(docId, 2, new Bbox(10, 20, 10, 20), (byte) 90, BALANCE_SHEET_TAG_ID, "Comment2",
                        1)
                // Add more if needed without changing expectedIds logic
        );

        List<LayoutBlockEntity> result = layoutService.getTaggedTables(docId);

        assertNotNull(result);
        assertEquals(taggedTables.size(), result.size());

        // Extract expected and actual block IDs
        Set<Integer> expectedIds = taggedTables.stream()
                .map(LayoutBlockEntity::getBlockId)
                .collect(Collectors.toSet());

        Set<Integer> actualIds = result.stream()
                .map(LayoutBlockEntity::getBlockId)
                .collect(Collectors.toSet());

        assertTrue(actualIds.containsAll(expectedIds));

        verify(layoutBlockRepository, times(1)).findAllTaggedTables(docId);
    }

    @Test
    void testGetTaggedTables_NoTaggedTablesFound() {
        UUID docId = UUID.randomUUID();
        createLayoutBlockForUser(docId, 1, new Bbox(0, 10, 0, 10));

        List<LayoutBlockEntity> result = layoutService.getTaggedTables(docId);

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(layoutBlockRepository, times(1)).findAllTaggedTables(docId);
    }

    @Test
    void testListBlocksForDocIdAndPageNum_Success() {
        UUID docId1 = UUID.randomUUID();
        UUID docId2 = UUID.randomUUID();

        int pageNum = 1;
        List<LayoutBlockEntity> blocks = new ArrayList<>();
        LayoutBlockEntity block1 = createLayoutBlockForUser(docId1, pageNum, new Bbox(0, 10, 0, 10));
        blocks.add(block1);
        LayoutBlockEntity block2 = createLayoutBlockForUser(docId2, pageNum, new Bbox(10, 20, 10, 20));
        blocks.add(block2);
        LayoutBlockEntity block3 = createLayoutBlockForUser(docId1, pageNum + 1, new Bbox(20, 30, 20, 30));
        blocks.add(block3);

        List<LayoutBlockEntity> docBlocks = blocks.stream()
                .filter(block -> block.getDocId().equals(docId1) && block.getPageNum() == pageNum)
                .toList();

        List<LayoutBlockEntity> result = layoutService.listBlocksForDocIdAndPageNum(docId1, pageNum,
                BlockTypeEnum.TABLE);

        assertNotNull(result);
        assertEquals(docBlocks.size(), result.size());
        assertTrue(docBlocks.stream().map(LayoutBlockEntity::getBlockId).toList().containsAll(
                result.stream().map(LayoutBlockEntity::getBlockId).toList()));
        verify(layoutBlockRepository, times(1)).findAllByDocIdAndBlockTypeAndPageNum(docId1, BlockTypeEnum.TABLE,
                pageNum);
    }

    @Test
    void testListBlocksForDocIdAndPageNum_NoBlocksFound() {
        UUID docId = UUID.randomUUID();
        int pageNum = 1;

        List<LayoutBlockEntity> result = layoutService.listBlocksForDocIdAndPageNum(docId, pageNum,
                BlockTypeEnum.TABLE);

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(layoutBlockRepository, times(1)).findAllByDocIdAndBlockTypeAndPageNum(docId, BlockTypeEnum.TABLE,
                pageNum);
    }

    @Test
    void testListBlocksForDocId_Success() {
        UUID docId = UUID.randomUUID();
        LayoutBlockEntity block1 = createLayoutBlockForUser(docId, 1, new Bbox(0, 10, 0, 10));
        LayoutBlockEntity block2 = createLayoutBlockForUser(docId, 2, new Bbox(10, 20, 10, 20));

        List<LayoutBlockEntity> result = layoutService.listBlocksForDocId(docId, BlockTypeEnum.TABLE);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.stream()
                .map(LayoutBlockEntity::getBlockId)
                .toList()
                .containsAll(List.of(block1.getBlockId(), block2.getBlockId())));
        verify(layoutBlockRepository, times(1)).findAllByDocIdAndBlockType(docId, BlockTypeEnum.TABLE);
    }

    @Test
    void testListBlocksForDocId_NoBlocksFound() {
        UUID docId1 = UUID.randomUUID();
        UUID docId2 = UUID.randomUUID();
        createLayoutBlockForUser(docId1, 1, new Bbox(0, 10, 0, 10));

        List<LayoutBlockEntity> result = layoutService.listBlocksForDocId(docId2, BlockTypeEnum.TABLE);

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(layoutBlockRepository, times(1)).findAllByDocIdAndBlockType(docId2, BlockTypeEnum.TABLE);
    }
}
