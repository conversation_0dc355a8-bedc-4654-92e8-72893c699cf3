package com.walnut.vegaspread.extraction.service;

import com.walnut.vegaspread.extraction.entity.CoaDataEntity;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.model.CoaDataDto;
import com.walnut.vegaspread.extraction.primarykey.ExtractedTableRowCoaDataPkId;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import com.walnut.vegaspread.extraction.repository.CoaDataRepository;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.util.List;
import java.util.Optional;

import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CoaDataServiceTest {

    @Inject
    Flyway flyway;
    @Inject
    CoaDataService coaDataService;
    @Inject
    CoaDataRepository coaDataRepository;
    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Transactional
    CoaDataEntity createCoaDataEntity(Integer coaId, Boolean useCoa, Byte coaScore) {
        CoaDataEntity entity = CoaDataEntity.builder()
                .coaId(coaId)
                .useCoa(useCoa)
                .coaScore(coaScore)
                .build();
        coaDataRepository.persist(entity);
        return entity;
    }

    ExtractedTableRowCoaDataJoinEntity createJoinEntity(Integer tableId, Short rowId, Integer coaDataId) {
        ExtractedTableRowCoaDataJoinEntity entity = new ExtractedTableRowCoaDataJoinEntity();
        TableRowPkId tableRowPkId = new TableRowPkId(tableId, rowId);
        ExtractedTableRowCoaDataPkId pkId = new ExtractedTableRowCoaDataPkId(tableRowPkId, coaDataId);
        entity.setExtractedTableRowCoaDataPkId(pkId);
        return entity;
    }

    @Test
    void testAddCoaData_Success() {
        CoaDataDto.Create createDto = new CoaDataDto.Create(100, 85, true);

        CoaDataEntity result = coaDataService.addCoaData(createDto);

        assertNotNull(result);
        assertEquals(createDto.coaId(), result.getCoaId());
        assertEquals(createDto.useCoa(), result.getUseCoa());
        assertEquals(createDto.coaScore().byteValue(), result.getCoaScore());
    }

    @Test
    void testFindByUseCoaAndCoaIdAndCoaScore_Found() {
        CoaDataEntity entity = createCoaDataEntity(100, true, (byte) 85);

        Optional<CoaDataEntity> result = coaDataService.findByUseCoaAndCoaIdAndCoaScore(true, 100, 85);

        assertTrue(result.isPresent());
        assertEquals(entity.getId(), result.get().getId());
        assertEquals(entity.getCoaId(), result.get().getCoaId());
        assertEquals(entity.getUseCoa(), result.get().getUseCoa());
        assertEquals(entity.getCoaScore(), result.get().getCoaScore());
    }

    @Test
    void testFindByUseCoaAndCoaIdAndCoaScore_NotFound() {
        Optional<CoaDataEntity> result = coaDataService.findByUseCoaAndCoaIdAndCoaScore(true, 999, 85);

        assertFalse(result.isPresent());
    }

    @Test
    void testIsUpdatedCoaData_True() {
        CoaDataEntity existingEntity = createCoaDataEntity(100, true, (byte) 85);
        ExtractedTableRowCoaDataJoinEntity joinEntity = createJoinEntity(1, (short) 1, existingEntity.getId());

        CoaDataDto.Create newDto = new CoaDataDto.Create(200, 90, false);

        boolean result = coaDataService.isUpdatedCoaData(joinEntity, newDto);

        assertTrue(result);
    }

    @Test
    void testIsUpdatedCoaData_False() {
        CoaDataEntity existingEntity = createCoaDataEntity(100, true, (byte) 85);
        ExtractedTableRowCoaDataJoinEntity joinEntity = createJoinEntity(1, (short) 1, existingEntity.getId());

        CoaDataDto.Create dto = new CoaDataDto.Create(existingEntity.getCoaId(),
                existingEntity.getCoaScore().intValue(), existingEntity.getUseCoa());

        boolean result = coaDataService.isUpdatedCoaData(joinEntity, dto);

        assertFalse(result);
    }

    @Test
    void testFindById_Success() {
        CoaDataEntity entity = createCoaDataEntity(100, true, (byte) 85);

        CoaDataEntity result = coaDataService.findById(entity.getId());

        assertNotNull(result);
        assertEquals(entity.getId(), result.getId());
        assertEquals(entity.getCoaId(), result.getCoaId());
        assertEquals(entity.getUseCoa(), result.getUseCoa());
        assertEquals(entity.getCoaScore(), result.getCoaScore());
    }

    @Test
    void testGetCoaDataByIds_Success() {
        CoaDataEntity entity1 = createCoaDataEntity(100, true, (byte) 85);
        CoaDataEntity entity2 = createCoaDataEntity(200, false, (byte) 90);
        List<Integer> ids = List.of(entity1.getId(), entity2.getId());

        List<CoaDataEntity> results = coaDataService.getCoaDataByIds(ids);

        assertEquals(2, results.size());
        assertTrue(results.stream().anyMatch(e -> e.getId().equals(entity1.getId())));
        assertTrue(results.stream().anyMatch(e -> e.getId().equals(entity2.getId())));
    }

    @Test
    void testGetCoaDataByIds_EmptyList() {
        List<CoaDataEntity> results = coaDataService.getCoaDataByIds(List.of());

        assertTrue(results.isEmpty());
    }
}
