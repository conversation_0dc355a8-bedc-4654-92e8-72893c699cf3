package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import com.walnut.vegaspread.extraction.entity.TableTagEntity;
import com.walnut.vegaspread.extraction.model.TableTypeAndFsHeaderDto;
import com.walnut.vegaspread.extraction.repository.CoaMappingRepository;
import com.walnut.vegaspread.extraction.repository.TableTagRepository;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;

@QuarkusTest
@TestHTTPEndpoint(CoaMappingResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CoaMappingResourceTest {
    @Inject
    CoaMappingRepository coaMappingRepository;
    @Inject
    Flyway flyway;
    @Inject
    EntityManager entityManager;
    @Inject
    TableTagRepository tableTagRepository;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
    }

    @Transactional
    public List<CoaMappingEntity> createCoaMappings() {
        List<CoaMappingEntity> coaMappingEntities = new ArrayList<>();

        for (int i = 0; i < 21; i++) {
            CoaMappingEntity coaMappingEntity = new CoaMappingEntity();
            coaMappingEntity.setTableId(i);
            coaMappingEntity.setRowId((short) i);
            coaMappingEntity.setDocId(UUID.randomUUID());
            coaMappingEntity.setTableTypeId(i + 2);
            coaMappingEntity.setRowParent("");
            coaMappingEntity.setText("Text" + i);
            coaMappingEntity.setFsHeader("fsHeader" + i);
            coaMappingEntity.setFsText("fsText" + i);
            coaMappingEntity.setCoaId(i);
            coaMappingEntity.setIsApproved(true);
            coaMappingEntities.add(coaMappingEntity);
        }
        coaMappingRepository.persist(coaMappingEntities);
        return coaMappingEntities;
    }

    @Transactional
    public List<TableTagEntity> createTableTags() {
        List<TableTagEntity> tableTagEntities = new ArrayList<>();

        for (int i = 0; i < 21; i++) {
            TableTagEntity tableTagEntity = new TableTagEntity();
            tableTagEntity.setTag("Tag" + (i + 2));
            tableTagEntity.setCreatedBy("user");
            tableTagEntity.setCreatedTime(LocalDateTime.now());
            tableTagEntity.setLastModifiedBy("user");
            tableTagEntity.setLastModifiedTime(LocalDateTime.now());
            tableTagEntities.add(tableTagEntity);
        }
        tableTagRepository.persist(tableTagEntities);
        return tableTagEntities;
    }

    @Test
    @TestSecurity(user = "user")
    void testGetCoaIdsForTableTypeOrFsHeader() {
        List<TableTagEntity> tableTagEntities = createTableTags();
        List<CoaMappingEntity> coaMappingEntities = createCoaMappings();

        TableTagEntity tableTag = tableTagEntities.get(2);

        List<Integer> coaIds = given().contentType(ContentType.JSON)
                .body(new TableTypeAndFsHeaderDto(tableTag.tag, "header1"))
                .when()
                .post("/coa-for-tag")
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<>() {
                });

        List<Integer> matchingTableTagIds = tableTagEntities.stream()
                .filter(tag -> tag.getTag().contains(tableTag.tag))
                .map(TableTagEntity::getId)
                .toList();

        List<Integer> expectedCoaIds = coaMappingEntities.stream()
                .filter(coaMappingEntity -> matchingTableTagIds.contains(
                        coaMappingEntity.getTableTypeId()) || coaMappingEntity.getFsHeader().contains("header1"))
                .map(CoaMappingEntity::getCoaId)
                .distinct().toList();

        Assertions.assertEquals(expectedCoaIds.size(), coaIds.size());
        expectedCoaIds.forEach(coaId -> Assertions.assertTrue(coaIds.contains(coaId)));
    }
}
