package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import com.walnut.vegaspread.extraction.entity.Bbox;
import com.walnut.vegaspread.extraction.entity.CoaDataEntity;
import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.entity.TableHeaderEntity;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.entity.TableTagEntity;
import com.walnut.vegaspread.extraction.model.BlockTypeEnum;
import com.walnut.vegaspread.extraction.model.Validation;
import com.walnut.vegaspread.extraction.primarykey.TableHeaderPkId;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import com.walnut.vegaspread.extraction.repository.CoaDataRepository;
import com.walnut.vegaspread.extraction.repository.ExtractedRowCoaDataRepository;
import com.walnut.vegaspread.extraction.repository.LayoutBlockRepository;
import com.walnut.vegaspread.extraction.repository.TableHeaderRepository;
import com.walnut.vegaspread.extraction.repository.TableRowRepository;
import com.walnut.vegaspread.extraction.repository.TableTagRepository;
import com.walnut.vegaspread.extraction.service.ExchangeService;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.apache.commons.lang3.StringUtils;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.walnut.vegaspread.common.utils.Constants.BALANCE_SHEET_TAG;
import static com.walnut.vegaspread.common.utils.Constants.INCOME_STATEMENT_TAG;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static com.walnut.vegaspread.extraction.model.Validation.ValidationError.COA_MAPPED_ROW_NO_TABLE_TAG;
import static com.walnut.vegaspread.extraction.model.Validation.ValidationError.INVALID_FS_ROW_HEADER_FOR_LINKED_TABLE_TAG;
import static com.walnut.vegaspread.extraction.model.Validation.ValidationError.INVALID_TABLE_FOR_LINKED_TABLE_TAG;
import static com.walnut.vegaspread.extraction.model.Validation.ValidationError.LVL1_CATEGORY_HEAD_MISMATCH;
import static com.walnut.vegaspread.extraction.model.Validation.ValidationError.LVL1_CATEGORY_TABLE_CATEGORY_MISMATCH;
import static com.walnut.vegaspread.extraction.model.Validation.ValidationError.NTA_TABLE_NO_FS_LINK;
import static com.walnut.vegaspread.extraction.model.Validation.ValidationError.NTA_TABLE_NO_MAPPING;
import static com.walnut.vegaspread.extraction.service.ValidationService.BS_NTA_TABLE_TAG;
import static com.walnut.vegaspread.extraction.service.ValidationService.IS_NTA_TABLE_TAG;
import static com.walnut.vegaspread.extraction.service.ValidationService.SEPARATOR;
import static com.walnut.vegaspread.extraction.service.ValidationService.UNWANTED_FS_TABLES;
import static com.walnut.vegaspread.extraction.utils.Config.NA_TAG_ID;
import static io.restassured.RestAssured.given;

@QuarkusTest
@TestHTTPEndpoint(ValidationResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ValidationResourceTest {

    @Inject
    LayoutBlockRepository layoutBlockRepository;
    @Inject
    TableRowRepository tableRowRepository;
    @Inject
    TableHeaderRepository tableHeaderRepository;
    @Inject
    ExtractedRowCoaDataRepository extractedRowCoaDataRepository;
    @Inject
    CoaDataRepository coaDataRepository;
    @Inject
    Flyway flyway;
    @InjectMock
    ExchangeService exchangeService;
    @Inject
    TableTagRepository tableTagRepository;
    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
    }

    int getCoord() {
        return new Random().nextInt(1000);
    }

    @Transactional
    TableTagEntity getOrCreateTableTag(String tag) {
        TableTagEntity tableTag = tableTagRepository.findByTag(tag);
        if (tableTag == null) {
            tableTag = TableTagEntity.builder()
                    .tag(tag)
                    .createdBy("user")
                    .createdTime(LocalDateTime.now())
                    .lastModifiedBy("user")
                    .lastModifiedTime(LocalDateTime.now())
                    .build();
            tableTagRepository.persist(tableTag);
        }
        return tableTag;
    }

    @Transactional
    List<LayoutBlockEntity> createBlocks(UUID docId, int blockCount, int rowCount) {

        List<TableTagEntity> tableTags = IntStream.range(0, blockCount)
                .mapToObj(id -> getOrCreateTableTag("tag" + id + "_" + docId))
                .toList();
        tableTagRepository.persist(tableTags);
        List<LayoutBlockEntity> blocks = IntStream.range(0, blockCount).mapToObj(id -> LayoutBlockEntity.builder()
                .docId(docId)
                .pageNum((short) (id % 3))
                .tag(tableTags.get(id))
                .blockType(BlockTypeEnum.TABLE)
                .score((byte) new Random().nextInt(100))
                .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                .tagExplainabilityId(id)
                .build()).toList();
        layoutBlockRepository.persist(blocks);

        List<TableHeaderEntity> headers = new ArrayList<>();
        for (LayoutBlockEntity block : blocks) {
            List<TableHeaderEntity> blockHeaders = IntStream.range(0, 3)
                    .mapToObj(id -> TableHeaderEntity.builder()
                            .tableHeaderPkId(new TableHeaderPkId(block.getBlockId(), id))
                            .layoutBlock(block)
                            .text(id == 0 ? "description" : "header" + id)
                            .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                            .score((byte) new Random().nextInt(100))
                            .pos(id)
                            .build())
                    .toList();
            headers.addAll(blockHeaders);
            block.setTableHeaders(blockHeaders);
        }
        tableHeaderRepository.persist(headers);

        List<TableRowEntity> rows = new ArrayList<>();
        for (LayoutBlockEntity block : blocks) {
            List<TableRowEntity> blockRows = IntStream.range(0, rowCount)
                    .mapToObj(id -> TableRowEntity.builder()
                            .tableRowPkId(new TableRowPkId(block.getBlockId(), id))
                            .layoutBlock(block)
                            .cellsText(List.of("row" + id, "row" + id))
                            .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                            .score((byte) new Random().nextInt(100))
                            .comment("comment" + id)
                            .parentText("parent" + id)
                            .pos(id)
                            .build())
                    .toList();
            rows.addAll(blockRows);
            block.setTableRows(blockRows);
        }
        tableRowRepository.persist(rows);
        entityManager.flush();
        return blocks;
    }

    @Transactional
    public CoaDataEntity addCoaData(Integer coaId, Integer coaScore, Boolean useCoa) {
        CoaDataEntity coaData = CoaDataEntity.builder()
                .coaId(coaId)
                .coaScore(coaScore.byteValue())
                .useCoa(useCoa)
                .build();
        coaDataRepository.persist(coaData);
        coaDataRepository.flush();
        return coaData;
    }

    @Transactional
    public void addCoaDataMapping(TableRowEntity row, Integer coaDataId, CoaMappingEntity explainability) {

        extractedRowCoaDataRepository.saveRowCoaJoin(
                List.of(new ExtractedTableRowCoaDataJoinEntity(row.getTableRowPkId(), coaDataId,
                        explainability)));
        extractedRowCoaDataRepository.flush();
    }

    @Transactional
    void saveBlocks(List<LayoutBlockEntity> fsBlocks) {
        fsBlocks.forEach(block -> {
            if (block.getBlockId() != null) {
                layoutBlockRepository.getEntityManager().merge(block);
            } else {
                layoutBlockRepository.persist(block);
            }
        });
    }

    @Transactional
    void saveRow(TableRowEntity row) {

        if (row.getTableRowPkId() != null) {
            tableRowRepository.getEntityManager().merge(row);
        } else {
            tableRowRepository.persist(row);
        }
    }

    @Transactional
    void saveHeader(TableHeaderEntity header) {
        if (header.getTableHeaderPkId() != null) {
            tableHeaderRepository.getEntityManager().merge(header);
        } else {
            tableHeaderRepository.persist(header);
        }
    }

    @Transactional
    public List<TableTagEntity> addNewTags(List<String> newTags) {
        List<TableTagEntity> tableTags = newTags.stream().map(tag -> TableTagEntity.builder()
                .tag(tag)
                .createdBy("user")
                .createdTime(LocalDateTime.now())
                .lastModifiedBy("user")
                .lastModifiedTime(LocalDateTime.now())
                .build()).toList();
        tableTagRepository.persist(tableTags);
        return tableTags;
    }

    @Test
    @TestSecurity(user = "user")
    void testValidateBSOrISNtaRowsCoaMapping() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId, 2, 3);
        List<LayoutBlockEntity> ntaBlocks = createBlocks(docId, 2, 3);

        TableTagEntity bsTableTag = addNewTags(List.of(BALANCE_SHEET_TAG)).get(0);
        TableTagEntity isTableTag = addNewTags(List.of(INCOME_STATEMENT_TAG)).get(0);
        List<LayoutBlockEntity> fsBlocks = blocks.stream()
                .peek(block -> block.setTag((block.getBlockId() % 2 == 0 ? bsTableTag : isTableTag)))
                .toList();
        saveBlocks(fsBlocks);

        TableRowEntity ntaLinkedFsRowWithCoaMapping = fsBlocks.get(0).getTableRows().get(0);
        ntaLinkedFsRowWithCoaMapping.setNtaTable(ntaBlocks.get(0));
        saveRow(ntaLinkedFsRowWithCoaMapping);
        CoaDataEntity coaData = addCoaData(2, 90, true);
        addCoaDataMapping(ntaLinkedFsRowWithCoaMapping, coaData.getId(), null);

        TableRowEntity ntaLinkedFsRowWithoutCoaMapping = fsBlocks.get(0).getTableRows().get(1);
        ntaLinkedFsRowWithoutCoaMapping.setNtaTable(ntaBlocks.get(1));
        saveRow(ntaLinkedFsRowWithoutCoaMapping);

        List<Validation.ResponseDto> failedValidations = given().contentType(ContentType.JSON)
                .pathParam("docId", docId)
                .when()
                .get("/{docId}/fs-tables")
                .then()
                .statusCode(200).extract()
                .body()
                .as(new TypeRef<>() {
                });

        List<Validation.ResponseDto> invalidNtaRowsCoaMapping = failedValidations.stream()
                .filter(failedValidation -> failedValidation.msg()
                        .equals(Validation.ValidationError.LINKED_FS_ROW_MAPPED.getMessage()))
                .toList();
        Assertions.assertEquals(1, invalidNtaRowsCoaMapping.size());
        Validation.ResponseDto invalidRow = invalidNtaRowsCoaMapping.get(0);
        Assertions.assertEquals(ntaLinkedFsRowWithCoaMapping.getTableRowPkId().getTableId(), invalidRow.tableId());
        Assertions.assertEquals(ntaLinkedFsRowWithCoaMapping.getTableRowPkId().getRowId().intValue(),
                invalidRow.rowId());
        Assertions.assertEquals(ntaLinkedFsRowWithCoaMapping.getLayoutBlock().getPageNum().intValue(),
                invalidRow.pageNum());
        Assertions.assertEquals(ntaLinkedFsRowWithCoaMapping.getLayoutBlock().getTag().getTag(), invalidRow.tag());
        Assertions.assertEquals(ntaLinkedFsRowWithCoaMapping.getCellsText().get(0), invalidRow.rowText());
        Assertions.assertEquals(StringUtils.EMPTY, invalidRow.coaText());
        Assertions.assertEquals(Validation.ValidationError.LINKED_FS_ROW_MAPPED.getMessage(), invalidRow.msg());
    }

    @Test
    @TestSecurity(user = "user")
    void testValidateBSOrISTableStandardization() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId, 2, 5);
        TableTagEntity bsTableTag = addNewTags(List.of(BALANCE_SHEET_TAG)).get(0);
        TableTagEntity isTableTag = addNewTags(List.of(INCOME_STATEMENT_TAG)).get(0);
        List<LayoutBlockEntity> fsBlocks = blocks.stream()
                .peek(block -> block.setTag((block.getBlockId() % 2 == 0 ? bsTableTag : isTableTag)))
                .toList();
        saveBlocks(fsBlocks);

        LayoutBlockEntity invalidHeaderBlock = fsBlocks.get(0);

        TableHeaderEntity invalidHeader = invalidHeaderBlock.getTableHeaders().get(0);
        invalidHeader.setText("invalidHeader");
        saveHeader(invalidHeader);

        List<Validation.ResponseDto> failedValidations = given().contentType(ContentType.JSON)
                .pathParam("docId", docId)
                .when()
                .get("/{docId}/fs-tables")
                .then()
                .statusCode(200).extract()
                .body()
                .as(new TypeRef<>() {
                });

        List<Validation.ResponseDto> nonStandardizedTable = failedValidations.stream()
                .filter(failedValidation -> failedValidation.msg()
                        .equals(Validation.ValidationError.TABLE_NOT_STANDARDIZED.getMessage()))
                .toList();
        Assertions.assertEquals(1, nonStandardizedTable.size());
        Validation.ResponseDto invalidTable = nonStandardizedTable.get(0);

        Assertions.assertEquals(invalidHeaderBlock.getBlockId(), invalidTable.tableId());
        Assertions.assertEquals(-1, invalidTable.rowId());
        Assertions.assertEquals(invalidHeaderBlock.getPageNum().intValue(), invalidTable.pageNum());
        Assertions.assertEquals(invalidHeaderBlock.getTag().getTag(), invalidTable.tag());
        Assertions.assertEquals(StringUtils.EMPTY, invalidTable.rowText());
        Assertions.assertEquals(StringUtils.EMPTY, invalidTable.coaText());
        Assertions.assertEquals(Validation.ValidationError.TABLE_NOT_STANDARDIZED.getMessage(), invalidTable.msg());
    }

    @Test
    @TestSecurity(user = "user")
    void testValidateBSOrISRowDescriptionsForBS() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId, 3, 5);
        TableTagEntity bsTableTag = addNewTags(List.of(BALANCE_SHEET_TAG)).get(0);
        TableTagEntity isTableTag = addNewTags(List.of(INCOME_STATEMENT_TAG)).get(0);
        blocks.stream()
                .peek(block -> block.setTag((block.getBlockId() % 2 == 0 ? bsTableTag : isTableTag)))
                .toList();
        saveBlocks(blocks);

        List<LayoutBlockEntity> bsBlocks = blocks.stream()
                .filter(block -> block.getTag().getTag().equals(BALANCE_SHEET_TAG))
                .toList();

        List<Validation.RowCategory> bsCategories = Stream.of(Validation.RowCategory.values())
                .filter(category -> !List.of(Validation.RowCategory.INCOME_STATEMENT,
                                Validation.RowCategory.NA)
                        .contains(category))
                .toList();

        bsBlocks.forEach(bsBlock -> bsBlock.getTableRows().forEach(tableRowEntity ->
        {
            List<String> cellsText = new ArrayList<>(tableRowEntity.getCellsText());
            cellsText.set(0,
                    bsCategories.get(new Random().nextInt(bsCategories.size()))
                            .getMessages()
                            .get(0) + SEPARATOR + cellsText.get(0));
            tableRowEntity.setCellsText(cellsText);
        }));
        TableRowEntity invalidBSRow = bsBlocks.get(0).getTableRows().get(0);
        List<String> invalidRowHeader = new ArrayList<>(invalidBSRow.getCellsText());
        invalidRowHeader.set(0, "Invalid Row Header");
        invalidBSRow.setCellsText(invalidRowHeader);
        saveBlocks(bsBlocks);

        List<Validation.ResponseDto> failedValidations = given().contentType(ContentType.JSON)
                .pathParam("docId", docId)
                .when()
                .get("/{docId}/fs-tables")
                .then()
                .statusCode(200).extract()
                .body()
                .as(new TypeRef<>() {
                });

        List<Validation.ResponseDto> bSRowsWithInvalidHeader = failedValidations.stream()
                .filter(failedValidation -> failedValidation.tag().equals(BALANCE_SHEET_TAG) && failedValidation.msg()
                        .equals(Validation.ValidationError.FS_ROW_INCORRECT_HEAD.getMessage()))
                .toList();
        Assertions.assertEquals(1, bSRowsWithInvalidHeader.size());
        Validation.ResponseDto bSRowWithInvalidHeader = bSRowsWithInvalidHeader.get(0);
        Assertions.assertEquals(invalidBSRow.getTableRowPkId().getTableId(), bSRowWithInvalidHeader.tableId());
        Assertions.assertEquals(invalidBSRow.getTableRowPkId().getRowId().intValue(), bSRowWithInvalidHeader.rowId());
        Assertions.assertEquals(invalidBSRow.getLayoutBlock().getPageNum().intValue(),
                bSRowWithInvalidHeader.pageNum());
        Assertions.assertEquals(BALANCE_SHEET_TAG, bSRowWithInvalidHeader.tag());
        Assertions.assertEquals(invalidBSRow.getCellsText().get(0), bSRowWithInvalidHeader.rowText());
        Assertions.assertEquals(StringUtils.EMPTY, bSRowWithInvalidHeader.coaText());
        Assertions.assertEquals(Validation.ValidationError.FS_ROW_INCORRECT_HEAD.getMessage(),
                bSRowWithInvalidHeader.msg());
    }

    @Test
    @TestSecurity(user = "user")
    void testValidateBSOrISRowDescriptionsForIS() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId, 3, 5);
        TableTagEntity bsTableTag = addNewTags(List.of(BALANCE_SHEET_TAG)).get(0);
        TableTagEntity isTableTag = addNewTags(List.of(INCOME_STATEMENT_TAG)).get(0);
        blocks.stream()
                .peek(block -> block.setTag((block.getBlockId() % 2 == 0 ? bsTableTag : isTableTag)))
                .toList();
        saveBlocks(blocks);

        List<LayoutBlockEntity> bsBlocks = blocks.stream()
                .filter(block -> block.getTag().getTag().equals(INCOME_STATEMENT_TAG))
                .toList();

        List<Validation.RowCategory> isCategories = List.of(Validation.RowCategory.INCOME_STATEMENT);

        bsBlocks.forEach(bsBlock -> bsBlock.getTableRows().forEach(tableRowEntity ->
        {
            List<String> cellsText = new ArrayList<>(tableRowEntity.getCellsText());
            cellsText.set(0,
                    isCategories.get(new Random().nextInt(isCategories.size()))
                            .getMessages()
                            .get(0) + SEPARATOR + cellsText.get(0));
            tableRowEntity.setCellsText(cellsText);
        }));
        TableRowEntity invalidBSRow = bsBlocks.get(0).getTableRows().get(0);
        List<String> invalidRowHeader = new ArrayList<>(invalidBSRow.getCellsText());
        invalidRowHeader.set(0, "Invalid " + SEPARATOR + " Row Header");
        invalidBSRow.setCellsText(invalidRowHeader);
        saveBlocks(bsBlocks);

        List<Validation.ResponseDto> failedValidations = given().contentType(ContentType.JSON)
                .pathParam("docId", docId)
                .when()
                .get("/{docId}/fs-tables")
                .then()
                .statusCode(200).extract()
                .body()
                .as(new TypeRef<>() {
                });

        List<Validation.ResponseDto> bSRowsWithInvalidHeader = failedValidations.stream()
                .filter(failedValidation -> failedValidation.tag()
                        .equals(INCOME_STATEMENT_TAG) && failedValidation.msg()
                        .equals(Validation.ValidationError.FS_ROW_INCORRECT_HEAD.getMessage()))
                .toList();
        Assertions.assertEquals(1, bSRowsWithInvalidHeader.size());
        Validation.ResponseDto bSRowWithInvalidHeader = bSRowsWithInvalidHeader.get(0);
        Assertions.assertEquals(invalidBSRow.getTableRowPkId().getTableId(), bSRowWithInvalidHeader.tableId());
        Assertions.assertEquals(invalidBSRow.getTableRowPkId().getRowId().intValue(), bSRowWithInvalidHeader.rowId());
        Assertions.assertEquals(invalidBSRow.getLayoutBlock().getPageNum().intValue(),
                bSRowWithInvalidHeader.pageNum());
        Assertions.assertEquals(INCOME_STATEMENT_TAG, bSRowWithInvalidHeader.tag());
        Assertions.assertEquals(invalidBSRow.getCellsText().get(0), bSRowWithInvalidHeader.rowText());
        Assertions.assertEquals(StringUtils.EMPTY, bSRowWithInvalidHeader.coaText());
        Assertions.assertEquals(Validation.ValidationError.FS_ROW_INCORRECT_HEAD.getMessage(),
                bSRowWithInvalidHeader.msg());
    }

    @Test
    @TestSecurity(user = "user")
    void validateNtaTablesCoaMapping() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> ntaTables = createBlocks(docId, 3, 3);

        LayoutBlockEntity untaggedNtaTable = ntaTables.get(0);
        untaggedNtaTable.setTag(tableTagRepository.findById(NA_TAG_ID));

        LayoutBlockEntity taggedNtaTableWithCoaMappedRow = ntaTables.get(1);
        CoaDataEntity coaData = addCoaData(3, 70, true);
        TableRowEntity coaMappedRow = taggedNtaTableWithCoaMappedRow.getTableRows().get(1);
        addCoaDataMapping(coaMappedRow, coaData.getId(), null);

        LayoutBlockEntity taggedNtaTableWithoutCoaMappedRow = ntaTables.get(2);
        saveBlocks(List.of(untaggedNtaTable, taggedNtaTableWithCoaMappedRow));

        List<Validation.ResponseDto> failedValidations = given().contentType(ContentType.JSON)
                .pathParam("docId", docId)
                .when()
                .get("/{docId}/nta-tables")
                .then()
                .statusCode(200).extract()
                .body()
                .as(new TypeRef<>() {
                });

        List<Validation.ResponseDto> failedValidationsForNoMapping = failedValidations.stream()
                .filter(failedValidation -> failedValidation.msg().equals(NTA_TABLE_NO_MAPPING.getMessage()))
                .toList();
        Assertions.assertEquals(1, failedValidationsForNoMapping.size());
        Validation.ResponseDto failedValidationForNoMapping = failedValidationsForNoMapping.get(0);

        Assertions.assertEquals(taggedNtaTableWithoutCoaMappedRow.getBlockId(), failedValidationForNoMapping.tableId());
        Assertions.assertEquals(-1, failedValidationForNoMapping.rowId());
        Assertions.assertEquals(taggedNtaTableWithoutCoaMappedRow.getPageNum().intValue(),
                failedValidationForNoMapping.pageNum());
        Assertions.assertEquals(taggedNtaTableWithoutCoaMappedRow.getTag().getTag(),
                failedValidationForNoMapping.tag());
        Assertions.assertEquals(StringUtils.EMPTY, failedValidationForNoMapping.rowText());
        Assertions.assertEquals(StringUtils.EMPTY, failedValidationForNoMapping.coaText());
        Assertions.assertEquals(NTA_TABLE_NO_MAPPING.getMessage(),
                failedValidationForNoMapping.msg());
    }

    @Test
    @TestSecurity(user = "user")
    void validateNtaTablesRowLinking() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> ntaTables = createBlocks(docId, 3, 3);
        TableTagEntity bsTableTag = addNewTags(List.of(BALANCE_SHEET_TAG)).get(0);
        LayoutBlockEntity untaggedNtaTable = ntaTables.get(0);
        untaggedNtaTable.setTag(tableTagRepository.findById(NA_TAG_ID));

        LayoutBlockEntity taggedNtaTableLinkedToRow = ntaTables.get(1);
        List<LayoutBlockEntity> fsTables = createBlocks(docId, 1, 2);
        LayoutBlockEntity fsTableWithLinkedRow = fsTables.get(0);
        fsTableWithLinkedRow.setTag(bsTableTag);
        fsTableWithLinkedRow.getTableRows().get(0).setNtaTable(taggedNtaTableLinkedToRow);

        LayoutBlockEntity taggedNtaTableNotLinkedToRow = ntaTables.get(2);
        saveBlocks(List.of(untaggedNtaTable, fsTableWithLinkedRow));

        List<Validation.ResponseDto> failedValidations = given().contentType(ContentType.JSON)
                .pathParam("docId", docId)
                .when()
                .get("/{docId}/nta-tables")
                .then()
                .statusCode(200).extract()
                .body()
                .as(new TypeRef<>() {
                });

        List<Validation.ResponseDto> failedValidationsForFSLink = failedValidations.stream()
                .filter(failedValidation -> failedValidation.msg().equals(NTA_TABLE_NO_FS_LINK.getMessage()))
                .toList();
        Assertions.assertEquals(1, failedValidationsForFSLink.size());
        Validation.ResponseDto failedValidationForFSLink = failedValidationsForFSLink.get(0);

        Assertions.assertEquals(taggedNtaTableNotLinkedToRow.getBlockId(), failedValidationForFSLink.tableId());
        Assertions.assertEquals(-1, failedValidationForFSLink.rowId());
        Assertions.assertEquals(taggedNtaTableNotLinkedToRow.getPageNum().intValue(),
                failedValidationForFSLink.pageNum());
        Assertions.assertEquals(taggedNtaTableNotLinkedToRow.getTag().getTag(), failedValidationForFSLink.tag());
        Assertions.assertEquals(StringUtils.EMPTY, failedValidationForFSLink.rowText());
        Assertions.assertEquals(StringUtils.EMPTY, failedValidationForFSLink.coaText());
        Assertions.assertEquals(NTA_TABLE_NO_FS_LINK.getMessage(),
                failedValidationForFSLink.msg());
    }

    @Test
    @TestSecurity(user = "user")
    void validateNtaTables_BSNTA_ISNTA_TagWithLinkedRowDesc() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> ntaTables = createBlocks(docId, 5, 3);
        List<LayoutBlockEntity> fsTables = createBlocks(docId, 2, 3);

        TableTagEntity bsTableTag = addNewTags(List.of(BALANCE_SHEET_TAG)).get(0);
        TableTagEntity isTableTag = addNewTags(List.of(INCOME_STATEMENT_TAG)).get(0);
        TableTagEntity bsNtaTableTag = addNewTags(List.of(BS_NTA_TABLE_TAG)).get(0);
        TableTagEntity isNtaTableTag = addNewTags(List.of(IS_NTA_TABLE_TAG)).get(0);
        LayoutBlockEntity untaggedNtaTable = ntaTables.get(0);
        untaggedNtaTable.setTag(tableTagRepository.findById(NA_TAG_ID));

        LayoutBlockEntity fsTableWithISTag = fsTables.get(0);
        fsTableWithISTag.setTag(isTableTag);

        LayoutBlockEntity fsTableWithBSTag = fsTables.get(1);
        fsTableWithBSTag.setTag(bsTableTag);

        //BS_NTA table linked to IS table
        LayoutBlockEntity bsNtaTagTableLinkedToIncorrectRowHead = ntaTables.get(1);
        bsNtaTagTableLinkedToIncorrectRowHead.setTag(bsNtaTableTag);
        TableRowEntity rowWithIncorrectHeadForBSNtaTable = fsTableWithISTag.getTableRows().get(0);
        rowWithIncorrectHeadForBSNtaTable.setNtaTable(bsNtaTagTableLinkedToIncorrectRowHead);

        //BS_NTA table linked to BS table
        LayoutBlockEntity bsNtaTagTableLinkedToCorrectRowHead = ntaTables.get(2);
        bsNtaTagTableLinkedToCorrectRowHead.setTag(bsNtaTableTag);
        TableRowEntity rowWithCorrectHeadForBSNtaTable = fsTableWithBSTag.getTableRows().get(0);
        rowWithCorrectHeadForBSNtaTable.setNtaTable(bsNtaTagTableLinkedToCorrectRowHead);

        //IS_NTA table linked to BS table
        LayoutBlockEntity isNtaTagTableLinkedToIncorrectRowHead = ntaTables.get(3);
        isNtaTagTableLinkedToIncorrectRowHead.setTag(isNtaTableTag);
        rowWithIncorrectHeadForBSNtaTable.setNtaTable(bsNtaTagTableLinkedToIncorrectRowHead);
        TableRowEntity rowWithIncorrectHeadForISNtaTable = fsTableWithBSTag.getTableRows().get(1);
        rowWithIncorrectHeadForISNtaTable.setNtaTable(isNtaTagTableLinkedToIncorrectRowHead);

        //IS_NTA table linked to IS table
        LayoutBlockEntity isNtaTagTableLinkedToCorrectRowHead = ntaTables.get(4);
        isNtaTagTableLinkedToCorrectRowHead.setTag(isNtaTableTag);
        TableRowEntity rowWithCorrectHeadForISNtaTable = fsTableWithISTag.getTableRows().get(1);
        rowWithCorrectHeadForISNtaTable.setNtaTable(isNtaTagTableLinkedToCorrectRowHead);

        saveBlocks(List.of(untaggedNtaTable, fsTableWithISTag, fsTableWithBSTag, isNtaTagTableLinkedToCorrectRowHead,
                isNtaTagTableLinkedToIncorrectRowHead, bsNtaTagTableLinkedToCorrectRowHead,
                bsNtaTagTableLinkedToIncorrectRowHead));

        List<Validation.ResponseDto> failedValidations = given().contentType(ContentType.JSON)
                .pathParam("docId", docId)
                .when()
                .get("/{docId}/nta-tables")
                .then()
                .statusCode(200).extract()
                .body()
                .as(new TypeRef<>() {
                });

        List<Validation.ResponseDto> failedValidationsForLinkedTableTag = failedValidations.stream()
                .filter(failedValidation -> failedValidation.msg()
                        .equals(INVALID_TABLE_FOR_LINKED_TABLE_TAG.getMessage()))
                .toList();
        Assertions.assertEquals(2, failedValidationsForLinkedTableTag.size());

        List<Validation.ResponseDto> failedValidationsForBsNtaTag = failedValidationsForLinkedTableTag.stream()
                .filter(failedValidationForLinkedTableTag -> failedValidationForLinkedTableTag.tag()
                        .equals(BS_NTA_TABLE_TAG))
                .toList();
        Assertions.assertEquals(1, failedValidationsForBsNtaTag.size());
        Validation.ResponseDto failedValidationForBsNtaTag = failedValidationsForBsNtaTag.get(0);
        Assertions.assertEquals(rowWithIncorrectHeadForBSNtaTable.getTableRowPkId().getTableId(),
                failedValidationForBsNtaTag.tableId());
        Assertions.assertEquals(rowWithIncorrectHeadForBSNtaTable.getTableRowPkId().getRowId().intValue(),
                failedValidationForBsNtaTag.rowId());
        Assertions.assertEquals(rowWithIncorrectHeadForBSNtaTable.getLayoutBlock().getPageNum().intValue(),
                failedValidationForBsNtaTag.pageNum());
        Assertions.assertEquals(BS_NTA_TABLE_TAG, failedValidationForBsNtaTag.tag());
        Assertions.assertEquals(rowWithIncorrectHeadForBSNtaTable.getCellsText().get(0),
                failedValidationForBsNtaTag.rowText());
        Assertions.assertEquals(StringUtils.EMPTY, failedValidationForBsNtaTag.coaText());
        Assertions.assertEquals(INVALID_TABLE_FOR_LINKED_TABLE_TAG.getMessage(),
                failedValidationForBsNtaTag.msg());

        List<Validation.ResponseDto> failedValidationsForIsNtaTag = failedValidationsForLinkedTableTag.stream()
                .filter(failedValidationForLinkedTableTag -> failedValidationForLinkedTableTag.tag()
                        .equals(IS_NTA_TABLE_TAG))
                .toList();
        Assertions.assertEquals(1, failedValidationsForIsNtaTag.size());
        Validation.ResponseDto failedValidationForIsNtaTag = failedValidationsForIsNtaTag.get(0);
        Assertions.assertEquals(rowWithIncorrectHeadForISNtaTable.getTableRowPkId().getTableId(),
                failedValidationForIsNtaTag.tableId());
        Assertions.assertEquals(rowWithIncorrectHeadForISNtaTable.getTableRowPkId().getRowId().intValue(),
                failedValidationForIsNtaTag.rowId());
        Assertions.assertEquals(rowWithIncorrectHeadForISNtaTable.getLayoutBlock().getPageNum().intValue(),
                failedValidationForIsNtaTag.pageNum());
        Assertions.assertEquals(IS_NTA_TABLE_TAG, failedValidationForIsNtaTag.tag());
        Assertions.assertEquals(rowWithIncorrectHeadForISNtaTable.getCellsText().get(0),
                failedValidationForIsNtaTag.rowText());
        Assertions.assertEquals(StringUtils.EMPTY, failedValidationForIsNtaTag.coaText());
        Assertions.assertEquals(INVALID_TABLE_FOR_LINKED_TABLE_TAG.getMessage(),
                failedValidationForIsNtaTag.msg());
    }

    @Test
    @TestSecurity(user = "user")
    void validateNtaTables_TagWithLinkedRowDesc() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> ntaTables = createBlocks(docId, 3, 3);
        List<LayoutBlockEntity> fsTables = createBlocks(docId, 1, 3);

        LayoutBlockEntity untaggedNtaTable = ntaTables.get(0);
        untaggedNtaTable.setTag(tableTagRepository.findById(NA_TAG_ID));

        TableTagEntity isTableTag = addNewTags(List.of(INCOME_STATEMENT_TAG)).get(0);
        LayoutBlockEntity isTable = fsTables.get(0);
        isTable.setTag(isTableTag);

        LayoutBlockEntity ntaTagTableLinkedToCorrectRowHead = ntaTables.get(1);
        TableTagEntity tag1 = addNewTags(List.of("BS.Assets.Other Assets")).get(0);
        ntaTagTableLinkedToCorrectRowHead.setTag(tag1);
        TableRowEntity rowWithCorrectHeadForNtaTable = isTable.getTableRows().get(0);
        List<String> cellsText = new ArrayList<>(rowWithCorrectHeadForNtaTable.getCellsText());
        cellsText.set(0, "Current assets - 2. Cash and Cash Equivalents");
        rowWithCorrectHeadForNtaTable.setCellsText(cellsText);
        rowWithCorrectHeadForNtaTable.setNtaTable(ntaTagTableLinkedToCorrectRowHead);

        LayoutBlockEntity ntaTagTableLinkedToIncorrectRowHead = ntaTables.get(2);
        TableTagEntity tag2 = addNewTags(List.of("IS.Income Statement.Tax Expenses")).get(0);
        ntaTagTableLinkedToIncorrectRowHead.setTag(tag2);
        TableRowEntity rowWithInCorrectHeadForNtaTable = isTable.getTableRows().get(1);
        List<String> cellsText2 = new ArrayList<>(rowWithCorrectHeadForNtaTable.getCellsText());
        cellsText2.set(0, "Equity - 2. Paid Up Capital");
        rowWithInCorrectHeadForNtaTable.setCellsText(cellsText2);
        rowWithInCorrectHeadForNtaTable.setNtaTable(ntaTagTableLinkedToIncorrectRowHead);

        saveBlocks(List.of(untaggedNtaTable, isTable, ntaTagTableLinkedToCorrectRowHead,
                ntaTagTableLinkedToIncorrectRowHead));

        List<Validation.ResponseDto> failedValidations = given().contentType(ContentType.JSON)
                .pathParam("docId", docId)
                .when()
                .get("/{docId}/nta-tables")
                .then()
                .statusCode(200).extract()
                .body()
                .as(new TypeRef<>() {
                });

        List<Validation.ResponseDto> failedValidationsForLinkedTableTag = failedValidations.stream()
                .filter(failedValidation -> failedValidation.msg()
                        .equals(INVALID_FS_ROW_HEADER_FOR_LINKED_TABLE_TAG.getMessage()))
                .toList();
        Assertions.assertEquals(1, failedValidationsForLinkedTableTag.size());

        Validation.ResponseDto failedValidationForNtaTag = failedValidationsForLinkedTableTag.get(0);

        Assertions.assertEquals(rowWithInCorrectHeadForNtaTable.getTableRowPkId().getTableId(),
                failedValidationForNtaTag.tableId());
        Assertions.assertEquals(rowWithInCorrectHeadForNtaTable.getTableRowPkId().getRowId().intValue(),
                failedValidationForNtaTag.rowId());
        Assertions.assertEquals(rowWithInCorrectHeadForNtaTable.getLayoutBlock().getPageNum().intValue(),
                failedValidationForNtaTag.pageNum());
        Assertions.assertEquals(ntaTagTableLinkedToIncorrectRowHead.getTag().getTag(),
                failedValidationForNtaTag.tag());
        Assertions.assertEquals(rowWithInCorrectHeadForNtaTable.getCellsText().get(0),
                failedValidationForNtaTag.rowText());
        Assertions.assertEquals(StringUtils.EMPTY, failedValidationForNtaTag.coaText());
        Assertions.assertEquals(INVALID_FS_ROW_HEADER_FOR_LINKED_TABLE_TAG.getMessage(),
                failedValidationForNtaTag.msg());
    }

    @Test
    @TestSecurity(user = "user")
    void validateCoaMappedRows_TagsForCoaMappedRows() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> taggedTables = createBlocks(docId, 3, 3);

        Mockito.when(exchangeService.getCoasFromIds(Mockito.anyList()))
                .thenReturn(
                        List.of(new CoaItemDto(2, "coaText2", "coaDescription2", "walnut", 2, "category2", true, false),
                                new CoaItemDto(3, "coaText3", "coaDescription3", "walnut", 3, "category3", true,
                                        false)));
        LayoutBlockEntity unwantedFSTaggedTable = taggedTables.get(0);
        TableTagEntity unwantedFSTableTag = addNewTags(List.of(UNWANTED_FS_TABLES.get(0))).get(0);
        unwantedFSTaggedTable.setTag(unwantedFSTableTag);

        LayoutBlockEntity coaMappedRowTaggedTable = taggedTables.get(1);
        TableTagEntity tag1 = addNewTags(List.of("BS.Assets.Current Assets.Trade and Other Receivables")).get(0);
        coaMappedRowTaggedTable.setTag(tag1);
        TableRowEntity coaMappedRorForTaggedTable = coaMappedRowTaggedTable.getTableRows().get(0);
        CoaDataEntity coaData = addCoaData(3, 100, true);
        addCoaDataMapping(coaMappedRorForTaggedTable, coaData.getId(), null);

        LayoutBlockEntity coaMappedRowUntaggedTable = taggedTables.get(2);
        coaMappedRowUntaggedTable.setTag(tableTagRepository.findById(NA_TAG_ID));
        TableRowEntity coaMappedRorForUntaggedTable = coaMappedRowUntaggedTable.getTableRows().get(0);
        CoaDataEntity coaData1 = addCoaData(2, 70, false);
        addCoaDataMapping(coaMappedRorForUntaggedTable, coaData1.getId(), null);

        saveBlocks(List.of(unwantedFSTaggedTable, coaMappedRowTaggedTable, coaMappedRowUntaggedTable));

        List<Validation.ResponseDto> failedValidations = given().contentType(ContentType.JSON)
                .pathParam("docId", docId)
                .when()
                .get("/{docId}/coa-mapped-rows")
                .then()
                .statusCode(200).extract()
                .body()
                .as(new TypeRef<>() {
                });

        List<Validation.ResponseDto> failedValidationsForCoaMappedRows = failedValidations.stream()
                .filter(failedValidation -> failedValidation.msg()
                        .equals(COA_MAPPED_ROW_NO_TABLE_TAG.getMessage()))
                .toList();
        Assertions.assertEquals(1, failedValidationsForCoaMappedRows.size());

        Validation.ResponseDto failedValidationForCoaMappedRows = failedValidationsForCoaMappedRows.get(0);

        Assertions.assertEquals(coaMappedRorForUntaggedTable.getTableRowPkId().getTableId(),
                failedValidationForCoaMappedRows.tableId());
        Assertions.assertEquals(coaMappedRorForUntaggedTable.getTableRowPkId().getRowId().intValue(),
                failedValidationForCoaMappedRows.rowId());
        Assertions.assertEquals(coaMappedRorForUntaggedTable.getLayoutBlock().getPageNum().intValue(),
                failedValidationForCoaMappedRows.pageNum());
        Assertions.assertEquals(StringUtils.EMPTY,
                failedValidationForCoaMappedRows.tag());
        Assertions.assertEquals(coaMappedRorForUntaggedTable.getCellsText().get(0),
                failedValidationForCoaMappedRows.rowText());
        Assertions.assertEquals(StringUtils.EMPTY, failedValidationForCoaMappedRows.coaText());
        Assertions.assertEquals(COA_MAPPED_ROW_NO_TABLE_TAG.getMessage(),
                failedValidationForCoaMappedRows.msg());
    }

    @Test
    @TestSecurity(user = "user")
    void validateCoaMappedRows_CategoriesForCoaMappedRows_FSTables() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> fsTables = createBlocks(docId, 2, 3);

        LayoutBlockEntity isTable = fsTables.get(0);
        TableTagEntity isTableTag = addNewTags(List.of(INCOME_STATEMENT_TAG)).get(0);
        isTable.setTag(isTableTag);
        TableRowEntity coaMappedRowForIsTable = isTable.getTableRows().get(0);
        List<String> cellsText1 = new ArrayList<>(coaMappedRowForIsTable.getCellsText());
        cellsText1.set(0, "Incomestatement - 6. Operatingincome");
        coaMappedRowForIsTable.setCellsText(cellsText1);
        CoaDataEntity coaData = addCoaData(2, 100, true);
        addCoaDataMapping(coaMappedRowForIsTable, coaData.getId(), null);
        CoaItemDto coaItemDtoForIsMappedRow = new CoaItemDto(2, "6.IS.Other Income", "6.IS.Other Income", "walnut", 11,
                "Income Statement.Other Income (Expense)", true, true);

        LayoutBlockEntity fsTableWithBSTag = fsTables.get(1);
        TableTagEntity bsTableTag = addNewTags(List.of(BALANCE_SHEET_TAG)).get(0);
        fsTableWithBSTag.setTag(bsTableTag);
        TableRowEntity coaMappedRowForBsTable = fsTableWithBSTag.getTableRows().get(0);
        List<String> cellsText2 = new ArrayList<>(coaMappedRowForBsTable.getCellsText());
        cellsText2.set(0, "Current assets - 3. Receivables");
        coaMappedRowForBsTable.setCellsText(cellsText2);
        CoaDataEntity coaData2 = addCoaData(3, 70, true);
        addCoaDataMapping(coaMappedRowForBsTable, coaData2.getId(), null);
        CoaItemDto coaItemDtoForBSMappedRow = new CoaItemDto(3, "2.BS.NCA.Land", "2.BS.NCA.Land", "walnut", 12,
                "Non Current Assets",
                true,
                false);

        saveBlocks(List.of(fsTableWithBSTag, isTable));

        Mockito.when(exchangeService.getCoasFromIds(Mockito.anyList()))
                .thenReturn(
                        List.of(coaItemDtoForBSMappedRow, coaItemDtoForIsMappedRow
                        ));

        List<Validation.ResponseDto> failedValidations = given().contentType(ContentType.JSON)
                .pathParam("docId", docId)
                .when()
                .get("/{docId}/coa-mapped-rows")
                .then()
                .statusCode(200).extract()
                .body()
                .as(new TypeRef<>() {
                });

        List<Validation.ResponseDto> failedValidationsForCoaMappedRows = failedValidations.stream()
                .filter(failedValidation -> failedValidation.msg()
                        .equals(LVL1_CATEGORY_HEAD_MISMATCH.getMessage()))
                .toList();
        Assertions.assertEquals(1, failedValidationsForCoaMappedRows.size());

        Validation.ResponseDto failedValidationForCoaMappedRows = failedValidationsForCoaMappedRows.get(0);

        Assertions.assertEquals(coaMappedRowForBsTable.getTableRowPkId().getTableId(),
                failedValidationForCoaMappedRows.tableId());
        Assertions.assertEquals(coaMappedRowForBsTable.getTableRowPkId().getRowId().intValue(),
                failedValidationForCoaMappedRows.rowId());
        Assertions.assertEquals(coaMappedRowForBsTable.getLayoutBlock().getPageNum().intValue(),
                failedValidationForCoaMappedRows.pageNum());
        Assertions.assertEquals(coaMappedRowForBsTable.getLayoutBlock().getTag().getTag(),
                failedValidationForCoaMappedRows.tag());
        Assertions.assertEquals(coaMappedRowForBsTable.getCellsText().get(0),
                failedValidationForCoaMappedRows.rowText());
        Assertions.assertEquals(coaItemDtoForBSMappedRow.coaText(), failedValidationForCoaMappedRows.coaText());
        Assertions.assertEquals(LVL1_CATEGORY_HEAD_MISMATCH.getMessage(),
                failedValidationForCoaMappedRows.msg());
    }

    @Test
    @TestSecurity(user = "user")
    void validateCoaMappedRows_CategoriesForCoaMappedRows_NTATables() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> ntaTables = createBlocks(docId, 6, 3);

        LayoutBlockEntity bsNtaTableWithValidMappedRow = ntaTables.get(0);
        TableTagEntity bsNtaTableTag = addNewTags(List.of(BS_NTA_TABLE_TAG)).get(0);
        bsNtaTableWithValidMappedRow.setTag(bsNtaTableTag);
        TableRowEntity validMappedRowForBsNtaTable = bsNtaTableWithValidMappedRow.getTableRows().get(0);
        CoaDataEntity coaDataForValidMappedRowForBsNtaTable = addCoaData(2, 100, true);
        addCoaDataMapping(validMappedRowForBsNtaTable, coaDataForValidMappedRowForBsNtaTable.getId(), null);
        CoaItemDto coaItemDtoForValidMappedRowForBsNtaTable = new CoaItemDto(
                coaDataForValidMappedRowForBsNtaTable.getCoaId(),
                "1.BS.CA.Cash",
                "1.BS.CA.Cash", "walnut", 13,
                "Current Assets", true, true);

        LayoutBlockEntity bsNtaTableWithInvalidMappedRow = ntaTables.get(1);
        bsNtaTableWithInvalidMappedRow.setTag(bsNtaTableTag);
        TableRowEntity inValidMappedRowForBsNtaTable = bsNtaTableWithInvalidMappedRow.getTableRows().get(0);
        CoaDataEntity coaDataForInValidMappedRowForBsNtaTable = addCoaData(3, 100, true);
        addCoaDataMapping(inValidMappedRowForBsNtaTable, coaDataForInValidMappedRowForBsNtaTable.getId(), null);
        CoaItemDto coaItemDtoForInValidMappedRowForBsNtaTable = new CoaItemDto(
                coaDataForInValidMappedRowForBsNtaTable.getCoaId(),
                "6.IS.Total Interest Income",
                "6.IS.Total Interest Income", "walnut", 14,
                "Income Statement.Interest Income", true, true);

        LayoutBlockEntity isNtaTableWithValidMappedRow = ntaTables.get(2);
        TableTagEntity isNtaTableTag = addNewTags(List.of(IS_NTA_TABLE_TAG)).get(0);
        isNtaTableWithValidMappedRow.setTag(isNtaTableTag);
        TableRowEntity validMappedRowForIsNtaTable = isNtaTableWithValidMappedRow.getTableRows().get(0);
        CoaDataEntity coaDataForValidMappedRowForIsNtaTable = addCoaData(4, 100, true);
        addCoaDataMapping(validMappedRowForIsNtaTable, coaDataForValidMappedRowForIsNtaTable.getId(), null);
        CoaItemDto coaItemDtoForValidMappedRowForIsNtaTable = new CoaItemDto(
                coaDataForValidMappedRowForIsNtaTable.getCoaId(),
                "6.IS.Other Taxes",
                "6.IS.Other Taxes", "walnut", 15,
                "Income Statement.Operating Expense", true, true);

        LayoutBlockEntity isNtaTableWithInvalidMappedRow = ntaTables.get(3);
        isNtaTableWithInvalidMappedRow.setTag(isNtaTableTag);
        TableRowEntity invalidMappedRowForIsNtaTable = isNtaTableWithInvalidMappedRow.getTableRows().get(0);
        CoaDataEntity coaDataForInvalidMappedRowForIsNtaTable = addCoaData(5, 100, true);
        addCoaDataMapping(invalidMappedRowForIsNtaTable, coaDataForInvalidMappedRowForIsNtaTable.getId(), null);
        CoaItemDto coaItemDtoForInvalidMappedRowForIsNtaTable = new CoaItemDto(
                coaDataForInvalidMappedRowForIsNtaTable.getCoaId(),
                "3.BS.CL.Hire Purchase-CP",
                "3.BS.CL.Hire Purchase-CP", "walnut", 16,
                "Current Liabilities", true, true);

        LayoutBlockEntity ntaTableWithValidMappedRow = ntaTables.get(4);
        TableTagEntity tableTag = addNewTags(List.of("BS.Assets.Non-Current Assets.Investment Property")).get(0);
        ntaTableWithValidMappedRow.setTag(tableTag);
        TableRowEntity validMappedRowForNtaTable = ntaTableWithValidMappedRow.getTableRows().get(0);
        CoaDataEntity coaDataForValidMappedRowForNtaTable = addCoaData(6, 100, true);
        addCoaDataMapping(validMappedRowForNtaTable, coaDataForValidMappedRowForNtaTable.getId(), null);
        CoaItemDto coaItemDtoForValidMappedRowForNtaTable = new CoaItemDto(
                coaDataForValidMappedRowForNtaTable.getCoaId(),
                "2.BS.NCA.Investment Properties",
                "2.BS.NCA.Investment Properties", "walnut", 17,
                "Non Current Assets", true, true);

        LayoutBlockEntity ntaTableWithInvalidMappedRow = ntaTables.get(5);
        TableTagEntity tableTag2 = addNewTags(
                List.of("BS.Liabilities.Current Liabilities.Trade and Other Payables")).get(0);
        ntaTableWithInvalidMappedRow.setTag(tableTag2);
        TableRowEntity invalidMappedRowForNtaTable = ntaTableWithInvalidMappedRow.getTableRows().get(0);
        CoaDataEntity coaDataForInvalidMappedRowForNtaTable = addCoaData(7, 100, true);
        addCoaDataMapping(invalidMappedRowForNtaTable, coaDataForInvalidMappedRowForNtaTable.getId(), null);
        CoaItemDto coaItemDtoForInvalidMappedRowForNtaTable = new CoaItemDto(
                coaDataForInvalidMappedRowForNtaTable.getCoaId(),
                "3.BS.NCL.Deferred Debt",
                "3.BS.NCL.Deferred Debt", "walnut", 18,
                "Non Current Liabilities", true, true);

        saveBlocks(List.of(bsNtaTableWithInvalidMappedRow, bsNtaTableWithValidMappedRow, isNtaTableWithInvalidMappedRow,
                isNtaTableWithValidMappedRow, ntaTableWithInvalidMappedRow, ntaTableWithValidMappedRow));

        Mockito.when(exchangeService.getCoasFromIds(Mockito.anyList()))
                .thenReturn(
                        List.of(coaItemDtoForInValidMappedRowForBsNtaTable, coaItemDtoForValidMappedRowForBsNtaTable,
                                coaItemDtoForInvalidMappedRowForIsNtaTable, coaItemDtoForValidMappedRowForIsNtaTable,
                                coaItemDtoForInvalidMappedRowForNtaTable, coaItemDtoForValidMappedRowForNtaTable
                        ));

        List<Validation.ResponseDto> failedValidations = given().contentType(ContentType.JSON)
                .pathParam("docId", docId)
                .when()
                .get("/{docId}/coa-mapped-rows")
                .then()
                .statusCode(200).extract()
                .body()
                .as(new TypeRef<>() {
                });

        List<Validation.ResponseDto> failedValidationsForCoaMappedRows = failedValidations.stream()
                .filter(failedValidation -> failedValidation.msg()
                        .equals(LVL1_CATEGORY_TABLE_CATEGORY_MISMATCH.getMessage()))
                .toList();
        Assertions.assertEquals(3, failedValidationsForCoaMappedRows.size());

        List<Validation.ResponseDto> failedCoaMappingValidationsForBSNtaTable =
                failedValidationsForCoaMappedRows.stream()
                        .filter(failedValidationForCoaMappedRows -> failedValidationForCoaMappedRows.tag()
                                .equals(BS_NTA_TABLE_TAG))
                        .toList();
        Assertions.assertEquals(1, failedCoaMappingValidationsForBSNtaTable.size());
        Validation.ResponseDto failedCoaMappingValidationForBSNtaTable = failedCoaMappingValidationsForBSNtaTable.get(
                0);

        Assertions.assertEquals(inValidMappedRowForBsNtaTable.getTableRowPkId().getTableId(),
                failedCoaMappingValidationForBSNtaTable.tableId());
        Assertions.assertEquals(inValidMappedRowForBsNtaTable.getTableRowPkId().getRowId().intValue(),
                failedCoaMappingValidationForBSNtaTable.rowId());
        Assertions.assertEquals(inValidMappedRowForBsNtaTable.getLayoutBlock().getPageNum().intValue(),
                failedCoaMappingValidationForBSNtaTable.pageNum());
        Assertions.assertEquals(inValidMappedRowForBsNtaTable.getLayoutBlock().getTag().getTag(),
                failedCoaMappingValidationForBSNtaTable.tag());
        Assertions.assertEquals(inValidMappedRowForBsNtaTable.getCellsText().get(0),
                failedCoaMappingValidationForBSNtaTable.rowText());
        Assertions.assertEquals(coaItemDtoForInValidMappedRowForBsNtaTable.coaText(),
                failedCoaMappingValidationForBSNtaTable.coaText());
        Assertions.assertEquals(LVL1_CATEGORY_TABLE_CATEGORY_MISMATCH.getMessage(),
                failedCoaMappingValidationForBSNtaTable.msg());

        List<Validation.ResponseDto> failedCoaMappingValidationsForISNtaTable =
                failedValidationsForCoaMappedRows.stream()
                        .filter(failedValidationForCoaMappedRows -> failedValidationForCoaMappedRows.tag()
                                .equals(IS_NTA_TABLE_TAG))
                        .toList();
        Assertions.assertEquals(1, failedCoaMappingValidationsForISNtaTable.size());
        Validation.ResponseDto failedCoaMappingValidationForISNtaTable = failedCoaMappingValidationsForISNtaTable.get(
                0);

        Assertions.assertEquals(invalidMappedRowForIsNtaTable.getTableRowPkId().getTableId(),
                failedCoaMappingValidationForISNtaTable.tableId());
        Assertions.assertEquals(invalidMappedRowForIsNtaTable.getTableRowPkId().getRowId().intValue(),
                failedCoaMappingValidationForISNtaTable.rowId());
        Assertions.assertEquals(invalidMappedRowForIsNtaTable.getLayoutBlock().getPageNum().intValue(),
                failedCoaMappingValidationForISNtaTable.pageNum());
        Assertions.assertEquals(invalidMappedRowForIsNtaTable.getLayoutBlock().getTag().getTag(),
                failedCoaMappingValidationForISNtaTable.tag());
        Assertions.assertEquals(invalidMappedRowForIsNtaTable.getCellsText().get(0),
                failedCoaMappingValidationForISNtaTable.rowText());
        Assertions.assertEquals(coaItemDtoForInvalidMappedRowForIsNtaTable.coaText(),
                failedCoaMappingValidationForISNtaTable.coaText());
        Assertions.assertEquals(LVL1_CATEGORY_TABLE_CATEGORY_MISMATCH.getMessage(),
                failedCoaMappingValidationForISNtaTable.msg());

        List<Validation.ResponseDto> failedCoaMappingValidationsForNtaTable =
                failedValidationsForCoaMappedRows.stream()
                        .filter(failedValidationForCoaMappedRows -> !failedValidationForCoaMappedRows.tag()
                                .equals(IS_NTA_TABLE_TAG) && !failedValidationForCoaMappedRows.tag()
                                .equals(BS_NTA_TABLE_TAG))
                        .toList();
        Assertions.assertEquals(1, failedCoaMappingValidationsForNtaTable.size());
        Validation.ResponseDto failedCoaMappingValidationForNtaTable = failedCoaMappingValidationsForNtaTable.get(
                0);

        Assertions.assertEquals(invalidMappedRowForNtaTable.getTableRowPkId().getTableId(),
                failedCoaMappingValidationForNtaTable.tableId());
        Assertions.assertEquals(invalidMappedRowForNtaTable.getTableRowPkId().getRowId().intValue(),
                failedCoaMappingValidationForNtaTable.rowId());
        Assertions.assertEquals(invalidMappedRowForNtaTable.getLayoutBlock().getPageNum().intValue(),
                failedCoaMappingValidationForNtaTable.pageNum());
        Assertions.assertEquals(invalidMappedRowForNtaTable.getLayoutBlock().getTag().getTag(),
                failedCoaMappingValidationForNtaTable.tag());
        Assertions.assertEquals(invalidMappedRowForNtaTable.getCellsText().get(0),
                failedCoaMappingValidationForNtaTable.rowText());
        Assertions.assertEquals(coaItemDtoForInvalidMappedRowForNtaTable.coaText(),
                failedCoaMappingValidationForNtaTable.coaText());
        Assertions.assertEquals(LVL1_CATEGORY_TABLE_CATEGORY_MISMATCH.getMessage(),
                failedCoaMappingValidationForNtaTable.msg());
    }
}


