package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.common.clients.IamApiClient;
import com.walnut.vegaspread.extraction.entity.Bbox;
import com.walnut.vegaspread.extraction.entity.CoaDataEntity;
import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.entity.TableHeaderEntity;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.entity.TableTagEntity;
import com.walnut.vegaspread.extraction.model.BlockTypeEnum;
import com.walnut.vegaspread.extraction.primarykey.TableHeaderPkId;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import com.walnut.vegaspread.extraction.repository.CoaDataRepository;
import com.walnut.vegaspread.extraction.repository.CoaMappingRepository;
import com.walnut.vegaspread.extraction.repository.ExtractedRowCoaDataRepository;
import com.walnut.vegaspread.extraction.repository.LayoutBlockRepository;
import com.walnut.vegaspread.extraction.repository.TableHeaderRepository;
import com.walnut.vegaspread.extraction.repository.TableRowRepository;
import com.walnut.vegaspread.extraction.repository.TableTagRepository;
import com.walnut.vegaspread.extraction.service.ExchangeService;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.flywaydb.core.Flyway;
import org.jboss.resteasy.reactive.RestResponse;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;
import java.util.stream.IntStream;

import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;

@QuarkusTest
@TestHTTPEndpoint(TestOnlyProcessorApiResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class TestOnlyProcessorApiResourceTest {

    // COA Data Mapping Tests - Testing createOrUpdateCoaDataMapping endpoint
    private static final String ROW_COA_PREFIX = "/row-coa-data-mapping";
    @InjectMock
    @RestClient
    IamApiClient iamApiClient;
    @Inject
    LayoutBlockRepository layoutBlockRepository;
    @Inject
    TableRowRepository tableRowRepository;
    @Inject
    TableHeaderRepository tableHeaderRepository;
    @Inject
    ExtractedRowCoaDataRepository extractedRowCoaDataRepository;
    @Inject
    CoaDataRepository coaDataRepository;
    @Inject
    CoaMappingRepository coaMappingRepository;
    @InjectMock
    ExchangeService exchangeService;
    @Inject
    TableTagRepository tableTagRepository;
    @Inject
    Flyway flyway;
    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
    }

    @BeforeEach
    @Transactional
    void setUp() {
        Mockito.when(iamApiClient.validateApiKey(Mockito.any(), Mockito.any())).thenReturn(RestResponse.ok());
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
        Mockito.reset(exchangeService);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
    }

    @Transactional
    List<LayoutBlockEntity> createBlocks(UUID docId, int numBlocks, boolean createHeaders, boolean createRows) {
        List<TableTagEntity> tableTags = IntStream.range(0, numBlocks).mapToObj(id -> TableTagEntity.builder()
                .tag("tag" + id)
                .createdBy("user")
                .createdTime(LocalDateTime.now())
                .lastModifiedBy("user")
                .lastModifiedTime(LocalDateTime.now())
                .build()).toList();
        tableTagRepository.persist(tableTags);
        List<LayoutBlockEntity> blocks = IntStream.range(0, numBlocks).mapToObj(id -> LayoutBlockEntity.builder()
                .docId(docId)
                .pageNum((short) (id % 3))
                .tag(tableTags.get(id))
                .blockType(createRows || createHeaders ? BlockTypeEnum.TABLE :
                        BlockTypeEnum.values()[id % BlockTypeEnum.values().length])
                .score((byte) new Random().nextInt(100))
                .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                .tagExplainabilityId(id)
                .build()).toList();
        layoutBlockRepository.persist(blocks);

        if (createHeaders) {
            List<TableHeaderEntity> headers = new ArrayList<>();
            for (LayoutBlockEntity block : blocks) {
                List<TableHeaderEntity> blockHeaders = IntStream.range(0, 3)
                        .mapToObj(id -> TableHeaderEntity.builder()
                                .tableHeaderPkId(new TableHeaderPkId(block.getBlockId(), id))
                                .layoutBlock(block)
                                .text("header" + id)
                                .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                                .score((byte) new Random().nextInt(100))
                                .pos(id)
                                .build())
                        .toList();
                headers.addAll(blockHeaders);
                block.setTableHeaders(blockHeaders);
            }
            tableHeaderRepository.persist(headers);
        }

        if (createRows) {
            List<TableRowEntity> rows = new ArrayList<>();
            for (LayoutBlockEntity block : blocks) {
                List<TableRowEntity> blockRows = IntStream.range(0, 3)
                        .mapToObj(id -> TableRowEntity.builder()
                                .tableRowPkId(new TableRowPkId(block.getBlockId(), id))
                                .layoutBlock(block)
                                .cellsText(List.of("row" + id, "row" + id))
                                .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                                .score((byte) new Random().nextInt(100))
                                .comment("comment" + id)
                                .parentText("parent" + id)
                                .pos(id)
                                .build())
                        .toList();
                rows.addAll(blockRows);
                block.setTableRows(blockRows);
            }
            tableRowRepository.persist(rows);
        }
        return blocks;
    }

    int getCoord() {
        return new Random().nextInt(1000);
    }

    @Transactional
    public CoaDataEntity addCoaData(Integer coaId, Integer coaScore, Boolean useCoa) {
        CoaDataEntity coaData = CoaDataEntity.builder()
                .coaId(coaId)
                .coaScore(coaScore.byteValue())
                .useCoa(useCoa)
                .build();
        coaDataRepository.persist(coaData);
        return coaData;
    }

    @Transactional
    public CoaMappingEntity addCoaMapping() {
        TableTagEntity tableTag = TableTagEntity.builder()
                .tag("TestType")
                .createdBy("user")
                .createdTime(LocalDateTime.now())
                .lastModifiedBy("user")
                .lastModifiedTime(LocalDateTime.now())
                .build();
        tableTagRepository.persist(tableTag);
        CoaMappingEntity coaMapping = CoaMappingEntity.builder()
                .docId(UUID.randomUUID())
                .tableId(1)
                .rowId((short) 1)
                .coaId(1)
                .tableTypeId(tableTag.getId())
                
                .rowParent("TestParent")
                .text("TestText")
                .fsHeader("TestHeader")
                .fsText("TestFsText")
                .isApproved(false)
                .build();
        coaMappingRepository.persist(coaMapping);
        return coaMapping;
    }

    @Transactional
    public void addCoaDataMapping(TableRowEntity row, Integer coaDataId, CoaMappingEntity explainability) {
        extractedRowCoaDataRepository.saveRowCoaJoin(
                List.of(new ExtractedTableRowCoaDataJoinEntity(row.getTableRowPkId(), coaDataId,
                        explainability)));
    }

    @Test
    void testCreateOrUpdateCoaDataMapping_Create() {
        // Arrange
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId, 3, false, true);
        TableRowEntity tableRow = blocks.get(0).getTableRows().get(0);
        CoaDataEntity coaData = addCoaData(2, 50, false);

        List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> extractedTableRowCoaDataJoinDtos = List.of(
                new com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                        tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId().intValue(),
                        new com.walnut.vegaspread.extraction.model.CoaDataDto.Create(coaData.getCoaId(),
                                coaData.getCoaScore().intValue(),
                                coaData.getUseCoa()), null));

        // Act
        List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.Response> response = given()
                .contentType(ContentType.JSON)
                .header("X-API-Key", "test-api-key")
                .body(extractedTableRowCoaDataJoinDtos)
                .when()
                .post(ROW_COA_PREFIX)
                .then().statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.Response>>() {
                });

        // Assert - verify response structure and content
        Assertions.assertNotNull(response);
        Assertions.assertEquals(1, response.size());

        com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.Response responseDto = response.get(0);
        Assertions.assertEquals(tableRow.getTableRowPkId().getTableId(), responseDto.tableId());
        Assertions.assertEquals(tableRow.getTableRowPkId().getRowId().intValue(), responseDto.rowId());

        // Verify COA data mapping
        Assertions.assertNotNull(responseDto.coaData());
        Assertions.assertEquals(coaData.getCoaId(), responseDto.coaData().coaId());
        Assertions.assertEquals(coaData.getCoaScore().intValue(), responseDto.coaData().coaScore());
        Assertions.assertEquals(coaData.getUseCoa(), responseDto.coaData().useCoa());

        // Verify explainability can be present (different from ExtractedRowCoaDataJoinResource)
        // This resource passes through the explainability from input

        // Verify database state
        Optional<ExtractedTableRowCoaDataJoinEntity> optExtractedTableRowCoaDataJoinEntity =
                extractedRowCoaDataRepository.findByRowOptional(
                        tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId());
        Assertions.assertTrue(optExtractedTableRowCoaDataJoinEntity.isPresent());
        Assertions.assertEquals(coaData.getId(),
                optExtractedTableRowCoaDataJoinEntity.get().getExtractedTableRowCoaDataPkId().getCoaDataId());

        // Verify audit was called with true (API key authenticated)
        Mockito.verify(exchangeService, Mockito.times(1))
                .auditExtractedRowCoaDataCreate(Mockito.any(), Mockito.eq(true));
    }

    @Test
    void testCreateOrUpdateCoaDataMapping_Update() {
        // Arrange
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId, 3, false, true);
        TableRowEntity tableRow = blocks.get(0).getTableRows().get(0);
        CoaDataEntity originalCoaData = addCoaData(2, 50, false);
        addCoaDataMapping(tableRow, originalCoaData.getId(), null);

        CoaDataEntity newCoaData = addCoaData(3, 75, true);
        List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> extractedTableRowCoaDataJoinDtos = List.of(
                new com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                        tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId().intValue(),
                        new com.walnut.vegaspread.extraction.model.CoaDataDto.Create(newCoaData.getCoaId(),
                                newCoaData.getCoaScore().intValue(),
                                newCoaData.getUseCoa()), null));

        // Act
        List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.Response> response = given()
                .contentType(ContentType.JSON)
                .header("X-API-Key", "test-api-key")
                .body(extractedTableRowCoaDataJoinDtos)
                .when()
                .post(ROW_COA_PREFIX)
                .then().statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.Response>>() {
                });

        // Assert - verify response reflects updated data
        Assertions.assertNotNull(response);
        Assertions.assertEquals(1, response.size());

        com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.Response responseDto = response.get(0);
        Assertions.assertEquals(newCoaData.getCoaId(), responseDto.coaData().coaId());
        Assertions.assertEquals(newCoaData.getCoaScore().intValue(), responseDto.coaData().coaScore());
        Assertions.assertEquals(newCoaData.getUseCoa(), responseDto.coaData().useCoa());

        // Verify database state
        Optional<ExtractedTableRowCoaDataJoinEntity> optExtractedTableRowCoaDataJoinEntity =
                extractedRowCoaDataRepository.findByRowOptional(
                        tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId());
        Assertions.assertTrue(optExtractedTableRowCoaDataJoinEntity.isPresent());
        Assertions.assertEquals(newCoaData.getId(),
                optExtractedTableRowCoaDataJoinEntity.get().getExtractedTableRowCoaDataPkId().getCoaDataId());

        // Verify audit was called for update with true (API key authenticated)
        Mockito.verify(exchangeService, Mockito.times(1))
                .auditExtractedRowCoaDataUpdate(Mockito.any(), Mockito.eq(true));
    }

    @Test
    void testCreateOrUpdateCoaDataMapping_EmptyList() {
        // Act
        List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.Response> response = given()
                .contentType(ContentType.JSON)
                .header("X-API-Key", "test-api-key")
                .body(List.of())
                .when()
                .post(ROW_COA_PREFIX)
                .then().statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.Response>>() {
                });

        // Assert
        Assertions.assertNotNull(response);
        Assertions.assertEquals(0, response.size());

        // Verify no audit calls were made
        Mockito.verify(exchangeService, Mockito.never()).auditExtractedRowCoaDataCreate(Mockito.any(), Mockito.any());
        Mockito.verify(exchangeService, Mockito.never()).auditExtractedRowCoaDataUpdate(Mockito.any(), Mockito.any());
        Mockito.verify(exchangeService, Mockito.never()).auditExtractedRowCoaDataDelete(Mockito.any(), Mockito.any());
    }

    @Test
    void testCreateOrUpdateCoaDataMapping_WithExplainability() {
        // Arrange
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId, 3, false, true);
        TableRowEntity tableRow = blocks.get(0).getTableRows().get(0);
        CoaDataEntity coaData = addCoaData(2, 50, false);
        CoaMappingEntity explainability = addCoaMapping();

        List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> extractedTableRowCoaDataJoinDtos = List.of(
                new com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                        tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId().intValue(),
                        new com.walnut.vegaspread.extraction.model.CoaDataDto.Create(coaData.getCoaId(),
                                coaData.getCoaScore().intValue(),
                                coaData.getUseCoa()), explainability.getId()));

        // Act
        List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.Response> response = given()
                .contentType(ContentType.JSON)
                .header("X-API-Key", "test-api-key")
                .body(extractedTableRowCoaDataJoinDtos)
                .when()
                .post(ROW_COA_PREFIX)
                .then().statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.Response>>() {
                });

        // Assert - verify explainability is preserved (different from ExtractedRowCoaDataJoinResource)
        Assertions.assertNotNull(response);
        Assertions.assertEquals(1, response.size());

        com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.Response responseDto = response.get(0);
        Assertions.assertNotNull(responseDto.explainability());
        Assertions.assertEquals(explainability.getId(), responseDto.explainability().id());

        // Verify database state includes explainability
        Optional<ExtractedTableRowCoaDataJoinEntity> dbEntity =
                extractedRowCoaDataRepository.findByRowOptional(
                        tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId());
        Assertions.assertTrue(dbEntity.isPresent());
        Assertions.assertNotNull(dbEntity.get().getExplainability());
        Assertions.assertEquals(explainability.getId(), dbEntity.get().getExplainability().getId());

        // Verify audit was called with true (API key authenticated)
        Mockito.verify(exchangeService, Mockito.times(1))
                .auditExtractedRowCoaDataCreate(Mockito.any(), Mockito.eq(true));
    }

    @Test
    void testCreateOrUpdateCoaDataMapping_MultipleOperations() {
        // Arrange
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId, 5, false, true);

        TableRowEntity row1 = blocks.get(0).getTableRows().get(0);
        TableRowEntity row2 = blocks.get(1).getTableRows().get(0);
        TableRowEntity row3 = blocks.get(2).getTableRows().get(0);

        CoaDataEntity coaData1 = addCoaData(10, 80, true);
        CoaDataEntity coaData2 = addCoaData(20, 90, false);
        CoaDataEntity coaData3 = addCoaData(30, 70, true);

        List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> multipleRequest =
                List.of(
                        new com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                                row1.getTableRowPkId().getTableId(),
                                row1.getTableRowPkId().getRowId().intValue(),
                                new com.walnut.vegaspread.extraction.model.CoaDataDto.Create(coaData1.getCoaId(),
                                        coaData1.getCoaScore().intValue(),
                                        coaData1.getUseCoa()), null
                        ),
                        new com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                                row2.getTableRowPkId().getTableId(),
                                row2.getTableRowPkId().getRowId().intValue(),
                                new com.walnut.vegaspread.extraction.model.CoaDataDto.Create(coaData2.getCoaId(),
                                        coaData2.getCoaScore().intValue(),
                                        coaData2.getUseCoa()), null
                        ),
                        new com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                                row3.getTableRowPkId().getTableId(),
                                row3.getTableRowPkId().getRowId().intValue(),
                                new com.walnut.vegaspread.extraction.model.CoaDataDto.Create(coaData3.getCoaId(),
                                        coaData3.getCoaScore().intValue(),
                                        coaData3.getUseCoa()), null
                        )
                );

        // Act
        List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.Response> response = given()
                .contentType(ContentType.JSON)
                .header("X-API-Key", "test-api-key")
                .body(multipleRequest)
                .when()
                .post(ROW_COA_PREFIX)
                .then().statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.Response>>() {
                });

        // Assert - verify all responses are returned
        Assertions.assertNotNull(response);
        Assertions.assertEquals(3, response.size());

        // Verify each response has correct data
        for (int i = 0; i < response.size(); i++) {
            com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.Response responseDto = response.get(
                    i);
            Assertions.assertNotNull(responseDto.coaData());
        }

        // Verify audit was called for create operations with API key authentication
        Mockito.verify(exchangeService, Mockito.times(1))
                .auditExtractedRowCoaDataCreate(Mockito.any(), Mockito.eq(true));
    }

    @Test
    void testCreateOrUpdateCoaDataMapping_Delete() {
        // Arrange
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId, 3, false, true);
        TableRowEntity tableRow = blocks.get(0).getTableRows().get(0);
        CoaDataEntity currentCoaData = addCoaData(2, 50, false);
        addCoaDataMapping(tableRow, currentCoaData.getId(), null);

        // This DTO represents a deletion request: coaData with special values indicating "no mapping"
        List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> deleteRequest =
                List.of(
                        new com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                                tableRow.getTableRowPkId().getTableId(),
                                tableRow.getTableRowPkId().getRowId().intValue(),
                                new com.walnut.vegaspread.extraction.model.CoaDataDto.Create(1, 1, false),
                                // Special values indicating "no mapping"
                                null
                        )
                );

        // Act
        List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.Response> response = given()
                .contentType(ContentType.JSON)
                .header("X-API-Key", "test-api-key")
                .body(deleteRequest)
                .when()
                .post(ROW_COA_PREFIX)
                .then().statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.Response>>() {
                });

        // Assert - verify response is empty for deletion
        Assertions.assertNotNull(response);
        Assertions.assertEquals(0, response.size());

        // Verify mapping was deleted from database
        Optional<ExtractedTableRowCoaDataJoinEntity> deletedEntity =
                extractedRowCoaDataRepository.findByRowOptional(tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId());
        Assertions.assertFalse(deletedEntity.isPresent(), "Mapping should be deleted");

        // Verify audit was called for deletion with API key authentication
        Mockito.verify(exchangeService, Mockito.times(1))
                .auditExtractedRowCoaDataDelete(Mockito.any(), Mockito.eq(true));
    }

    @Test
    void testCreateOrUpdateCoaDataMapping_ApiKeyAuthentication() {
        // Arrange
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId, 3, false, true);
        TableRowEntity tableRow = blocks.get(0).getTableRows().get(0);
        CoaDataEntity coaData = addCoaData(2, 50, false);

        List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> createRequest =
                List.of(
                        new com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                                tableRow.getTableRowPkId().getTableId(),
                                tableRow.getTableRowPkId().getRowId().intValue(),
                                new com.walnut.vegaspread.extraction.model.CoaDataDto.Create(coaData.getCoaId(),
                                        coaData.getCoaScore().intValue(),
                                        coaData.getUseCoa()), null
                        )
                );

        // Act
        given().contentType(ContentType.JSON)
                .header("X-API-Key", "test-api-key")
                .body(createRequest)
                .when()
                .post(ROW_COA_PREFIX)
                .then().statusCode(200);

        // Assert - verify audit was called with true (API key authenticated)
        // This is the key difference from ExtractedRowCoaDataJoinResource which always passes false
        Mockito.verify(exchangeService, Mockito.times(1))
                .auditExtractedRowCoaDataCreate(Mockito.any(), Mockito.eq(true));
    }

    @Test
    void testCreateOrUpdateCoaDataMapping_MixedOperations() {
        // Arrange
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId, 5, false, true);

        TableRowEntity row1 = blocks.get(0).getTableRows().get(0); // Will be created
        TableRowEntity row2 = blocks.get(1).getTableRows().get(0); // Will be updated
        TableRowEntity row3 = blocks.get(2).getTableRows().get(0); // Will be deleted

        CoaDataEntity coaData1 = addCoaData(10, 50, true);
        CoaDataEntity coaData2 = addCoaData(20, 60, false);
        CoaDataEntity coaData3 = addCoaData(30, 70, true);

        // Pre-populate row2 and row3 with existing mappings
        addCoaDataMapping(row2, coaData2.getId(), null);
        addCoaDataMapping(row3, coaData3.getId(), null);

        // Create mixed operations request
        List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> mixedRequest =
                List.of(
                        // Create new mapping for row1
                        new com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                                row1.getTableRowPkId().getTableId(),
                                row1.getTableRowPkId().getRowId().intValue(),
                                new com.walnut.vegaspread.extraction.model.CoaDataDto.Create(coaData1.getCoaId(),
                                        coaData1.getCoaScore().intValue(),
                                        coaData1.getUseCoa()), null
                        ),
                        // Update existing mapping for row2
                        new com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                                row2.getTableRowPkId().getTableId(),
                                row2.getTableRowPkId().getRowId().intValue(),
                                new com.walnut.vegaspread.extraction.model.CoaDataDto.Create(25, 80, true),
                                // Different COA data
                                null
                        ),
                        // Delete mapping for row3
                        new com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                                row3.getTableRowPkId().getTableId(),
                                row3.getTableRowPkId().getRowId().intValue(),
                                new com.walnut.vegaspread.extraction.model.CoaDataDto.Create(1, 1, false),
                                // Deletion values
                                null
                        )
                );

        // Act
        List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.Response> response = given()
                .contentType(ContentType.JSON)
                .header("X-API-Key", "test-api-key")
                .body(mixedRequest)
                .when()
                .post(ROW_COA_PREFIX)
                .then().statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<List<com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto.Response>>() {
                });

        // Assert - verify response contains only non-deleted mappings
        Assertions.assertNotNull(response);
        Assertions.assertEquals(2, response.size()); // row1 and row2, row3 should be deleted

        // Row1 should be created
        Optional<ExtractedTableRowCoaDataJoinEntity> row1Entity =
                extractedRowCoaDataRepository.findByRowOptional(row1.getTableRowPkId().getTableId(),
                        row1.getTableRowPkId().getRowId());
        Assertions.assertTrue(row1Entity.isPresent());

        // Row2 should be updated
        Optional<ExtractedTableRowCoaDataJoinEntity> row2Entity =
                extractedRowCoaDataRepository.findByRowOptional(row2.getTableRowPkId().getTableId(),
                        row2.getTableRowPkId().getRowId());
        Assertions.assertTrue(row2Entity.isPresent());

        // Row3 should be deleted
        Optional<ExtractedTableRowCoaDataJoinEntity> row3Entity =
                extractedRowCoaDataRepository.findByRowOptional(row3.getTableRowPkId().getTableId(),
                        row3.getTableRowPkId().getRowId());
        Assertions.assertFalse(row3Entity.isPresent());

        // Verify all audit types were called with API key authentication
        Mockito.verify(exchangeService, Mockito.times(1))
                .auditExtractedRowCoaDataCreate(Mockito.any(), Mockito.eq(true));
        Mockito.verify(exchangeService, Mockito.times(1))
                .auditExtractedRowCoaDataUpdate(Mockito.any(), Mockito.eq(true));
        Mockito.verify(exchangeService, Mockito.times(1))
                .auditExtractedRowCoaDataDelete(Mockito.any(), Mockito.eq(true));
    }
}
