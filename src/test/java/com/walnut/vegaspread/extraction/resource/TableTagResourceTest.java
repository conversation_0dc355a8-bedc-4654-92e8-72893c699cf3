package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.common.model.extraction.TableTagDto;
import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.extraction.entity.TableTagEntity;
import com.walnut.vegaspread.extraction.repository.TableTagRepository;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.IntStream;

import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.containsString;

@QuarkusTest
@TestHTTPEndpoint(TableTagResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class TableTagResourceTest {

    private static final String TEST_USER = "user";
    @Inject
    TableTagRepository tableTagRepository;
    @Inject
    Flyway flyway;
    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
    }

    @Transactional
    public List<TableTagEntity> createTableTags(int count) {
        List<TableTagEntity> tableTagEntities = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            TableTagEntity tableTagEntity = TableTagEntity.builder()
                    .tag("Tag" + i)
                    .createdBy(TEST_USER)
                    .createdTime(LocalDateTime.now())
                    .lastModifiedBy(TEST_USER)
                    .lastModifiedTime(LocalDateTime.now())
                    .build();
            tableTagEntities.add(tableTagEntity);
        }
        tableTagRepository.persist(tableTagEntities);
        return tableTagEntities;
    }

    @Test
    @TestSecurity(user = TEST_USER, roles = Roles.ADMIN)
    void testCreateTableTag() {
        List<String> tagsToCreate = List.of("NewTag1", "NewTag2");

        List<TableTagDto.Response> response = given().contentType(ContentType.JSON)
                .body(tagsToCreate)
                .when()
                .post()
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<>() {
                });

        Assertions.assertNotNull(response);
        Assertions.assertEquals(tagsToCreate.size(), response.size());
        tagsToCreate.forEach(tag -> {
            TableTagEntity foundEntity = tableTagRepository.findByTag(tag);
            Assertions.assertNotNull(foundEntity, "Tag should be found in DB: " + tag);
            Assertions.assertEquals(tag, foundEntity.getTag());
            Assertions.assertEquals(TEST_USER, foundEntity.getCreatedBy());
        });
    }

    @Test
    @TestSecurity(user = TEST_USER, roles = Roles.ADMIN)
    void testCreateTableTag_nullTags() {
        given().contentType(ContentType.JSON)
                .body("null")
                .when()
                .post()
                .then()
                .statusCode(400)
                .body(containsString("must not be empty"));
    }

    @Test
    @TestSecurity(user = TEST_USER, roles = Roles.ADMIN)
    void testCreateTableTag_emptyTags() {
        given().contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .when()
                .post()
                .then()
                .statusCode(400)
                .body(containsString("must not be empty"));
    }

    @Test
    @TestSecurity(user = TEST_USER, roles = Roles.ADMIN)
    void testCreateTableTag_blankTags() {
        List<String> tagsToCreate = new ArrayList<>();
        tagsToCreate.add("ValidTag");
        tagsToCreate.add("");
        tagsToCreate.add("  ");
        tagsToCreate.add(null);

        given().contentType(ContentType.JSON)
                .body(tagsToCreate)
                .when()
                .post()
                .then()
                .statusCode(400)
                .body(containsString("must not be blank"));
    }

    @Test
    @TestSecurity(user = TEST_USER, roles = Roles.ADMIN)
    void testUpdateTableTag() {
        List<TableTagEntity> initialTags = createTableTags(2);
        TableTagEntity tag1 = initialTags.get(0);
        TableTagEntity tag2 = initialTags.get(1);

        List<TableTagDto.Update> updates = List.of(
                new TableTagDto.Update(tag1.getId(), "UpdatedTag1"),
                new TableTagDto.Update(tag2.getId(), "UpdatedTag2")
        );

        List<TableTagDto.Response> response = given().contentType(ContentType.JSON)
                .body(updates)
                .when()
                .patch()
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<>() {
                });

        Assertions.assertNotNull(response);
        Assertions.assertEquals(updates.size(), response.size());

        updates.forEach(updateDto -> {
            TableTagEntity foundEntity = tableTagRepository.findById(updateDto.id());
            Assertions.assertNotNull(foundEntity, "Updated tag should be found in DB: " + updateDto.id());
            Assertions.assertEquals(updateDto.tag(), foundEntity.getTag());
            Assertions.assertEquals(TEST_USER, foundEntity.getLastModifiedBy());
        });
    }

    @Test
    @TestSecurity(user = TEST_USER, roles = Roles.ADMIN)
    void testUpdateTableTag_nullUpdates() {
        given().contentType(ContentType.JSON)
                .body("null")
                .when()
                .patch()
                .then()
                .statusCode(400)
                .body(containsString("must not be null"));
    }

    @Test
    @TestSecurity(user = TEST_USER, roles = Roles.ADMIN)
    void testUpdateTableTag_emptyUpdates() {
        given().contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .when()
                .patch()
                .then()
                .statusCode(400)
                .body(containsString("must not be empty"));
    }

    @Test
    @TestSecurity(user = TEST_USER, roles = Roles.ADMIN)
    void testUpdateTableTag_nonExistentId() {
        createTableTags(1);
        List<TableTagDto.Update> updates = List.of(
                new TableTagDto.Update(9999, "NonExistentTag")
        );

        List<TableTagDto.Response> response = given().contentType(ContentType.JSON)
                .body(updates)
                .when()
                .patch()
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<>() {
                });

        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.isEmpty());
    }

    @Test
    @TestSecurity(user = TEST_USER, roles = Roles.ADMIN)
    void testDeleteTableTag() {
        List<TableTagEntity> initialTags = createTableTags(3);
        List<Integer> tagIdsToDelete = initialTags.stream()
                .map(TableTagEntity::getId)
                .toList();

        long deletedCount = given().contentType(ContentType.JSON)
                .body(tagIdsToDelete)
                .when()
                .delete()
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(Long.class);

        Assertions.assertEquals(tagIdsToDelete.size(), deletedCount);
        tagIdsToDelete.forEach(tagId -> {
            TableTagEntity foundEntity = tableTagRepository.findById(tagId);
            Assertions.assertNull(foundEntity, "Deleted tag should not be found in DB: " + tagId);
        });
    }

    @Test
    @TestSecurity(user = TEST_USER, roles = Roles.ADMIN)
    void testDeleteTableTag_nullTagIds() {
        given().contentType(ContentType.JSON)
                .body("null")
                .when()
                .delete()
                .then()
                .statusCode(400)
                .body(containsString("must not be empty"));
    }

    @Test
    @TestSecurity(user = TEST_USER, roles = Roles.ADMIN)
    void testDeleteTableTag_emptyTagIds() {
        given().contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .when()
                .delete()
                .then()
                .statusCode(400)
                .body(containsString("must not be empty"));
    }

    @Test
    @TestSecurity(user = TEST_USER, roles = Roles.ADMIN)
    void testDeleteTableTag_nullIdInList() {
        List<Integer> tagIdsToDelete = new ArrayList<>();
        tagIdsToDelete.add(1);
        tagIdsToDelete.add(null);
        tagIdsToDelete.add(2);

        given().contentType(ContentType.JSON)
                .body(tagIdsToDelete)
                .when()
                .delete()
                .then()
                .statusCode(400)
                .body(containsString("must not be null"));
    }

    @Test
    @TestSecurity(user = TEST_USER)
    void testGetTableTags() {
        List<TableTagEntity> initialTags = createTableTags(5);
        List<TableTagDto.Response> expectedTags = initialTags.stream()
                .map(TableTagEntity::toDto)
                .toList();

        List<TableTagDto.Response> retrievedTags = Arrays.asList(given()
                .when()
                .get("/list")
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(TableTagDto.Response[].class));

        Assertions.assertNotNull(retrievedTags);
        Assertions.assertEquals(expectedTags.size(), retrievedTags.size());
        for (int i = 0; i < expectedTags.size(); i++) {
            TableTagDto.Response expected = expectedTags.get(i);
            TableTagDto.Response retrieved = retrievedTags.get(i);
            Assertions.assertEquals(expected.id(), retrieved.id(), "ID should match");
            Assertions.assertEquals(expected.tag(), retrieved.tag(), "Tag should match");
            Assertions.assertEquals(expected.createdBy(), retrieved.createdBy(), "CreatedBy should match");
            Assertions.assertEquals(expected.lastModifiedBy(), retrieved.lastModifiedBy(),
                    "LastModifiedBy should match");
        }
    }

    @Test
    @TestSecurity(user = TEST_USER)
    void testGetTableTagsByIds() {
        List<TableTagEntity> initialTags = createTableTags(5);
        List<Integer> idsToRetrieve = IntStream.of(initialTags.get(0).getId(), initialTags.get(2).getId())
                .boxed()
                .toList();

        List<TableTagDto.Response> expectedTags = initialTags.stream()
                .filter(tag -> idsToRetrieve.contains(tag.getId()))
                .map(TableTagEntity::toDto)
                .toList();

        List<TableTagDto.Response> retrievedTags = given().contentType(ContentType.JSON)
                .body(idsToRetrieve)
                .when()
                .post("/list/ids")
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<>() {
                });

        Assertions.assertNotNull(retrievedTags);
        Assertions.assertEquals(expectedTags.size(), retrievedTags.size());
        retrievedTags.forEach(retrievedTag ->
                Assertions.assertTrue(idsToRetrieve.contains(retrievedTag.id()),
                        "Retrieved tag should be in expected list"));
    }

    @Test
    @TestSecurity(user = TEST_USER)
    void testGetTableTagsByIds_nullTagIds() {
        given().contentType(ContentType.JSON)
                .body("null")
                .when()
                .post("/list/ids")
                .then()
                .statusCode(400)
                .body(containsString("must not be null"));
    }

    @Test
    @TestSecurity(user = TEST_USER)
    void testGetTableTagsByIds_emptyTagIds() {
        List<TableTagDto.Response> retrievedTags = given().contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .when()
                .post("/list/ids")
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<>() {
                });

        Assertions.assertNotNull(retrievedTags);
        Assertions.assertTrue(retrievedTags.isEmpty());
    }

    @Test
    @TestSecurity(user = TEST_USER)
    void testGetTableTagsByIds_nullIdInList() {
        List<Integer> tagIdsToRetrieve = new ArrayList<>();
        tagIdsToRetrieve.add(1);
        tagIdsToRetrieve.add(null);
        tagIdsToRetrieve.add(2);

        given().contentType(ContentType.JSON)
                .body(tagIdsToRetrieve)
                .when()
                .post("/list/ids")
                .then()
                .statusCode(400)
                .body(containsString("must not be null"));
    }
}
