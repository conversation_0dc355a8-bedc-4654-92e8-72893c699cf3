package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.extraction.entity.Bbox;
import com.walnut.vegaspread.extraction.entity.CoaDataEntity;
import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.entity.TableHeaderEntity;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.entity.TableTagEntity;
import com.walnut.vegaspread.extraction.model.BlockTypeEnum;
import com.walnut.vegaspread.extraction.model.CoaDataDto;
import com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto;
import com.walnut.vegaspread.extraction.primarykey.TableHeaderPkId;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import com.walnut.vegaspread.extraction.repository.CoaDataRepository;
import com.walnut.vegaspread.extraction.repository.CoaMappingRepository;
import com.walnut.vegaspread.extraction.repository.ExtractedRowCoaDataRepository;
import com.walnut.vegaspread.extraction.repository.LayoutBlockRepository;
import com.walnut.vegaspread.extraction.repository.TableHeaderRepository;
import com.walnut.vegaspread.extraction.repository.TableRowRepository;
import com.walnut.vegaspread.extraction.repository.TableTagRepository;
import com.walnut.vegaspread.extraction.service.ExchangeService;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;
import java.util.stream.IntStream;

import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.containsString;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@QuarkusTest
@TestHTTPEndpoint(ExtractedRowCoaDataJoinResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ExtractedRowCoaDataResourceTest {

    @Inject
    LayoutBlockRepository layoutBlockRepository;
    @Inject
    TableRowRepository tableRowRepository;
    @Inject
    TableHeaderRepository tableHeaderRepository;
    @Inject
    ExtractedRowCoaDataRepository extractedRowCoaDataRepository;
    @Inject
    CoaDataRepository coaDataRepository;
    @Inject
    CoaMappingRepository coaMappingRepository;
    @Inject
    TableTagRepository tableTagRepository;
    @Inject
    Flyway flyway;
    @InjectMock
    ExchangeService exchangeService;

    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
        reset(exchangeService);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
    }

    int getCoord() {
        return new Random().nextInt(1000);
    }

    @Transactional
    List<LayoutBlockEntity> createBlocks(UUID docId) {
        List<TableTagEntity> tableTags = IntStream.range(0, 5).mapToObj(id -> TableTagEntity.builder()
                .tag("tag" + id)
                .createdBy("user")
                .createdTime(LocalDateTime.now())
                .lastModifiedBy("user")
                .lastModifiedTime(LocalDateTime.now())
                .build()).toList();
        tableTagRepository.persist(tableTags);
        List<LayoutBlockEntity> blocks = IntStream.range(0, 5).mapToObj(id -> LayoutBlockEntity.builder()
                .docId(docId)
                .pageNum((short) (id % 3))
                .tag(tableTags.get(id))
                .blockType(BlockTypeEnum.TABLE)
                .score((byte) new Random().nextInt(100))
                .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                .tagExplainabilityId(id)
                .build()).toList();
        layoutBlockRepository.persist(blocks);

        for (LayoutBlockEntity block : blocks) {
            List<TableHeaderEntity> headers = IntStream.range(0, 3).mapToObj(id -> TableHeaderEntity.builder()
                    .tableHeaderPkId(new TableHeaderPkId(block.getBlockId(), id))
                    .layoutBlock(block)
                    .text("header" + id)
                    .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                    .score((byte) new Random().nextInt(100))
                    .pos(id)
                    .build()).toList();
            block.setTableHeaders(headers);
            tableHeaderRepository.persist(headers);

            List<TableRowEntity> rows = IntStream.range(0, 3).mapToObj(id -> TableRowEntity.builder()
                    .tableRowPkId(new TableRowPkId(block.getBlockId(), id))
                    .layoutBlock(block)
                    .cellsText(List.of("row" + id, "data" + id))
                    .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                    .score((byte) new Random().nextInt(100))
                    .comment("comment" + id)
                    .parentText("parent" + id)
                    .pos(id)
                    .build()).toList();
            block.setTableRows(rows);
            tableRowRepository.persist(rows);
            layoutBlockRepository.persist(block);
        }
        return blocks;
    }

    @Transactional
    public CoaDataEntity addCoaData(Integer coaId, Integer coaScore, Boolean useCoa) {
        CoaDataEntity coaData = CoaDataEntity.builder()
                .coaId(coaId)
                .coaScore(coaScore.byteValue())
                .useCoa(useCoa)
                .build();
        coaDataRepository.persist(coaData);
        return coaData;
    }

    @Transactional
    public CoaMappingEntity addCoaMapping() {
        TableTagEntity tableTag = TableTagEntity.builder()
                .tag("TestType")
                .createdBy("user")
                .createdTime(LocalDateTime.now())
                .lastModifiedBy("user")
                .lastModifiedTime(LocalDateTime.now())
                .build();
        tableTagRepository.persist(tableTag);
        CoaMappingEntity coaMapping = CoaMappingEntity.builder()
                .docId(UUID.randomUUID())
                .tableId(1)
                .rowId((short) 1)
                .coaId(2)
                .tableTypeId(tableTag.getId())
                .rowParent("TestParent")
                .text("TestText")
                .fsHeader("TestHeader")
                .fsText("TestFsText")
                .isApproved(false)
                .build();
        coaMappingRepository.persist(coaMapping);
        return coaMapping;
    }

    @Transactional
    public void addCoaDataMapping(TableRowEntity row, Integer coaDataId, CoaMappingEntity explainability) {
        extractedRowCoaDataRepository.saveRowCoaJoin(
                List.of(new ExtractedTableRowCoaDataJoinEntity(row.getTableRowPkId(), coaDataId,
                        explainability)));
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.MAP_COA)
    @DisplayName("Should create COA data mapping and return proper response DTO")
    void testCreateCoaDataMapping() {

        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        TableRowEntity tableRow = blocks.get(0).getTableRows().get(0);
        CoaDataEntity coaData = addCoaData(2, 50, false);

        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> extractedTableRowCoaDataJoinDtos = List.of(
                new ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId().intValue(),
                        new CoaDataDto.Create(coaData.getCoaId(), coaData.getCoaScore().intValue(),
                                coaData.getUseCoa()), null));

        ExtractedTableRowCoaDataJoinDto.Response[] response = given().contentType(ContentType.JSON)
                .body(extractedTableRowCoaDataJoinDtos)
                .when()
                .post()
                .then().statusCode(200)
                .extract()
                .body()
                .as(ExtractedTableRowCoaDataJoinDto.Response[].class);

        // Assert - verify response structure and content
        assertNotNull(response);
        assertEquals(1, response.length);

        ExtractedTableRowCoaDataJoinDto.Response responseDto = response[0];
        assertEquals(tableRow.getTableRowPkId().getTableId(), responseDto.tableId());
        assertEquals(tableRow.getTableRowPkId().getRowId().intValue(), responseDto.rowId());

        // Verify COA data mapping
        assertNotNull(responseDto.coaData());
        assertEquals(coaData.getCoaId(), responseDto.coaData().coaId());
        assertEquals(coaData.getCoaScore().intValue(), responseDto.coaData().coaScore());
        assertEquals(coaData.getUseCoa(), responseDto.coaData().useCoa());

        // Verify explainability is null (resource behavior)
        assertNull(responseDto.explainability());

        // Verify database state
        for (ExtractedTableRowCoaDataJoinDto.CreateOrUpdate dto : extractedTableRowCoaDataJoinDtos) {
            Optional<ExtractedTableRowCoaDataJoinEntity> optExtractedTableRowCoaDataJoinEntity =
                    extractedRowCoaDataRepository.findByRowOptional(dto.tableId(), dto.rowId().shortValue());
            assertTrue(optExtractedTableRowCoaDataJoinEntity.isPresent());
            assertEquals(coaData.getId(),
                    optExtractedTableRowCoaDataJoinEntity.get().getExtractedTableRowCoaDataPkId().getCoaDataId());
            assertNull(optExtractedTableRowCoaDataJoinEntity.get().getExplainability());
        }
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.MAP_COA)
    @DisplayName("Should update existing COA data mapping and return updated response")
    void testUpdateCoaDataMapping() {

        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        TableRowEntity tableRow = blocks.get(0).getTableRows().get(0);
        CoaDataEntity originalCoaData = addCoaData(2, 50, false);
        addCoaDataMapping(tableRow, originalCoaData.getId(), null);

        CoaDataEntity newCoaData = addCoaData(3, 75, true);
        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> extractedTableRowCoaDataJoinDtos = List.of(
                new ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId().intValue(),
                        new CoaDataDto.Create(newCoaData.getCoaId(), newCoaData.getCoaScore().intValue(),
                                newCoaData.getUseCoa()), null));

        ExtractedTableRowCoaDataJoinDto.Response[] response = given().contentType(ContentType.JSON)
                .body(extractedTableRowCoaDataJoinDtos)
                .when()
                .post()
                .then().statusCode(200)
                .extract()
                .body()
                .as(ExtractedTableRowCoaDataJoinDto.Response[].class);

        // Assert - verify response reflects updated data
        assertNotNull(response);
        assertEquals(1, response.length);

        ExtractedTableRowCoaDataJoinDto.Response responseDto = response[0];
        assertEquals(newCoaData.getCoaId(), responseDto.coaData().coaId());
        assertEquals(newCoaData.getCoaScore().intValue(), responseDto.coaData().coaScore());
        assertEquals(newCoaData.getUseCoa(), responseDto.coaData().useCoa());
        assertNull(responseDto.explainability());

        // Verify database state
        for (ExtractedTableRowCoaDataJoinDto.CreateOrUpdate dto : extractedTableRowCoaDataJoinDtos) {
            Optional<ExtractedTableRowCoaDataJoinEntity> optExtractedTableRowCoaDataJoinEntity =
                    extractedRowCoaDataRepository.findByRowOptional(dto.tableId(), dto.rowId().shortValue());
            assertTrue(optExtractedTableRowCoaDataJoinEntity.isPresent());
            assertEquals(newCoaData.getId(),
                    optExtractedTableRowCoaDataJoinEntity.get().getExtractedTableRowCoaDataPkId().getCoaDataId());
            assertNull(optExtractedTableRowCoaDataJoinEntity.get().getExplainability());
        }
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.MAP_COA)
    @DisplayName("Should handle empty list input and return empty response")
    void testCreateCoaDataMappingEmptyList() {

        ExtractedTableRowCoaDataJoinDto.Response[] response = given().contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .when()
                .post()
                .then().statusCode(200)
                .extract()
                .body()
                .as(ExtractedTableRowCoaDataJoinDto.Response[].class);

        // Assert
        assertNotNull(response);
        assertEquals(0, response.length);

        // Verify no audit calls were made
        verify(exchangeService, never()).auditExtractedRowCoaDataCreate(any(), any());
        verify(exchangeService, never()).auditExtractedRowCoaDataUpdate(any(), any());
        verify(exchangeService, never()).auditExtractedRowCoaDataDelete(any(), any());
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.MAP_COA)
    @DisplayName("Should handle null list input with 500 error")
    void testCreateCoaDataMappingNullList() {

        ExtractedTableRowCoaDataJoinDto.Response[] response = given().contentType(ContentType.JSON)
                .when()
                .post()
                .then().statusCode(200)
                .extract()
                .body()
                .as(ExtractedTableRowCoaDataJoinDto.Response[].class);

        // Assert
        assertNotNull(response);
        assertEquals(0, response.length);

        // Verify no audit calls were made since the request failed
        verify(exchangeService, never()).auditExtractedRowCoaDataCreate(any(), any());
        verify(exchangeService, never()).auditExtractedRowCoaDataUpdate(any(), any());
        verify(exchangeService, never()).auditExtractedRowCoaDataDelete(any(), any());
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.MAP_COA)
    @DisplayName("Should always nullify explainability regardless of input")
    void testExplainabilityNullificationBehavior() {

        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        TableRowEntity tableRow = blocks.get(0).getTableRows().get(0);
        CoaDataEntity coaData = addCoaData(2, 50, false);
        CoaMappingEntity explainability = addCoaMapping();

        // Create request with explainability ID (should be nullified by resource)
        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> createRequest = List.of(
                new ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                        tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId().intValue(),
                        new CoaDataDto.Create(coaData.getCoaId(), coaData.getCoaScore().intValue(),
                                coaData.getUseCoa()), explainability.getId() // This should be nullified
                )
        );

        ExtractedTableRowCoaDataJoinDto.Response[] response = given().contentType(ContentType.JSON)
                .body(createRequest)
                .when()
                .post()
                .then().statusCode(200)
                .extract()
                .body()
                .as(ExtractedTableRowCoaDataJoinDto.Response[].class);

        // Assert - verify explainability is null despite being provided in input
        assertNotNull(response);
        assertEquals(1, response.length);
        assertNull(response[0].explainability());

        // Verify in database that explainability is also null
        Optional<ExtractedTableRowCoaDataJoinEntity> dbEntity =
                extractedRowCoaDataRepository.findByRowOptional(
                        tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId());
        assertTrue(dbEntity.isPresent());
        assertNull(dbEntity.get().getExplainability());
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.MAP_COA)
    @DisplayName("Should handle multiple operations and return all responses")
    void testMultipleOperationsResponseMapping() {

        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);

        TableRowEntity row1 = blocks.get(0).getTableRows().get(0);
        TableRowEntity row2 = blocks.get(1).getTableRows().get(0);
        TableRowEntity row3 = blocks.get(2).getTableRows().get(0);

        CoaDataEntity coaData1 = addCoaData(10, 80, true);
        CoaDataEntity coaData2 = addCoaData(20, 90, false);
        CoaDataEntity coaData3 = addCoaData(30, 70, true);

        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> multipleRequest = List.of(
                new ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                        row1.getTableRowPkId().getTableId(),
                        row1.getTableRowPkId().getRowId().intValue(),
                        new CoaDataDto.Create(coaData1.getCoaId(), coaData1.getCoaScore().intValue(),
                                coaData1.getUseCoa()), null
                ),
                new ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                        row2.getTableRowPkId().getTableId(),
                        row2.getTableRowPkId().getRowId().intValue(),
                        new CoaDataDto.Create(coaData2.getCoaId(), coaData2.getCoaScore().intValue(),
                                coaData2.getUseCoa()), null
                ),
                new ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                        row3.getTableRowPkId().getTableId(),
                        row3.getTableRowPkId().getRowId().intValue(),
                        new CoaDataDto.Create(coaData3.getCoaId(), coaData3.getCoaScore().intValue(),
                                coaData3.getUseCoa()), null
                )
        );

        ExtractedTableRowCoaDataJoinDto.Response[] response = given().contentType(ContentType.JSON)
                .body(multipleRequest)
                .when()
                .post()
                .then().statusCode(200)
                .extract()
                .body()
                .as(ExtractedTableRowCoaDataJoinDto.Response[].class);

        // Assert - verify all responses are returned
        assertNotNull(response);
        assertEquals(3, response.length);

        // Verify each response has correct data
        for (int i = 0; i < response.length; i++) {
            ExtractedTableRowCoaDataJoinDto.Response responseDto = response[i];
            assertNotNull(responseDto.coaData());
            assertNull(responseDto.explainability()); // Always null due to resource behavior
        }

        // Verify audit was called for create operations
        verify(exchangeService, times(1)).auditExtractedRowCoaDataCreate(any(), eq(false));
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.MAP_COA)
    @DisplayName("Should delete existing COA data mapping via POST")
    void testDeleteCoaDataMapping() {

        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        TableRowEntity tableRow = blocks.get(0).getTableRows().get(0);
        CoaDataEntity currentCoaData = addCoaData(2, 50, false);
        addCoaDataMapping(tableRow, currentCoaData.getId(), null);

        // This DTO represents a deletion request: coaData is effectively empty and explainability is null.
        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> deleteRequest = List.of(
                new ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                        tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId().intValue(),
                        new CoaDataDto.Create(1, 1, false), // Special values indicating "no mapping"
                        null
                )
        );

        ExtractedTableRowCoaDataJoinDto.Response[] response = given().contentType(ContentType.JSON)
                .body(deleteRequest)
                .when()
                .post()
                .then().statusCode(200)
                .extract()
                .body()
                .as(ExtractedTableRowCoaDataJoinDto.Response[].class);

        // Assert - verify response is empty for deletion
        assertNotNull(response);
        assertEquals(0, response.length);

        // Verify mapping was deleted from database
        Optional<ExtractedTableRowCoaDataJoinEntity> deletedEntity =
                extractedRowCoaDataRepository.findByRowOptional(tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId());
        assertFalse(deletedEntity.isPresent(), "Mapping should be deleted");

        // Verify audit was called for deletion
        verify(exchangeService, times(1)).auditExtractedRowCoaDataDelete(any(), eq(false));
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.MAP_COA)
    @DisplayName("Should handle mixed operations - create, update, and delete")
    void testMixedOperations() {

        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);

        TableRowEntity row1 = blocks.get(0).getTableRows().get(0); // Will be created
        TableRowEntity row2 = blocks.get(1).getTableRows().get(0); // Will be updated
        TableRowEntity row3 = blocks.get(2).getTableRows().get(0); // Will be deleted

        CoaDataEntity coaData1 = addCoaData(10, 50, true);
        CoaDataEntity coaData2 = addCoaData(20, 60, false);
        CoaDataEntity coaData3 = addCoaData(30, 70, true);

        // Pre-populate row2 and row3 with existing mappings
        addCoaDataMapping(row2, coaData2.getId(), null);
        addCoaDataMapping(row3, coaData3.getId(), null);

        // Create mixed operations request
        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> mixedRequest = List.of(
                // Create new mapping for row1
                new ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                        row1.getTableRowPkId().getTableId(),
                        row1.getTableRowPkId().getRowId().intValue(),
                        new CoaDataDto.Create(coaData1.getCoaId(), coaData1.getCoaScore().intValue(),
                                coaData1.getUseCoa()), null
                ),
                // Update existing mapping for row2
                new ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                        row2.getTableRowPkId().getTableId(),
                        row2.getTableRowPkId().getRowId().intValue(),
                        new CoaDataDto.Create(25, 80, true), // Different COA data
                        null
                ),
                // Delete mapping for row3
                new ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                        row3.getTableRowPkId().getTableId(),
                        row3.getTableRowPkId().getRowId().intValue(),
                        new CoaDataDto.Create(1, 1, false), // Deletion values
                        null
                )
        );

        ExtractedTableRowCoaDataJoinDto.Response[] response = given().contentType(ContentType.JSON)
                .body(mixedRequest)
                .when()
                .post()
                .then().statusCode(200)
                .extract()
                .body()
                .as(ExtractedTableRowCoaDataJoinDto.Response[].class);

        // Assert - verify response contains only non-deleted mappings
        assertNotNull(response);
        assertEquals(2, response.length); // row1 and row2, row3 should be deleted

        // Row1 should be created
        Optional<ExtractedTableRowCoaDataJoinEntity> row1Entity =
                extractedRowCoaDataRepository.findByRowOptional(row1.getTableRowPkId().getTableId(),
                        row1.getTableRowPkId().getRowId());
        assertTrue(row1Entity.isPresent());

        // Row2 should be updated
        Optional<ExtractedTableRowCoaDataJoinEntity> row2Entity =
                extractedRowCoaDataRepository.findByRowOptional(row2.getTableRowPkId().getTableId(),
                        row2.getTableRowPkId().getRowId());
        assertTrue(row2Entity.isPresent());

        // Row3 should be deleted
        Optional<ExtractedTableRowCoaDataJoinEntity> row3Entity =
                extractedRowCoaDataRepository.findByRowOptional(row3.getTableRowPkId().getTableId(),
                        row3.getTableRowPkId().getRowId());
        assertFalse(row3Entity.isPresent());

        // Verify all audit types were called
        verify(exchangeService, times(1)).auditExtractedRowCoaDataCreate(any(), eq(false));
        verify(exchangeService, times(1)).auditExtractedRowCoaDataUpdate(any(), eq(false));
        verify(exchangeService, times(1)).auditExtractedRowCoaDataDelete(any(), eq(false));
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.MAP_COA)
    @DisplayName("Should verify isApiKeyAuthenticated is always false")
    void testApiKeyAuthenticatedParameter() {

        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        TableRowEntity tableRow = blocks.get(0).getTableRows().get(0);
        CoaDataEntity coaData = addCoaData(2, 50, false);

        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> createRequest = List.of(
                new ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                        tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId().intValue(),
                        new CoaDataDto.Create(coaData.getCoaId(), coaData.getCoaScore().intValue(),
                                coaData.getUseCoa()), null
                )
        );

        given().contentType(ContentType.JSON)
                .body(createRequest)
                .when()
                .post()
                .then().statusCode(200);

        // Assert - verify audit was called with false (not API key authenticated)
        verify(exchangeService, times(1)).auditExtractedRowCoaDataCreate(any(), eq(false));
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.MAP_COA)
    @DisplayName("Should handle update with same COA data (no actual update)")
    void testUpdateWithSameCoaData() {

        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        TableRowEntity tableRow = blocks.get(0).getTableRows().get(0);
        CoaDataEntity coaData = addCoaData(2, 50, false);
        addCoaDataMapping(tableRow, coaData.getId(), null);

        // Update with same COA data
        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> updateRequest = List.of(
                new ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                        tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId().intValue(),
                        new CoaDataDto.Create(coaData.getCoaId(), coaData.getCoaScore().intValue(),
                                coaData.getUseCoa()), null
                )
        );

        ExtractedTableRowCoaDataJoinDto.Response[] response = given().contentType(ContentType.JSON)
                .body(updateRequest)
                .when()
                .post()
                .then().statusCode(200)
                .extract()
                .body()
                .as(ExtractedTableRowCoaDataJoinDto.Response[].class);

        // Assert - verify response is returned even for no-op update
        assertNotNull(response);
        assertEquals(1, response.length);
        assertEquals(coaData.getCoaId(), response[0].coaData().coaId());

        // Verify mapping still exists
        Optional<ExtractedTableRowCoaDataJoinEntity> entity =
                extractedRowCoaDataRepository.findByRowOptional(tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId());
        assertTrue(entity.isPresent());
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.MAP_COA)
    @DisplayName("Should handle deletion of mapping with explainability")
    void testDeleteMappingWithExplainability() {

        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        TableRowEntity tableRow = blocks.get(0).getTableRows().get(0);
        CoaDataEntity coaData = addCoaData(2, 50, false);
        CoaMappingEntity explainability = addCoaMapping();
        addCoaDataMapping(tableRow, coaData.getId(), explainability);

        // Delete request
        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> deleteRequest = List.of(
                new ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                        tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId().intValue(),
                        new CoaDataDto.Create(1, 1, false), // Deletion values
                        null
                )
        );

        ExtractedTableRowCoaDataJoinDto.Response[] response = given().contentType(ContentType.JSON)
                .body(deleteRequest)
                .when()
                .post()
                .then().statusCode(200)
                .extract()
                .body()
                .as(ExtractedTableRowCoaDataJoinDto.Response[].class);

        // Assert
        assertNotNull(response);
        assertEquals(0, response.length);

        Optional<ExtractedTableRowCoaDataJoinEntity> deletedEntity =
                extractedRowCoaDataRepository.findByRowOptional(tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId());
        assertFalse(deletedEntity.isPresent(), "Mapping with explainability should be deleted");

        // Verify audit was called for deletion
        verify(exchangeService, times(1)).auditExtractedRowCoaDataDelete(any(), eq(false));
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.MAP_COA)
    @DisplayName("Test that deleting a non-existent COA data mapping returns 400 Bad Request via API call")
    void testDeleteNonExistentCoaDataMappingReturnsBadRequest() {

        Integer nonExistentTableId = 9999; // Use a high ID to ensure it doesn't exist
        Integer nonExistentRowId = 9999;

        // Create a delete request DTO for a non-existent mapping
        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> deleteRequest = List.of(
                new ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                        nonExistentTableId,
                        nonExistentRowId,
                        new CoaDataDto.Create(1, 1, false),
                        // coaId is not relevant for deletion of non-existent entity
                        null
                )
        );

        given().contentType(ContentType.JSON)
                .body(deleteRequest)
                .when()
                .post()
                .then()
                .statusCode(500) // Expect Bad Request for InvalidParameterException
                .body(containsString(
                        "Row coa data mapping cannot be deleted due to missing row for table " + nonExistentTableId + " row " + nonExistentRowId));

        // Verify that audit was never called for a failed deletion
        verify(exchangeService, never()).auditExtractedRowCoaDataDelete(any(), anyBoolean());
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.MAP_COA)
    @DisplayName("Should skip operation when coaData is null")
    void testSkipOperationWithNullCoaData() {

        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        TableRowEntity tableRow = blocks.get(0).getTableRows().get(0);

        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> skipRequest = List.of(
                new ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                        tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId().intValue(),
                        null, // coaData is null, should be skipped
                        null
                )
        );

        ExtractedTableRowCoaDataJoinDto.Response[] response = given().contentType(ContentType.JSON)
                .body(skipRequest)
                .when()
                .post()
                .then().statusCode(200)
                .extract()
                .body()
                .as(ExtractedTableRowCoaDataJoinDto.Response[].class);

        // Assert
        assertNotNull(response);
        assertEquals(0, response.length);

        // Verify no audit calls were made
        verify(exchangeService, never()).auditExtractedRowCoaDataCreate(any(), any());
        verify(exchangeService, never()).auditExtractedRowCoaDataUpdate(any(), any());
        verify(exchangeService, never()).auditExtractedRowCoaDataDelete(any(), any());

        // Verify no mapping was created in the database
        Optional<ExtractedTableRowCoaDataJoinEntity> existingEntity =
                extractedRowCoaDataRepository.findByRowOptional(tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId().shortValue());
        assertFalse(existingEntity.isPresent(), "No mapping should be created for skipped operation");
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.MAP_COA)
    @DisplayName("Should skip operation when coaId is invalid (e.g., 0)")
    void testSkipOperationWithInvalidCoaId() {

        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        TableRowEntity tableRow = blocks.get(0).getTableRows().get(0);

        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> skipRequest = List.of(
                new ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                        tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId().intValue(),
                        new CoaDataDto.Create(0, 50, false), // coaId = 0, should be skipped
                        null
                )
        );

        ExtractedTableRowCoaDataJoinDto.Response[] response = given().contentType(ContentType.JSON)
                .body(skipRequest)
                .when()
                .post()
                .then().statusCode(200)
                .extract()
                .body()
                .as(ExtractedTableRowCoaDataJoinDto.Response[].class);

        // Assert
        assertNotNull(response);
        assertEquals(0, response.length);

        // Verify no audit calls were made
        verify(exchangeService, never()).auditExtractedRowCoaDataCreate(any(), any());
        verify(exchangeService, never()).auditExtractedRowCoaDataUpdate(any(), any());
        verify(exchangeService, never()).auditExtractedRowCoaDataDelete(any(), any());

        // Verify no mapping was created in the database
        Optional<ExtractedTableRowCoaDataJoinEntity> existingEntity =
                extractedRowCoaDataRepository.findByRowOptional(tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId().shortValue());
        assertFalse(existingEntity.isPresent(), "No mapping should be created for skipped operation");
    }
}
