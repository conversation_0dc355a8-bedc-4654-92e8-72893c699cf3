package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.extraction.entity.Bbox;
import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.entity.TableTagEntity;
import com.walnut.vegaspread.extraction.model.BlockTypeEnum;
import com.walnut.vegaspread.extraction.model.DbBlock;
import com.walnut.vegaspread.extraction.model.ResponseDto;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import com.walnut.vegaspread.extraction.repository.LayoutBlockRepository;
import com.walnut.vegaspread.extraction.repository.TableRowRepository;
import com.walnut.vegaspread.extraction.repository.TableTagRepository;
import com.walnut.vegaspread.extraction.service.ExchangeService;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.stream.IntStream;

import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.reset;

@QuarkusTest
@TestHTTPEndpoint(LayoutBlockResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class LayoutBlockResourceTest {
    @Inject
    LayoutBlockRepository layoutBlockRepository;
    @Inject
    TableRowRepository tableRowRepository;
    @Inject
    TableTagRepository tableTagRepository;
    @Inject
    Flyway flyway;
    @InjectMock
    ExchangeService exchangeService;
    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
        reset(exchangeService);
        TableTagEntity tableTagEntity = TableTagEntity.builder()
                .tag("NA")
                .createdBy("user")
                .createdTime(LocalDateTime.now())
                .lastModifiedBy("user")
                .lastModifiedTime(LocalDateTime.now())
                .build();
        tableTagRepository.persist(tableTagEntity);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
    }

    @Transactional
    List<LayoutBlockEntity> addBlocksForSingleDocId(UUID docId, int numBlocks) {
        List<TableTagEntity> tableTags = IntStream.range(0, numBlocks).mapToObj(id -> TableTagEntity.builder()
                .tag("tag" + id + "_" + docId)
                .createdBy("user")
                .createdTime(LocalDateTime.now())
                .lastModifiedBy("user")
                .lastModifiedTime(LocalDateTime.now())
                .build()).toList();
        tableTagRepository.persist(tableTags);
        List<LayoutBlockEntity> blocks = IntStream.range(0, numBlocks).mapToObj(id -> LayoutBlockEntity.builder()
                .docId(docId)
                .pageNum((short) (id % 3))
                .tag(tableTags.get(id))
                .blockType(BlockTypeEnum.values()[id % BlockTypeEnum.values().length])
                .score((byte) new Random().nextInt(100))
                .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                .tableHeaders(new ArrayList<>())
                .tagExplainabilityId(id)
                .build()).toList();
        layoutBlockRepository.persist(blocks);

        List<TableRowEntity> rowsForBlock0 = IntStream.range(1, numBlocks).mapToObj(id -> TableRowEntity.builder()
                .tableRowPkId(new TableRowPkId(blocks.get(0).getBlockId(), id))
                .layoutBlock(blocks.get(0))
                .ntaTable(blocks.get(id))
                .cellsText(List.of("row" + id, "row" + id))
                .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                .score((byte) new Random().nextInt(100))
                .comment("comment" + id)
                .parentText("parent" + id)
                .pos(id)
                .build()).toList();
        tableRowRepository.persist(rowsForBlock0);
        return blocks;
    }

    int getCoord() {
        return new Random().nextInt(1000);
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.LABEL_DATA)
    void testAddTablesFromUI() {
        List<DbBlock.CreateTableBlockDto> tableBlockDtos = IntStream.range(1, 3)
                .mapToObj(id -> new DbBlock.CreateTableBlockDto(getCoord(), getCoord(), getCoord(), getCoord()))
                .toList();
        UUID docId = UUID.randomUUID();

        given().contentType(ContentType.JSON).body(tableBlockDtos)
                .when().post("/{docId}/{pageNum}/add-table-from-ui", docId, 1)
                .then().statusCode(200);

        List<LayoutBlockEntity> dbBlocks = layoutBlockRepository.findAllByDocIdAndBlockTypeAndPageNum(docId,
                BlockTypeEnum.TABLE, 1);

        Assertions.assertEquals(tableBlockDtos.size(), dbBlocks.size());
        for (int i = 0; i < tableBlockDtos.size(); i++) {
            LayoutBlockEntity dbBlock = dbBlocks.get(i);
            DbBlock.CreateTableBlockDto tableBlockDto = tableBlockDtos.get(i);

            Assertions.assertEquals(docId, dbBlock.getDocId());
            Assertions.assertEquals(1, (int) dbBlock.getPageNum());
            Assertions.assertEquals(BlockTypeEnum.TABLE, dbBlock.getBlockType());
            Assertions.assertEquals(tableBlockDto.xMin(), (int) dbBlock.getBbox().getXMin());
            Assertions.assertEquals(tableBlockDto.xMax(), (int) dbBlock.getBbox().getXMax());
            Assertions.assertEquals(tableBlockDto.yMin(), (int) dbBlock.getBbox().getYMin());
            Assertions.assertEquals(tableBlockDto.yMax(), (int) dbBlock.getBbox().getYMax());
        }
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.TAG_TABLE)
    void testUpdateTags() {
        int numBlocks = 10;
        List<LayoutBlockEntity> blocks = addBlocksForSingleDocId(UUID.randomUUID(), numBlocks);

        List<String> newTags = IntStream.range(0, numBlocks).mapToObj(id -> "newTag" + id).toList();
        List<TableTagEntity> newTableTags = addNewTags(newTags);
        List<DbBlock.UpdateBlockTagDto> updateBlockTagDtos = IntStream.range(0, numBlocks)
                .mapToObj(id -> new DbBlock.UpdateBlockTagDto(blocks.get(id).getBlockId(),
                        id % 2 == 0 ? newTableTags.get(id).getId() : 1))
                .toList();

        given().contentType(ContentType.JSON).body(updateBlockTagDtos)
                .when().patch("/tags")
                .then().statusCode(200);

        List<LayoutBlockEntity> dbBlocks = layoutBlockRepository.listAll();
        Assertions.assertEquals(blocks.size(), dbBlocks.size());
        TableTagEntity naTag = tableTagRepository.findById(1);
        for (int i = 0; i < blocks.size(); i++) {
            LayoutBlockEntity dbBlock = dbBlocks.get(i);
            LayoutBlockEntity block = blocks.get(i);

            Assertions.assertEquals(block.getBlockId(), dbBlock.getBlockId());
            Assertions.assertEquals(i % 2 == 0 ? newTableTags.get(i).getId() : naTag.getId(),
                    dbBlock.getTag().getId());
        }
        Mockito.verify(exchangeService, Mockito.times(1)).auditUpdateBlocks(Mockito.any(), eq(Boolean.FALSE));
    }

    @Transactional
    public List<TableTagEntity> addNewTags(List<String> newTags) {
        List<TableTagEntity> tableTags = newTags.stream().map(tag -> TableTagEntity.builder()
                .tag(tag)
                .createdBy("user")
                .createdTime(LocalDateTime.now())
                .lastModifiedBy("user")
                .lastModifiedTime(LocalDateTime.now())
                .build()).toList();
        tableTagRepository.persist(tableTags);
        return tableTags;
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.MAP_COA)
    void testUpdateComment() {
        int numBlocks = 10;
        List<LayoutBlockEntity> blocks = addBlocksForSingleDocId(UUID.randomUUID(), numBlocks);
        List<String> newComments = IntStream.range(0, numBlocks).mapToObj(id -> "newComment" + id).toList();
        List<DbBlock.UpdateBlockCommentDto> updateBlockCommentDtos = IntStream.range(0, 10)
                .mapToObj(id -> new DbBlock.UpdateBlockCommentDto(blocks.get(id).getBlockId(), newComments.get(id)))
                .toList();

        given().contentType(ContentType.JSON).body(updateBlockCommentDtos)
                .when().patch("/comment")
                .then().statusCode(200);

        List<LayoutBlockEntity> dbBlocks = layoutBlockRepository.listAll();
        Assertions.assertEquals(blocks.size(), dbBlocks.size());

        for (int i = 0; i < blocks.size(); i++) {
            LayoutBlockEntity dbBlock = dbBlocks.get(i);
            LayoutBlockEntity block = blocks.get(i);
            String newComment = newComments.get(i);

            Assertions.assertEquals(block.getBlockId(), dbBlock.getBlockId());
            Assertions.assertEquals(newComment, dbBlock.getComment());
        }
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.LABEL_DATA)
    void testUpdateBbox() {
        Random rng = new Random();
        int numBlocks = 10;
        List<LayoutBlockEntity> blocks = addBlocksForSingleDocId(UUID.randomUUID(), numBlocks);
        LayoutBlockEntity block = blocks.get(rng.nextInt(blocks.size()));
        DbBlock.UpdateBlockBboxDto updateBlockBboxDto = new DbBlock.UpdateBlockBboxDto(getCoord(), getCoord(),
                getCoord(), getCoord());

        given().contentType(ContentType.JSON).queryParams("blockId", block.getBlockId())
                .body(updateBlockBboxDto)
                .when().patch("/bbox")
                .then().statusCode(200);

        LayoutBlockEntity dbBlock = layoutBlockRepository.findById(block.getBlockId());
        Bbox newBbox = new Bbox(updateBlockBboxDto.xMin(), updateBlockBboxDto.xMax(), updateBlockBboxDto.yMin(),
                updateBlockBboxDto.yMax());
        Assertions.assertEquals(block.getBlockId(), dbBlock.getBlockId());
        Assertions.assertEquals(newBbox.getXMin(), dbBlock.getBbox().getXMin());
        Assertions.assertEquals(newBbox.getXMax(), dbBlock.getBbox().getXMax());
        Assertions.assertEquals(newBbox.getYMin(), dbBlock.getBbox().getYMin());
        Assertions.assertEquals(newBbox.getYMax(), dbBlock.getBbox().getYMax());
    }

    @Test
    @TestSecurity(user = "user")
    void testListBlocksForDocId() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = addBlocksForSingleDocId(docId, 30);
        addBlocksForSingleDocId(UUID.randomUUID(), 10);
        List<LayoutBlockEntity> docBlocks = blocks.stream()
                .filter(block -> block.getDocId().equals(docId) && block.getBlockType().equals(BlockTypeEnum.TABLE))
                .toList();

        ResponseDto.LayoutBlock[] rsp = given().when().get("/doc/{docId}", docId)
                .then().statusCode(200)
                .extract().body().as(ResponseDto.LayoutBlock[].class);

        Assertions.assertEquals(docBlocks.size(), rsp.length);

        for (int i = 0; i < docBlocks.size(); i++) {
            LayoutBlockEntity dbBlock = docBlocks.get(i);
            ResponseDto.LayoutBlock block = rsp[i];

            Assertions.assertEquals(dbBlock.getBlockId(), block.blockId());
            Assertions.assertEquals(dbBlock.getDocId(), block.docId());
            Assertions.assertEquals(dbBlock.getPageNum(), block.pageNum());
            Assertions.assertEquals(dbBlock.getBlockType(), block.blockType());
            Assertions.assertEquals(dbBlock.getBbox().getXMin(), block.xMin());
            Assertions.assertEquals(dbBlock.getBbox().getXMax(), block.xMax());
            Assertions.assertEquals(dbBlock.getBbox().getYMin(), block.yMin());
            Assertions.assertEquals(dbBlock.getBbox().getYMax(), block.yMax());
            Assertions.assertEquals(dbBlock.getScore(), block.score());
            Assertions.assertEquals(dbBlock.getTag().getId(), block.tagId());
            Assertions.assertEquals(dbBlock.getComment(), block.comment());
        }
    }

    @Test
    @TestSecurity(user = "user")
    void testListBlocksForDocIdAndPageNum() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = addBlocksForSingleDocId(docId, 30);
        addBlocksForSingleDocId(UUID.randomUUID(), 10);
        List<LayoutBlockEntity> docBlocks = blocks.stream()
                .filter(block -> block.getDocId().equals(docId) && block.getBlockType().equals(
                        BlockTypeEnum.TABLE) && block.getPageNum() == 1)
                .toList();

        ResponseDto.LayoutBlock[] rsp = given().when().get("/doc/{docId}/page/{pageNum}", docId, 1)
                .then().statusCode(200)
                .extract().body().as(ResponseDto.LayoutBlock[].class);

        Assertions.assertEquals(docBlocks.size(), rsp.length);

        for (int i = 0; i < docBlocks.size(); i++) {
            LayoutBlockEntity dbBlock = docBlocks.get(i);
            ResponseDto.LayoutBlock block = rsp[i];

            Assertions.assertEquals(dbBlock.getBlockId(), block.blockId());
            Assertions.assertEquals(dbBlock.getDocId(), block.docId());
            Assertions.assertEquals(dbBlock.getPageNum(), block.pageNum());
            Assertions.assertEquals(dbBlock.getBlockType(), block.blockType());
            Assertions.assertEquals(dbBlock.getBbox().getXMin(), block.xMin());
            Assertions.assertEquals(dbBlock.getBbox().getXMax(), block.xMax());
            Assertions.assertEquals(dbBlock.getBbox().getYMin(), block.yMin());
            Assertions.assertEquals(dbBlock.getBbox().getYMax(), block.yMax());
            Assertions.assertEquals(dbBlock.getScore(), block.score());
            Assertions.assertEquals(dbBlock.getTag(), block.tag());
            Assertions.assertEquals(dbBlock.getComment(), block.comment());
        }
    }

    @Test
    @TestSecurity(user = "user")
    void testGetBlock() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = addBlocksForSingleDocId(docId, 30);
        LayoutBlockEntity block = blocks.get(0);

        ResponseDto.LayoutBlock dbBlock = given().when().get("/{blockId}", block.getBlockId())
                .then().statusCode(200)
                .extract().body().as(ResponseDto.LayoutBlock.class);

        Assertions.assertEquals(block.getBlockId(), dbBlock.blockId());

        Assertions.assertEquals(dbBlock.blockId(), block.getBlockId());
        Assertions.assertEquals(dbBlock.docId(), block.getDocId());
        Assertions.assertEquals(dbBlock.pageNum(), block.getPageNum());
        Assertions.assertEquals(dbBlock.blockType(), block.getBlockType());
        Assertions.assertEquals(dbBlock.xMin(), block.getBbox().getXMin());
        Assertions.assertEquals(dbBlock.xMax(), block.getBbox().getXMax());
        Assertions.assertEquals(dbBlock.yMin(), block.getBbox().getYMin());
        Assertions.assertEquals(dbBlock.yMax(), block.getBbox().getYMax());
        Assertions.assertEquals(dbBlock.score(), block.getScore());
        Assertions.assertEquals(dbBlock.tagId(), block.getTag().getId());
        Assertions.assertEquals(dbBlock.comment(), block.getComment());
        Assertions.assertEquals(dbBlock.tagExplainabilityId(), block.getTagExplainabilityId());
        Assertions.assertEquals(dbBlock.tagExplainabilityDocId(),
                block.getTagExplainabilityId() == 0 ? null : layoutBlockRepository.findById(
                                block.getTagExplainabilityId())
                        .getDocId());
        Assertions.assertEquals(dbBlock.tagExplainabilityPageNum().byteValue(),
                block.getTagExplainabilityId() == 0 ? 0 : layoutBlockRepository.findById(
                                block.getTagExplainabilityId())
                        .getPageNum());
    }

    @Test
    @TestSecurity(user = "user")
    void testGetBlockIdsForDoc() {
        UUID docId = UUID.randomUUID();
        int numBlocks = 10;
        List<LayoutBlockEntity> blocks = addBlocksForSingleDocId(docId, numBlocks);
        List<Integer> blockIds = blocks.stream().map(LayoutBlockEntity::getBlockId).toList();

        List<Integer> blockIdsForDoc = given().when()
                .get("/doc/{docId}/ids", docId)
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<>() {
                });

        Assertions.assertEquals(blockIds.size(), blockIdsForDoc.size());
        Assertions.assertEquals(blockIds, blockIdsForDoc);
    }

    @Test
    @TestSecurity(user = "user")
    void testGetTags() {
        UUID docId = UUID.randomUUID();
        int numBlocks = 10;
        List<LayoutBlockEntity> blocks = addBlocksForSingleDocId(docId, numBlocks);
        List<LayoutBlockEntity> tables = blocks.stream()
                .filter(block -> block.getBlockType() == BlockTypeEnum.TABLE)
                .toList();

        DbBlock.BlockTagOnly[] tags = given().when()
                .get("/doc/{docId}/tags", docId)
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(DbBlock.BlockTagOnly[].class);

        Assertions.assertEquals(tables.size(), tags.length);
        for (int i = 0; i < tags.length; i++) {
            DbBlock.BlockTagOnly tag = tags[i];
            LayoutBlockEntity dbBlock = tables.get(i);
            Assertions.assertEquals(dbBlock.getBlockId(), tag.getBlockId());
            Assertions.assertEquals(dbBlock.getTag().id, tag.getTagId());
        }
    }
}
