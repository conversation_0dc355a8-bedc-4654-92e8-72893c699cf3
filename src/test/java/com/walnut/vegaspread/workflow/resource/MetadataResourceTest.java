package com.walnut.vegaspread.workflow.resource;

import com.walnut.vegaspread.common.model.extraction.DenominationEnum;
import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.workflow.entity.Auditable;
import com.walnut.vegaspread.workflow.entity.EntityName;
import com.walnut.vegaspread.workflow.entity.Industry;
import com.walnut.vegaspread.workflow.entity.Region;
import com.walnut.vegaspread.workflow.repository.EntityNameRepository;
import com.walnut.vegaspread.workflow.repository.IndustryRepository;
import com.walnut.vegaspread.workflow.repository.RegionRepository;
import com.walnut.vegaspread.workflow.service.ExchangeService;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;

import java.util.Comparator;
import java.util.List;
import java.util.stream.IntStream;

import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;
import static org.mockito.Mockito.reset;

@QuarkusTest
@TestHTTPEndpoint(MetadataResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class MetadataResourceTest {

    @Inject
    Flyway flyway;
    @Inject
    EntityNameRepository entityNameRepository;
    @Inject
    IndustryRepository industryRepository;
    @Inject
    RegionRepository regionRepository;
    @InjectMock
    ExchangeService exchangeService;

    List<EntityName> entities;
    List<Industry> industries;
    List<Region> regions;

    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @BeforeEach
    void setUp() {
        addDbItems();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
        reset(exchangeService);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Transactional
    void addDbItems() {
        entities = IntStream.range(1, 11).mapToObj(id -> EntityName.builder()
                .name("Entity" + id)
                .auditable(new Auditable("Name" + id))
                .build()).sorted(Comparator.comparing(EntityName::getName)).toList();
        entityNameRepository.persist(entities);

        industries = IntStream.range(1, 11).mapToObj(id -> Industry.builder()
                .industryName("Industry" + id)
                .auditable(new Auditable("Name" + id))
                .build()).sorted(Comparator.comparing(Industry::getIndustryName)).toList();
        industryRepository.persist(industries);

        regions = IntStream.range(1, 11).mapToObj(id -> Region.builder()
                .regionName("Region" + id)
                .auditable(new Auditable("Name" + id))
                .build()).sorted(Comparator.comparing(Region::getRegionName)).toList();
        regionRepository.persist(regions);
    }

    @Test
    @TestSecurity(user = "Name0", roles = Roles.UPLOAD_FILE)
    void testGetEntities() {
        EntityName[] response = given().when().get("/entity")
                .then().statusCode(200)
                .extract().body().as(EntityName[].class);

        Assertions.assertEquals(entities.size(), response.length);
        for (int i = 0; i < entities.size(); i++) {
            Assertions.assertEquals(entities.get(i).getName(), response[i].getName());
        }

        Mockito.verify(exchangeService, Mockito.times(1)).getUsernameMapping(Mockito.any());
    }

    @Test
    @TestSecurity(user = "Name0", roles = Roles.UPLOAD_FILE)
    void testGetIndustries() {
        Industry[] response = given().when().get("/industry")
                .then().statusCode(200)
                .extract().body().as(Industry[].class);

        Assertions.assertEquals(industries.size(), response.length);
        for (int i = 0; i < industries.size(); i++) {
            Assertions.assertEquals(industries.get(i).getIndustryName(), response[i].getIndustryName());
        }

        Mockito.verify(exchangeService, Mockito.times(1)).getUsernameMapping(Mockito.any());
    }

    @Test
    @TestSecurity(user = "Name0", roles = Roles.UPLOAD_FILE)
    void testGetRegion() {
        Region[] response = given().when().get("/region")
                .then().statusCode(200)
                .extract().body().as(Region[].class);

        Assertions.assertEquals(entities.size(), response.length);
        for (int i = 0; i < regions.size(); i++) {
            Assertions.assertEquals(regions.get(i).getRegionName(), response[i].getRegionName());
        }

        Mockito.verify(exchangeService, Mockito.times(1)).getUsernameMapping(Mockito.any());
    }

    @Test
    @TestSecurity(user = "Name0", roles = Roles.UPLOAD_FILE)
    void testGetDenominations() {
        DenominationEnum[] response = given().when().get("/denomination")
                .then().statusCode(200)
                .extract().body().as(DenominationEnum[].class);

        Assertions.assertEquals(DenominationEnum.values().length, response.length);
        for (int i = 0; i < DenominationEnum.values().length; i++) {
            Assertions.assertEquals(DenominationEnum.values()[i], response[i]);
        }
    }
}
