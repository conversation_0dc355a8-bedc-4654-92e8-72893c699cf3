package com.walnut.vegaspread.workflow.resource;

import com.walnut.vegaspread.common.model.extraction.DenominationEnum;
import com.walnut.vegaspread.common.model.workflow.StatusEnum;
import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.workflow.entity.Auditable;
import com.walnut.vegaspread.workflow.entity.Document;
import com.walnut.vegaspread.workflow.entity.EntityName;
import com.walnut.vegaspread.workflow.entity.Industry;
import com.walnut.vegaspread.workflow.entity.Region;
import com.walnut.vegaspread.workflow.entity.SpreadingTask;
import com.walnut.vegaspread.workflow.model.CreateTaskDto;
import com.walnut.vegaspread.workflow.model.DocForSpread;
import com.walnut.vegaspread.workflow.model.SpreadLevelEnum;
import com.walnut.vegaspread.workflow.model.SpreadOutputDto;
import com.walnut.vegaspread.workflow.repository.DocumentRepository;
import com.walnut.vegaspread.workflow.repository.EntityNameRepository;
import com.walnut.vegaspread.workflow.repository.IndustryRepository;
import com.walnut.vegaspread.workflow.repository.RegionRepository;
import com.walnut.vegaspread.workflow.repository.SpreadingTaskRepository;
import com.walnut.vegaspread.workflow.service.ExchangeService;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;

import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static com.walnut.vegaspread.workflow.utils.Utils.getCurrentReviewer;
import static com.walnut.vegaspread.workflow.utils.Utils.isRevised;
import static io.restassured.RestAssured.given;
import static org.mockito.Mockito.reset;

@QuarkusTest
@TestHTTPEndpoint(SpreadingTaskResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class SpreadingTaskResourceTest {

    @Inject
    Flyway flyway;
    @Inject
    EntityNameRepository entityNameRepository;
    @Inject
    IndustryRepository industryRepository;
    @Inject
    RegionRepository regionRepository;
    @Inject
    SpreadingTaskRepository spreadingTaskRepository;
    @Inject
    DocumentRepository documentRepository;
    @InjectMock
    ExchangeService exchangeService;
    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
        reset(exchangeService);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Transactional
    SpreadingTask createSpread() {
        int id = 1;
        EntityName entityName = EntityName.builder()
                .name("Entity" + id)
                .auditable(new Auditable("Name" + id))
                .build();
        entityNameRepository.persist(entityName);

        Industry industry = Industry.builder()
                .industryName("Industry" + id)
                .auditable(new Auditable("Name" + id))
                .build();
        industryRepository.persist(industry);

        Region region = Region.builder()
                .regionName("Region" + id)
                .auditable(new Auditable("Name" + id))
                .build();
        regionRepository.persist(region);

        SpreadingTask spread = SpreadingTask.builder()
                .clientName("Client1")
                .entityName(entityName)
                .industry(industry)
                .region(region)
                .auditable(new Auditable("Name" + id))
                .lastModifiedBy("Name" + (id + 1))
                .lastModifiedTime(LocalDateTime.now())
                .build();
        spreadingTaskRepository.persist(spread);

        return spread;
    }

    @Transactional
    Document createDoc(SpreadingTask task) {
        int id = 1;
        Document document = Document.builder()
                .period(LocalDate.of(2024, 12, id))
                .spreadLevel(SpreadLevelEnum.CONSOLIDATED)
                .fileName("Document" + id)
                .filePath("folder/Document" + id)
                .fileSize(1024 * (id + 1))
                .status(StatusEnum.DRAFT)
                .statusText("Status Text")
                .auditable(new Auditable("Name" + id))
                .isDigital(Boolean.TRUE)
                .lastModifiedBy("Name" + id)
                .lastModifiedTime(LocalDateTime.now())
                .build();
        if (task != null) {
            document.setSpreadingTask(task);
        }
        documentRepository.persist(document);

        return document;
    }

    @Test
    @TestSecurity(user = "Name0", roles = Roles.UPLOAD_FILE)
    void testCreateSpread() {
        Document document = createDoc(null);
        CreateTaskDto taskDto = new CreateTaskDto(LocalDate.now(), SpreadLevelEnum.STANDALONE,
                new CreateTaskDto.Entity(null, "Entity2"), new CreateTaskDto.Industry(null, "Industry2"),
                new CreateTaskDto.Region(null, "Region2"), Boolean.TRUE, document.docId, DenominationEnum.NONE,
                DenominationEnum.THOUSAND);

        given().contentType(ContentType.JSON).body(taskDto).when().post("").then().statusCode(200);

        SpreadingTask dbTask = spreadingTaskRepository.listAll().get(0);
        Document firstDoc = dbTask.getDocuments().get(0);
        Assertions.assertEquals(1, dbTask.getSpreadId());
        Assertions.assertEquals(taskDto.period(), firstDoc.getPeriod());
        Assertions.assertEquals(taskDto.spreadLevel(), firstDoc.getSpreadLevel());
        Assertions.assertEquals(taskDto.entityName().name(), dbTask.getEntityName().getName());
        Assertions.assertEquals(taskDto.industry().name(), dbTask.getIndustry().getIndustryName());
        Assertions.assertEquals(taskDto.region().name(), dbTask.getRegion().getRegionName());
        Assertions.assertEquals(taskDto.isDigital(), firstDoc.getIsDigital());
        Assertions.assertEquals(taskDto.fileDenomination(), firstDoc.getFileDenomination());
        Assertions.assertEquals(taskDto.outputDenomination(), firstDoc.getOutputDenomination());
    }

    private void assertEqual(Document dbDoc, DocForSpread outputDoc) {
        Assertions.assertEquals(dbDoc.getPeriod(), outputDoc.period());
        Assertions.assertEquals(dbDoc.getSpreadLevel(), outputDoc.spreadLevel());
        Assertions.assertEquals(Optional.ofNullable(dbDoc.getDpi()).orElse((short) 0),
                outputDoc.dpi().shortValue());
        Assertions.assertEquals(0, outputDoc.extractedItems());
        Assertions.assertEquals(Optional.ofNullable(dbDoc.getMappedItems()).orElse((short) 0),
                outputDoc.mappedItems().shortValue());
        Assertions.assertEquals(dbDoc.getStatus(), outputDoc.status());
        Assertions.assertEquals(dbDoc.getStatusText(), outputDoc.statusText());
        Assertions.assertEquals(dbDoc.getDocId(), outputDoc.docId());
        Assertions.assertEquals(dbDoc.getFileName(), outputDoc.docFileName());
        Assertions.assertEquals(dbDoc.getFileSize(), outputDoc.docFileSize());
        Assertions.assertEquals(dbDoc.getAuditable().getCreatedBy(), outputDoc.createdBy());
        Assertions.assertEquals(dbDoc.getIsDigital() ? "Digital" : "Scanned", outputDoc.documentType());
        Assertions.assertEquals(Optional.ofNullable(dbDoc.getOcrScore()).orElse((byte) 100),
                outputDoc.ocrScore().byteValue());
        Assertions.assertEquals(dbDoc.getAuditable().getCreatedTime().toLocalDate(),
                outputDoc.createdTime().toLocalDate());
        Assertions.assertEquals(dbDoc.getLastModifiedBy(), outputDoc.lastModifiedBy());
        Assertions.assertEquals(dbDoc.getLastModifiedTime().toLocalDate(), outputDoc.lastModifiedTime().toLocalDate());
        Assertions.assertEquals(getCurrentReviewer(dbDoc), outputDoc.currentReviewer());
        Assertions.assertEquals(Optional.ofNullable(dbDoc.getFileDenomination()).orElse(DenominationEnum.NONE),
                outputDoc.fileDenomination());
        Assertions.assertEquals(Optional.ofNullable(dbDoc.getOutputDenomination()).orElse(DenominationEnum.NONE),
                outputDoc.outputDenomination());
        Assertions.assertEquals(isRevised(dbDoc), outputDoc.isRevised());
    }

    @Test
    @TestSecurity(user = "Name0")
    void testGetSpread() {
        SpreadingTask newTask = createSpread();
        createDoc(newTask);
        SpreadOutputDto output = given().when()
                .get("/{spreadId}", newTask.getSpreadId())
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(SpreadOutputDto.class);

        SpreadingTask task = spreadingTaskRepository.findById(newTask.getSpreadId());
        Assertions.assertEquals(task.getSpreadId(), output.spreadId());
        Assertions.assertEquals(task.getEntityName().getName(), output.entityName());
        Assertions.assertEquals(task.getEntityName().getEntityId(), output.entityId());
        Assertions.assertEquals(task.getIndustry().getIndustryName(), output.industryName());
        Assertions.assertEquals(task.getIndustry().getIndustryId(), output.industryId());
        Assertions.assertEquals(task.getRegion().getRegionName(), output.regionName());
        Assertions.assertEquals(task.getRegion().getRegionId(), output.regionId());

        Assertions.assertEquals(task.documents.size(), output.docs().size());
        for (int i = 0; i < output.docs().size(); i++) {
            Document dbDoc = task.documents.get(i);
            DocForSpread outputDoc = output.docs().get(i);
            assertEqual(dbDoc, outputDoc);
        }

        Mockito.verify(exchangeService, Mockito.times(1)).getUsernameMappingForDocs(Mockito.any());
    }
}
