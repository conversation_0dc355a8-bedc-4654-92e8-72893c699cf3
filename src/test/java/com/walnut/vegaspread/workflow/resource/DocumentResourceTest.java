package com.walnut.vegaspread.workflow.resource;

import com.walnut.vegaspread.common.model.extraction.DenominationEnum;
import com.walnut.vegaspread.common.model.extraction.UrlDto;
import com.walnut.vegaspread.common.model.workflow.StatusEnum;
import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.workflow.cloud.GcpCloudProvider;
import com.walnut.vegaspread.workflow.entity.Auditable;
import com.walnut.vegaspread.workflow.entity.Document;
import com.walnut.vegaspread.workflow.entity.EntityName;
import com.walnut.vegaspread.workflow.entity.Industry;
import com.walnut.vegaspread.workflow.entity.Region;
import com.walnut.vegaspread.workflow.entity.SpreadingTask;
import com.walnut.vegaspread.workflow.model.DocOutputDto;
import com.walnut.vegaspread.workflow.model.ListTaskDto;
import com.walnut.vegaspread.workflow.model.MetadataDto;
import com.walnut.vegaspread.workflow.model.SpreadLevelEnum;
import com.walnut.vegaspread.workflow.model.TaskListResponseDto;
import com.walnut.vegaspread.workflow.model.UpdateUiDocDto;
import com.walnut.vegaspread.workflow.repository.DocumentRepository;
import com.walnut.vegaspread.workflow.repository.EntityNameRepository;
import com.walnut.vegaspread.workflow.repository.IndustryRepository;
import com.walnut.vegaspread.workflow.repository.RegionRepository;
import com.walnut.vegaspread.workflow.repository.SpreadingTaskRepository;
import com.walnut.vegaspread.workflow.service.ExchangeService;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.quarkus.test.security.oidc.Claim;
import io.quarkus.test.security.oidc.OidcSecurity;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.apache.commons.lang3.StringUtils;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.IntStream;

import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static com.walnut.vegaspread.workflow.utils.Utils.getCurrentReviewer;
import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.containsString;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@QuarkusTest
@TestHTTPEndpoint(DocumentResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class DocumentResourceTest {
    private static final String CLIENT1 = "Client1";
    private static final String CLIENT_KEY = "client_name";
    @Inject
    Flyway flyway;
    @InjectMock
    GcpCloudProvider gcsService;
    @InjectMock
    ExchangeService exchangeService;
    @Inject
    EntityNameRepository entityNameRepository;
    @Inject
    IndustryRepository industryRepository;
    @Inject
    RegionRepository regionRepository;
    @Inject
    SpreadingTaskRepository spreadingTaskRepository;
    @Inject
    DocumentRepository documentRepository;
    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
        reset(exchangeService);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Transactional
    List<Document> createDocs() {
        List<EntityName> entityNames = IntStream.range(1, 11).mapToObj(id -> EntityName.builder()
                .name("Entity" + id)
                .auditable(new Auditable("Name" + id))
                .build()).toList();
        entityNameRepository.persist(entityNames);

        List<Industry> industries = IntStream.range(1, 11).mapToObj(id -> Industry.builder()
                .industryName("Industry" + id)
                .auditable(new Auditable("Industry" + id))
                .build()).toList();
        industryRepository.persist(industries);

        List<Region> regions = IntStream.range(1, 11).mapToObj(id -> Region.builder()
                .regionName("Region" + id)
                .auditable(new Auditable("Region" + id))
                .build()).toList();
        regionRepository.persist(regions);

        List<SpreadingTask> spreads = IntStream.range(0, 10).mapToObj(id -> SpreadingTask.builder()
                .clientName(id == 0 ? "Client0" : CLIENT1)
                .entityName(entityNames.get(id))
                .industry(industries.get(id))
                .region(regions.get(id))
                .auditable(new Auditable("Name" + id))
                .lastModifiedBy("Name" + (id + 1))
                .lastModifiedTime(LocalDateTime.now())
                .build()).toList();
        spreadingTaskRepository.persist(spreads);

        List<Document> documents = IntStream.range(1, 11).mapToObj(id -> Document.builder()
                .period(LocalDate.of(2024, 12, id))
                .spreadLevel(id % 2 == 0 ? SpreadLevelEnum.CONSOLIDATED : SpreadLevelEnum.STANDALONE)
                .fileName("Document" + id)
                .filePath("folder/Document" + id)
                .fileSize(1024 * (id + 1))
                .status(id % 2 == 0 ? StatusEnum.PROCESSING : StatusEnum.CREATED)
                .statusText("Status Text")
                .auditable(new Auditable("Name" + id, "Name" + id, LocalDate.of(2024, 1, id).atStartOfDay()))
                .isDigital(id % 3 == 0 ? Boolean.TRUE : Boolean.FALSE)
                .lastModifiedBy("Name" + id)
                .lastModifiedTime(LocalDate.of(2024, 1, id).atStartOfDay())
                .spreadingTask(spreads.get(id - 1))
                .build()).toList();
        documentRepository.persist(documents);

        return documents;
    }

    private File createTempFileWithSize(long sizeInKb) throws IOException {

        File tempFile = File.createTempFile("prefix-", "-suffix");

        try (FileOutputStream out = new FileOutputStream(tempFile)) {
            byte[] buffer = new byte[1024]; // 1 KB buffer
            for (long i = 0; i < sizeInKb; i++) {
                out.write(buffer);
            }
        }

        return tempFile;
    }

    @Transactional
    Document updateDocStatus(UUID docId, StatusEnum status) {
        Document doc = documentRepository.findById(docId);
        doc.setStatus(status);
        doc.setStatusText(status.toString());
        documentRepository.persist(doc);
        return doc;
    }

    @Test
    @TestSecurity(user = "Name0", roles = Roles.UPLOAD_FILE)
    void testUpload() throws IOException {
        File file = createTempFileWithSize(101);

        DocOutputDto output = given().contentType(ContentType.MULTIPART).multiPart("file", file)
                .when().put("/upload")
                .then().statusCode(200)
                .extract().body().as(DocOutputDto.class);

        Assertions.assertNotNull(output.docId());

        Document dbDocument = documentRepository.findById(output.docId());
        String gcsPath = String.format("%s/%s.pdf", LocalDate.now(), output.docId());
        Assertions.assertNotNull(dbDocument);
        Assertions.assertEquals(file.length() / 1024, (long) dbDocument.fileSize);
        Assertions.assertEquals("Name0", dbDocument.auditable.createdBy);
        Assertions.assertEquals("Name0", dbDocument.lastModifiedBy);
        Assertions.assertEquals(StatusEnum.DRAFT, dbDocument.status);
        Assertions.assertEquals(StatusEnum.DRAFT.toString(), dbDocument.statusText);
        Assertions.assertEquals(file.getName(), dbDocument.fileName);
        Assertions.assertEquals(gcsPath, dbDocument.filePath);
        Assertions.assertNull(dbDocument.spreadingTask);

        verify(gcsService, times(1)).upload(eq(gcsPath), any(Path.class));
        Mockito.verify(exchangeService, Mockito.times(1)).createDocAudit(Mockito.any(), eq(false));
    }

    @Test
    @TestSecurity(user = "Name0", roles = Roles.UPLOAD_FILE)
    void testUploadWithSpreadIdGreaterThanZero() throws IOException {
        createDocs();

        File file = createTempFileWithSize(101);
        Integer spreadId = 1;
        DocOutputDto output = given().contentType(ContentType.MULTIPART)
                .queryParam("spreadId", spreadId)
                .multiPart("file", file)
                .when()
                .put("/upload")
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(DocOutputDto.class);

        Assertions.assertNotNull(output.docId());
        Document dbDocument = documentRepository.findById(output.docId());
        String gcsPath = String.format("%s/%s.pdf", LocalDate.now(), output.docId());
        Assertions.assertNotNull(dbDocument);
        Assertions.assertEquals(file.length() / 1024, (long) dbDocument.fileSize);
        Assertions.assertEquals("Name0", dbDocument.auditable.createdBy);
        Assertions.assertEquals("Name0", dbDocument.lastModifiedBy);
        Assertions.assertEquals(StatusEnum.DRAFT, dbDocument.status);
        Assertions.assertEquals(StatusEnum.DRAFT.toString(), dbDocument.statusText);
        Assertions.assertEquals(file.getName(), dbDocument.fileName);
        Assertions.assertEquals(gcsPath, dbDocument.filePath);
        Assertions.assertEquals(spreadId, dbDocument.spreadingTask.spreadId);

        verify(gcsService, times(1)).upload(eq(gcsPath), any(Path.class));
        Mockito.verify(exchangeService, Mockito.times(1)).createDocAudit(Mockito.any(), eq(false));
    }

    @Test
    @TestSecurity(user = "Name0", roles = Roles.UPLOAD_FILE)
    void testUploadWithDocSizeLessThanMin() throws IOException {
        File file = createTempFileWithSize(10);

        given().contentType(ContentType.MULTIPART).multiPart("file", file)
                .when().put("/upload")
                .then().statusCode(400);
    }

    @Test
    @TestSecurity(user = "Name0")
    void testUpdateTime() {
        List<Document> docs = createDocs();
        Document doc = docs.get(0);

        given().when().patch("/{docId}/time", doc.docId).then().statusCode(200);

        Document updatedDoc = documentRepository.findById(doc.docId);

        Assertions.assertNotEquals(doc.lastModifiedTime, updatedDoc.lastModifiedTime);
        Assertions.assertEquals("Name0", updatedDoc.lastModifiedBy);
        Assertions.assertNotEquals(doc.spreadingTask.lastModifiedTime, updatedDoc.spreadingTask.lastModifiedTime);
        Assertions.assertEquals("Name0", updatedDoc.spreadingTask.lastModifiedBy);
        Mockito.verify(exchangeService, Mockito.times(1)).updateDocAudit(Mockito.any());
    }

    @Test
    @TestSecurity(user = "Name0", roles = Roles.MAP_COA)
    void testUpdateDocFromUi() {
        List<Document> docs = createDocs();
        Document doc = docs.get(0);
        UpdateUiDocDto docDto = new UpdateUiDocDto(LocalDate.now().minusDays(10),
                SpreadLevelEnum.CONSOLIDATED, true, false, StringUtils.EMPTY, DenominationEnum.NONE,
                DenominationEnum.NONE, new MetadataDto.Entity(null, "entity1"),
                new MetadataDto.Industry(null, "industry1"), new MetadataDto.Region(null, "region1"));

        given().contentType(ContentType.JSON)
                .body(docDto)
                .when()
                .patch("/{docId}/ui", doc.getDocId())
                .then()
                .statusCode(200);

        Document updatedDoc = documentRepository.findById(doc.getDocId());
        Assertions.assertEquals(docDto.period(), updatedDoc.getPeriod());
        Assertions.assertEquals(docDto.spreadLevel(), updatedDoc.getSpreadLevel());
        Assertions.assertEquals(docDto.isDigital(), updatedDoc.getIsDigital());
        Assertions.assertEquals(doc.getIsOutlier(), updatedDoc.getIsOutlier());
        Assertions.assertEquals(doc.getOutlierComment(), updatedDoc.getOutlierComment());
        Assertions.assertEquals(docDto.fileDenomination(), updatedDoc.getFileDenomination());
        Assertions.assertEquals(docDto.outputDenomination(), updatedDoc.getOutputDenomination());
        Assertions.assertEquals("Name0", updatedDoc.getLastModifiedBy());
        Assertions.assertEquals("Name0", updatedDoc.getSpreadingTask().getLastModifiedBy());

        Mockito.verify(exchangeService, Mockito.times(1)).updateDocAudit(Mockito.any());
    }

    @Test
    @TestSecurity(user = "Name0")
    void testGet() {
        List<Document> docs = createDocs();
        Document doc = docs.get(0);

        DocOutputDto output = given().when().get("/{docId}", doc.docId)
                .then().statusCode(200)
                .extract().body().as(DocOutputDto.class);

        Assertions.assertEquals(doc.getSpreadingTask().getSpreadId(), output.spreadId());
        Assertions.assertEquals(doc.getPeriod(), output.period());
        Assertions.assertEquals(doc.getSpreadLevel(), output.spreadLevel());
        Assertions.assertEquals(doc.getDpi() == null ? 0 : doc.getDpi(), output.dpi().shortValue());
        Assertions.assertEquals(0, output.extractedItems());
        Assertions.assertEquals(Optional.ofNullable(doc.getMappedItems()).orElse((short) 0),
                output.mappedItems().shortValue());
        Assertions.assertEquals(doc.getStatus(), output.status());
        Assertions.assertEquals(doc.getStatusText(), output.statusText());
        Assertions.assertEquals(doc.getSpreadingTask().getEntityName().getName(), output.entityName());
        Assertions.assertEquals(doc.getSpreadingTask().getIndustry().getIndustryName(), output.industryName());
        Assertions.assertEquals(doc.getSpreadingTask().getRegion().getRegionName(), output.regionName());
        Assertions.assertEquals(doc.getDocId(), output.docId());
        Assertions.assertEquals(doc.getFileName(), output.docFileName());
        Assertions.assertEquals(doc.getFileSize(), output.docFileSize());
        Assertions.assertEquals(doc.getAuditable().getCreatedBy(), output.createdBy());
        Assertions.assertEquals(doc.getIsDigital() ? "Digital" : "Scanned", output.documentType());
        Assertions.assertEquals(Optional.ofNullable(doc.getOcrScore()).orElse((byte) 100),
                output.ocrScore().byteValue());
        Assertions.assertEquals(doc.getAuditable().getCreatedTime().toLocalDate(), output.createdTime().toLocalDate());
        Assertions.assertEquals(doc.getLastModifiedBy(), output.lastModifiedBy());
        Assertions.assertEquals(doc.getLastModifiedTime().toLocalDate(), output.lastModifiedTime().toLocalDate());
        Assertions.assertEquals(doc.getSpreadingTask().getClientName(), output.clientName());
        Assertions.assertEquals(getCurrentReviewer(doc), output.currentReviewer());
        Assertions.assertEquals(Optional.ofNullable(doc.getFileDenomination()).orElse(DenominationEnum.NONE),
                output.fileDenomination());
        Assertions.assertEquals(Optional.ofNullable(doc.getOutputDenomination()).orElse(DenominationEnum.NONE),
                output.outputDenomination());
    }

    @Test
    @TestSecurity(user = "Name0")
    void testGetLink() throws MalformedURLException {
        List<Document> docs = createDocs();
        Document doc = docs.get(0);

        String urlStr = "http://localhost:8080";
        UrlDto urlDto = new UrlDto(new URL(urlStr));
        when(gcsService.getLink(any(String.class), any(Integer.class))).thenReturn(new URL(urlStr));

        UrlDto docUrl = given().when()
                .get("/link/{docId}", doc.docId)
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(UrlDto.class);

        Assertions.assertEquals(urlDto.url(), docUrl.url());
        verify(gcsService, times(1)).getLink(doc.filePath, 30);
    }

    @Test
    @TestSecurity(user = "Name0")
    void testDelete() {
        List<Document> docs = createDocs();
        Document doc = docs.get(0);

        doc = updateDocStatus(doc.docId, StatusEnum.UNDER_REVIEW_LVL_1);

        long deleteCount = given().when()
                .delete("/{docId}", doc.docId)
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(Long.class);

        verify(gcsService, times(1)).delete(doc.filePath, false);
        Assertions.assertEquals(1, deleteCount);
    }

    @Test
    @TestSecurity(user = "Name0")
    void testDeleteForInvalidDocId() {

        UUID docId = UUID.fromString("11111111-**************-************");
        given().when()
                .delete("/{docId}", docId)
                .then()
                .statusCode(404)
                .body(containsString("Document not found for id " + docId));
    }

    @Test
    @TestSecurity(user = "Name0")
    @OidcSecurity(claims = {@Claim(key = CLIENT_KEY, value = CLIENT1)})
    void testList_filterByStatus() {
        List<Document> docs = createDocs();

        ListTaskDto.GetTaskList filterByStatus = new ListTaskDto.GetTaskList(1, 5, List.of(),
                List.of(), "", List.of(StatusEnum.CREATED));
        TaskListResponseDto statusResponse = given().contentType(ContentType.JSON)
                .body(filterByStatus)
                .when()
                .post("/list")
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(TaskListResponseDto.class);

        List<Document> dbDocs = docs.stream().filter(doc -> doc.status == StatusEnum.CREATED
                && Objects.equals(doc.spreadingTask.clientName, CLIENT1)).toList();
        Assertions.assertEquals(dbDocs.size(), statusResponse.tasks().size());
    }

    @Test
    @TestSecurity(user = "Name0")
    @OidcSecurity(claims = {@Claim(key = CLIENT_KEY, value = CLIENT1)})
    void testList_sort() {
        List<Document> docs = createDocs();
        ListTaskDto.GetTaskList sortBySpreadId = new ListTaskDto.GetTaskList(1, 10, List.of(),
                List.of(new ListTaskDto.SortDto("spreadId", "DESC")), "",
                List.of(StatusEnum.CREATED, StatusEnum.PROCESSING));
        TaskListResponseDto sortResponse = given().contentType(ContentType.JSON).body(sortBySpreadId)
                .when().post("/list")
                .then().statusCode(200)
                .extract().body().as(TaskListResponseDto.class);
        Assertions.assertEquals(9, sortResponse.tasks().size());
        Assertions.assertEquals(docs.get(9).docId, sortResponse.tasks().get(0).docId());
    }

    @Test
    @TestSecurity(user = "Name0")
    @OidcSecurity(claims = {@Claim(key = CLIENT_KEY, value = CLIENT1)})
    void testList_filterByDirectFields() {
        List<Document> docs = createDocs();
        List<Document> actualDocs;

        ListTaskDto.GetTaskList filterBySpreadLevel = new ListTaskDto.GetTaskList(1, 10,
                List.of(new ListTaskDto.FilterDto("spreadLevel", "CONSOLIDATED")),
                List.of(), "", List.of(StatusEnum.CREATED, StatusEnum.PROCESSING));
        TaskListResponseDto spreadLevelResponse = given().contentType(ContentType.JSON).body(filterBySpreadLevel)
                .when().post("/list")
                .then().statusCode(200)
                .extract().body().as(TaskListResponseDto.class);
        actualDocs = docs.stream().filter(doc -> doc.spreadLevel == SpreadLevelEnum.CONSOLIDATED
                && Objects.equals(doc.spreadingTask.clientName, CLIENT1)).toList();
        Assertions.assertEquals(actualDocs.size(), spreadLevelResponse.tasks().size());

        ListTaskDto.GetTaskList filterByCreatedBy = new ListTaskDto.GetTaskList(1, 10,
                List.of(new ListTaskDto.FilterDto("createdBy", "Name4")),
                List.of(), "", List.of(StatusEnum.CREATED, StatusEnum.PROCESSING));
        TaskListResponseDto filterByCreatedResponse = given().contentType(ContentType.JSON).body(filterByCreatedBy)
                .when().post("/list")
                .then().statusCode(200)
                .extract().body().as(TaskListResponseDto.class);
        actualDocs = docs.stream().filter(doc -> Objects.equals(doc.auditable.createdBy, "Name4")
                && Objects.equals(doc.spreadingTask.clientName, CLIENT1)).toList();
        Assertions.assertEquals(actualDocs.size(), filterByCreatedResponse.tasks().size());
        Assertions.assertEquals(actualDocs.get(0).spreadingTask.spreadId,
                filterByCreatedResponse.tasks().get(0).spreadId());

        ListTaskDto.GetTaskList filterByLastModifiedBy = new ListTaskDto.GetTaskList(1, 10,
                List.of(new ListTaskDto.FilterDto("lastModifiedBy", "Name6")),
                List.of(), "", List.of(StatusEnum.CREATED, StatusEnum.PROCESSING));
        TaskListResponseDto filterByLastModifiedResponse = given().contentType(ContentType.JSON)
                .body(filterByLastModifiedBy)
                .when()
                .post("/list")
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(TaskListResponseDto.class);
        actualDocs = docs.stream().filter(doc -> Objects.equals(doc.lastModifiedBy, "Name6")
                && Objects.equals(doc.spreadingTask.clientName, CLIENT1)).toList();
        Assertions.assertEquals(actualDocs.size(), filterByLastModifiedResponse.tasks().size());
        Assertions.assertEquals(actualDocs.get(0).spreadingTask.spreadId,
                filterByLastModifiedResponse.tasks().get(0).spreadId());

        ListTaskDto.GetTaskList filterByDocumentType = new ListTaskDto.GetTaskList(1, 10,
                List.of(new ListTaskDto.FilterDto("documentType", "Digital")),
                List.of(), "", List.of(StatusEnum.CREATED, StatusEnum.PROCESSING));
        TaskListResponseDto filterByDocumentTypeResponse = given().contentType(ContentType.JSON)
                .body(filterByDocumentType)
                .when()
                .post("/list")
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(TaskListResponseDto.class);
        actualDocs = docs.stream().filter(doc -> Boolean.TRUE.equals(doc.isDigital)
                && Objects.equals(doc.spreadingTask.clientName, CLIENT1)).toList();
        Assertions.assertEquals(actualDocs.size(), filterByDocumentTypeResponse.tasks().size());

        ListTaskDto.GetTaskList filterByPeriod = new ListTaskDto.GetTaskList(1, 10,
                List.of(new ListTaskDto.FilterDto("period", "2024")),
                List.of(), "", List.of(StatusEnum.CREATED, StatusEnum.PROCESSING));
        TaskListResponseDto filterByPeriodResponse = given().contentType(ContentType.JSON).body(filterByPeriod)
                .when().post("/list")
                .then().statusCode(200)
                .extract().body().as(TaskListResponseDto.class);
        actualDocs = docs.stream().filter(doc -> doc.period.getYear() == 2024
                && Objects.equals(doc.spreadingTask.clientName, CLIENT1)).toList();
        Assertions.assertEquals(actualDocs.size(), filterByPeriodResponse.tasks().size());

        LocalDateTime lastModifiedTime = LocalDateTime.of(2024, 1, 1, 0, 0, 0);
        ListTaskDto.GetTaskList filterByLastModifiedTime = new ListTaskDto.GetTaskList(1, 10,
                List.of(new ListTaskDto.FilterDto("lastModifiedTime", lastModifiedTime.toString())),
                List.of(), "", List.of(StatusEnum.CREATED, StatusEnum.PROCESSING));
        TaskListResponseDto filterByLastModifiedTimeResponse =
                given().contentType(ContentType.JSON).body(filterByLastModifiedTime)
                        .when().post("/list")
                        .then().statusCode(200)
                        .extract().body().as(TaskListResponseDto.class);
        actualDocs = docs.stream()
                .filter(doc -> !doc.lastModifiedTime.isBefore(lastModifiedTime)
                        && !doc.lastModifiedTime.isAfter(lastModifiedTime.plusDays(1))
                        && Objects.equals(doc.spreadingTask.clientName, CLIENT1))
                .toList();
        Assertions.assertEquals(actualDocs.size(), filterByLastModifiedTimeResponse.tasks().size());

        LocalDateTime createdTime = LocalDateTime.of(2024, 1, 1, 0, 0, 0);
        ListTaskDto.GetTaskList filterByCreatedTime = new ListTaskDto.GetTaskList(1, 10,
                List.of(new ListTaskDto.FilterDto("createdTime", createdTime.toString())),
                List.of(), "", List.of(StatusEnum.CREATED, StatusEnum.PROCESSING));
        TaskListResponseDto filterByCreatedTimeResponse =
                given().contentType(ContentType.JSON).body(filterByCreatedTime)
                        .when().post("/list")
                        .then().statusCode(200)
                        .extract().body().as(TaskListResponseDto.class);
        actualDocs = docs.stream()
                .filter(doc -> !doc.auditable.createdTime.isBefore(createdTime)
                        && !doc.auditable.createdTime.isAfter(createdTime.plusDays(1))
                        && Objects.equals(doc.spreadingTask.clientName, CLIENT1))
                .toList();
        Assertions.assertEquals(actualDocs.size(), filterByCreatedTimeResponse.tasks().size());
    }

    @Test
    @TestSecurity(user = "Name0")
    @OidcSecurity(claims = {@Claim(key = CLIENT_KEY, value = CLIENT1)})
    void testList_filterByMetadata() {
        List<Document> docs = createDocs();
        List<Document> actualDocs;

        ListTaskDto.GetTaskList filterByEntity = new ListTaskDto.GetTaskList(1, 5,
                List.of(new ListTaskDto.FilterDto("entityName", "Entity3")),
                List.of(), "", List.of(StatusEnum.CREATED, StatusEnum.PROCESSING));
        TaskListResponseDto entityResponse = given().contentType(ContentType.JSON).body(filterByEntity)
                .when().post("/list")
                .then().statusCode(200)
                .extract().body().as(TaskListResponseDto.class);
        actualDocs = docs.stream()
                .filter(doc -> Objects.equals(doc.getSpreadingTask().getEntityName().getName(), "Entity3")
                        && Objects.equals(doc.spreadingTask.clientName, CLIENT1))
                .toList();
        Assertions.assertEquals(actualDocs.size(), entityResponse.tasks().size());
        Assertions.assertEquals(actualDocs.get(0).spreadingTask.spreadId, entityResponse.tasks().get(0).spreadId());

        ListTaskDto.GetTaskList filterByIndustry = new ListTaskDto.GetTaskList(1, 5,
                List.of(new ListTaskDto.FilterDto("industryName", "Industry5")),
                List.of(), "", List.of(StatusEnum.CREATED, StatusEnum.PROCESSING));
        TaskListResponseDto industryResponse = given().contentType(ContentType.JSON).body(filterByIndustry)
                .when().post("/list")
                .then().statusCode(200)
                .extract().body().as(TaskListResponseDto.class);
        actualDocs = docs.stream()
                .filter(doc -> Objects.equals(doc.getSpreadingTask().getIndustry().getIndustryName(), "Industry5")
                        && Objects.equals(doc.spreadingTask.clientName, CLIENT1))
                .toList();
        Assertions.assertEquals(actualDocs.size(), industryResponse.tasks().size());
        Assertions.assertEquals(actualDocs.get(0).spreadingTask.spreadId, industryResponse.tasks().get(0).spreadId());

        ListTaskDto.GetTaskList filterByRegion = new ListTaskDto.GetTaskList(1, 5,
                List.of(new ListTaskDto.FilterDto("regionName", "Region9")),
                List.of(), "", List.of(StatusEnum.CREATED, StatusEnum.PROCESSING));
        TaskListResponseDto regionResponse = given().contentType(ContentType.JSON).body(filterByRegion)
                .when().post("/list")
                .then().statusCode(200)
                .extract().body().as(TaskListResponseDto.class);
        actualDocs = docs.stream()
                .filter(doc -> Objects.equals(doc.getSpreadingTask().getRegion().getRegionName(), "Region9")
                        && Objects.equals(doc.spreadingTask.clientName, CLIENT1))
                .toList();
        Assertions.assertEquals(actualDocs.size(), regionResponse.tasks().size());
        Assertions.assertEquals(actualDocs.get(0).spreadingTask.spreadId, regionResponse.tasks().get(0).spreadId());
    }
}
