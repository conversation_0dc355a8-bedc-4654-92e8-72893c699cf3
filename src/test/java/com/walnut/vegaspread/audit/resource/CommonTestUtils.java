package com.walnut.vegaspread.audit.resource;

import io.smallrye.jwt.build.Jwt;

import java.util.Set;

public class CommonTestUtils {
    public static final String USERNAME = "testUser";
    public static final String NAME = "Full Name";
    public static final String CLIENT_NAME = "Client Name";
    public static String getAccessToken(Set<String> groups) {
        return Jwt.preferredUserName(USERNAME)
                .groups(groups)
                .claim("name", NAME)
                .claim("client_name", CLIENT_NAME)
                .issuer("https://server.example.com")
                .audience("https://service.example.com")
                .sign();
    }
}
