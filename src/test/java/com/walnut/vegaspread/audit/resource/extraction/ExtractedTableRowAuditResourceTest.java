package com.walnut.vegaspread.audit.resource.extraction;

import com.walnut.vegaspread.audit.entity.extraction.ExtractedTableRowAuditEntity;
import com.walnut.vegaspread.audit.repository.extraction.ExtractedTableRowAuditRepository;
import com.walnut.vegaspread.audit.service.common.ExchangeService;
import com.walnut.vegaspread.common.model.audit.coa.CoaItemAuditDto;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedTableRowAuditDto;
import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import static com.walnut.vegaspread.audit.resource.CommonTestUtils.NAME;
import static com.walnut.vegaspread.audit.resource.CommonTestUtils.USERNAME;
import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;

@QuarkusTest
@TestHTTPEndpoint(ExtractedTableRowAuditResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ExtractedTableRowAuditResourceTest {

    @Inject
    ExtractedTableRowAuditRepository extractedTableRowAuditRepository;

    @InjectMock
    ExchangeService exchangeService;

    @Inject
    Flyway flyway;

    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Transactional
    List<ExtractedTableRowAuditEntity> createExtractedTableRowAuditItems() {
        List<ExtractedTableRowAuditEntity> extractedTableRowAuditEntities = new ArrayList<>();

        ExtractedTableRowAuditEntity extractedTableRowAuditEntity1 = new ExtractedTableRowAuditEntity();
        extractedTableRowAuditEntity1.setTableId(1)
                .setRowId((short) 2)
                .setColName("header_ids")
                .setPrevValue(null)
                .setNewValue(String.valueOf(List.of(1, 2, 3)))
                .setAuditTime(LocalDateTime.now())
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAction(AuditStatus.CREATED);
        extractedTableRowAuditEntities.add(extractedTableRowAuditEntity1);

        ExtractedTableRowAuditEntity extractedTableRowAuditEntity2 = new ExtractedTableRowAuditEntity();
        extractedTableRowAuditEntity2.setTableId(1)
                .setRowId((short) 2)
                .setColName("header_ids")
                .setPrevValue(String.valueOf(List.of(1, 2, 3)))
                .setNewValue(null)
                .setAuditTime(LocalDateTime.now())
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAction(AuditStatus.DELETED);
        extractedTableRowAuditEntities.add(extractedTableRowAuditEntity2);

        ExtractedTableRowAuditEntity extractedTableRowAuditEntity3 = new ExtractedTableRowAuditEntity();
        extractedTableRowAuditEntity3.setTableId(2)
                .setRowId((short) 1)
                .setColName("nta_table_id")
                .setPrevValue(null)
                .setNewValue(String.valueOf(3))
                .setAuditTime(LocalDateTime.now())
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAction(AuditStatus.CREATED);
        extractedTableRowAuditEntities.add(extractedTableRowAuditEntity3);

        extractedTableRowAuditRepository.persist(extractedTableRowAuditEntities);
        return extractedTableRowAuditEntities;
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForCreateForEmptyList() {

        List<CoaItemAuditDto.Response> auditedCreateItems = given().when()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .post("/audit-create")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(auditedCreateItems.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForCreate() {
        List<ExtractedTableRowAuditDto.Create> auditItems = List.of(
                new ExtractedTableRowAuditDto.Create(1, 1, "coa_id", String.valueOf(2)),
                new ExtractedTableRowAuditDto.Create(1, 1, "header_ids", String.valueOf(List.of(1, 2, 3)))
        );

        List<ExtractedTableRowAuditDto.Response> auditedCreateItems = given().when()
                .contentType(ContentType.JSON)
                .body(auditItems)
                .post("/audit-create")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(auditItems.size(), auditedCreateItems.size());

        for (int i = 0; i < auditItems.size(); i++) {
            ExtractedTableRowAuditEntity extractedTableRowAuditEntity = extractedTableRowAuditRepository.findById(
                    auditedCreateItems.get(i).id());
            ExtractedTableRowAuditDto.Create auditItem = auditItems.get(i);
            Assertions.assertEquals(auditItem.tableId(), extractedTableRowAuditEntity.getTableId());
            Assertions.assertEquals(auditItem.rowId().shortValue(), extractedTableRowAuditEntity.getRowId());
            Assertions.assertEquals(auditItem.colName(), extractedTableRowAuditEntity.getColName());
            Assertions.assertEquals(auditItem.newValue(), extractedTableRowAuditEntity.getNewValue());
            Assertions.assertEquals(AuditStatus.CREATED, extractedTableRowAuditEntity.getAction());
            Assertions.assertNull(extractedTableRowAuditEntity.getPrevValue());
        }
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForUpdateForEmptyList() {

        List<ExtractedTableRowAuditDto.Response> auditUpdateItems = given().when()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .post("/audit-update")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(auditUpdateItems.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForUpdate() {

        createExtractedTableRowAuditItems();
        List<ExtractedTableRowAuditDto.Update> auditItems = List.of(
                new ExtractedTableRowAuditDto.Update(2, 1, "nta_table_id", String.valueOf(3),
                        String.valueOf(6)));
        List<ExtractedTableRowAuditDto.Response> auditUpdateItems = given().when()
                .contentType(ContentType.JSON)
                .body(auditItems)
                .post("/audit-update")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(auditItems.size(), auditUpdateItems.size());

        for (int i = 0; i < auditItems.size(); i++) {
            ExtractedTableRowAuditEntity extractedTableRowAuditEntity = extractedTableRowAuditRepository.findById(
                    auditUpdateItems.get(i).id());
            ExtractedTableRowAuditDto.Update auditItem = auditItems.get(i);
            Assertions.assertEquals(auditItem.tableId(), extractedTableRowAuditEntity.getTableId());
            Assertions.assertEquals(auditItem.rowId().shortValue(), extractedTableRowAuditEntity.getRowId());
            Assertions.assertEquals(auditItem.colName(), extractedTableRowAuditEntity.getColName());
            Assertions.assertEquals(auditItem.newValue(), extractedTableRowAuditEntity.getNewValue());
            Assertions.assertEquals(AuditStatus.UPDATED, extractedTableRowAuditEntity.getAction());
            Assertions.assertEquals(auditItem.prevValue(), extractedTableRowAuditEntity.getPrevValue());
        }
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForDeleteForEmptyList() {

        List<ExtractedTableRowAuditDto.Response> auditDeleteItems = given().when()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .post("/audit-delete")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(auditDeleteItems.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForDelete() {

        createExtractedTableRowAuditItems();
        List<ExtractedTableRowAuditDto.Delete> auditItems = List.of(
                new ExtractedTableRowAuditDto.Delete(2, 1, "nta_table_id", String.valueOf(3)));
        List<ExtractedTableRowAuditDto.Response> auditDeleteItems = given().when()
                .contentType(ContentType.JSON)
                .body(auditItems)
                .post("/audit-delete")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(auditItems.size(), auditDeleteItems.size());

        for (int i = 0; i < auditItems.size(); i++) {
            ExtractedTableRowAuditEntity extractedTableRowAuditEntity = extractedTableRowAuditRepository.findById(
                    auditDeleteItems.get(i).id());
            ExtractedTableRowAuditDto.Delete auditItem = auditItems.get(i);
            Assertions.assertEquals(auditItem.tableId(), extractedTableRowAuditEntity.getTableId());
            Assertions.assertEquals(auditItem.rowId().shortValue(), extractedTableRowAuditEntity.getRowId());
            Assertions.assertEquals(auditItem.colName(), extractedTableRowAuditEntity.getColName());
            Assertions.assertNull(extractedTableRowAuditEntity.getNewValue());
            Assertions.assertEquals(AuditStatus.DELETED, extractedTableRowAuditEntity.getAction());
            Assertions.assertEquals(auditItem.prevValue(), extractedTableRowAuditEntity.getPrevValue());
        }
    }

    @Test
    @TestSecurity(user = NAME)
    void testList() {

        List<ExtractedTableRowAuditEntity> extractedTableRowAuditEntities = createExtractedTableRowAuditItems();
        LocalDate filterDate = LocalDate.now();
        String filterUser = USERNAME;
        int pageSize = 3;
        int pageNumber = 1;
        String colName = "header_ids";
        ExtractedTableRowAuditDto.ListForTables listTablesDto = new ExtractedTableRowAuditDto.ListForTables(
                List.of(1, 2), colName, pageNumber, pageSize, filterUser, filterDate
        );
        ExtractedTableRowAuditDto.ListResponse auditListResponse = given().when()
                .contentType(ContentType.JSON)
                .body(listTablesDto)
                .post("/list")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        List<ExtractedTableRowAuditEntity> filteredDbEntities = extractedTableRowAuditEntities.stream()
                .filter(extractedTableRowAuditEntity ->
                        listTablesDto.tableIds().contains(extractedTableRowAuditEntity.getTableId())
                                && colName.equals(extractedTableRowAuditEntity.getColName())
                                && filterUser.equals(extractedTableRowAuditEntity.getAuditedBy())
                                && filterDate.equals(extractedTableRowAuditEntity.getAuditTime().toLocalDate()))
                .sorted(Comparator.comparing(ExtractedTableRowAuditEntity::getAuditTime).reversed())
                .skip((pageNumber - 1) * pageSize)
                .limit(pageSize).toList();

        Assertions.assertEquals(listTablesDto.pageNumber(), auditListResponse.pageNumber());
        Assertions.assertEquals(listTablesDto.pageSize(), auditListResponse.pageSize());
        Assertions.assertEquals((int) Math.ceil((double) filteredDbEntities.size() / listTablesDto.pageSize()),
                auditListResponse.totalPages());

        List<ExtractedTableRowAuditDto.Response> auditListItems = auditListResponse.extractedTableRowAudits();
        Assertions.assertEquals(filteredDbEntities.size(), auditListItems.size());

        for (int i = 0; i < auditListItems.size(); i++) {
            ExtractedTableRowAuditEntity extractedTableRowAuditEntity = filteredDbEntities.get(i);
            ExtractedTableRowAuditDto.Response auditListItem = auditListItems.get(i);
            Assertions.assertEquals(extractedTableRowAuditEntity.getId(), auditListItem.id());
            Assertions.assertEquals(extractedTableRowAuditEntity.getTableId(), auditListItem.tableId());
            Assertions.assertEquals(extractedTableRowAuditEntity.getRowId().shortValue(), auditListItem.rowId());
            Assertions.assertEquals(extractedTableRowAuditEntity.getColName(), auditListItem.colName());
            Assertions.assertEquals(extractedTableRowAuditEntity.getPrevValue(), auditListItem.prevValue());
            Assertions.assertEquals(extractedTableRowAuditEntity.getNewValue(), auditListItem.newValue());
            Assertions.assertEquals(extractedTableRowAuditEntity.getAction(), auditListItem.action());
        }
    }
}
