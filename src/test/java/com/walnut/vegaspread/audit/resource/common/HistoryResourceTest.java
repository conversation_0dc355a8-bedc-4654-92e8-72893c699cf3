package com.walnut.vegaspread.audit.resource.common;

import com.walnut.vegaspread.audit.entity.common.BaseAuditEntity;
import com.walnut.vegaspread.audit.entity.extraction.ExtractedRowCoaDataAuditEntity;
import com.walnut.vegaspread.audit.entity.extraction.ExtractedTableRowAuditEntity;
import com.walnut.vegaspread.audit.entity.extraction.LayoutBlockAuditEntity;
import com.walnut.vegaspread.audit.model.common.AuditListType;
import com.walnut.vegaspread.audit.model.common.HistoryDto;
import com.walnut.vegaspread.audit.repository.extraction.ExtractedRowCoaDataAuditRepository;
import com.walnut.vegaspread.audit.repository.extraction.ExtractedTableRowAuditRepository;
import com.walnut.vegaspread.audit.repository.extraction.LayoutBlockAuditRepository;
import com.walnut.vegaspread.audit.service.common.ExchangeService;
import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import static com.walnut.vegaspread.audit.resource.CommonTestUtils.NAME;
import static com.walnut.vegaspread.audit.resource.CommonTestUtils.USERNAME;
import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static io.restassured.RestAssured.given;

@QuarkusTest
@TestHTTPEndpoint(HistoryResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class HistoryResourceTest {

    @Inject
    Flyway flyway;
    @InjectMock
    ExchangeService exchangeService;
    @Inject
    ExtractedTableRowAuditRepository extractedTableRowAuditRepository;
    @Inject
    ExtractedRowCoaDataAuditRepository extractedRowCoaDataAuditRepository;
    @Inject
    LayoutBlockAuditRepository layoutBlockAuditRepository;

    @BeforeAll
    void setUp() {
        startMockServer();
    }

    //We need to run migrations before each test since the data needs to be reloaded from migration for the view to
    // refresh.
    @BeforeEach()
    void setupDb() {
        flyway.migrate();
    }

    @AfterEach
    void clean() {
        flyway.clean();
    }

    @AfterAll
    void cleanDb() {
        stopMockServer();
    }

    @Transactional
    List<ExtractedTableRowAuditEntity> createExtractedTableRowAuditItemsForHeaderIds() {
        List<ExtractedTableRowAuditEntity> extractedTableRowAuditEntities = new ArrayList<>();

        ExtractedTableRowAuditEntity extractedTableRowAuditEntity1 = new ExtractedTableRowAuditEntity();
        extractedTableRowAuditEntity1.setTableId(1)
                .setRowId((short) 1)
                .setColName("coa_id")
                .setPrevValue(null)
                .setNewValue(String.valueOf(1))
                .setAuditTime(LocalDateTime.now())
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAction(AuditStatus.CREATED);
        extractedTableRowAuditEntities.add(extractedTableRowAuditEntity1);

        ExtractedTableRowAuditEntity extractedTableRowAuditEntity2 = new ExtractedTableRowAuditEntity();
        extractedTableRowAuditEntity2.setTableId(1)
                .setRowId((short) 1)
                .setColName("coa_id")
                .setPrevValue(String.valueOf(1))
                .setNewValue(String.valueOf(2))
                .setAuditTime(LocalDateTime.now())
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAction(AuditStatus.UPDATED);
        extractedTableRowAuditEntities.add(extractedTableRowAuditEntity2);

        ExtractedTableRowAuditEntity extractedTableRowAuditEntity3 = new ExtractedTableRowAuditEntity();
        extractedTableRowAuditEntity3.setTableId(1)
                .setRowId((short) 2)
                .setColName("header_ids")
                .setPrevValue(null)
                .setNewValue(String.valueOf(List.of(1, 2, 3)))
                .setAuditTime(LocalDateTime.now())
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAction(AuditStatus.CREATED);
        extractedTableRowAuditEntities.add(extractedTableRowAuditEntity3);

        ExtractedTableRowAuditEntity extractedTableRowAuditEntity4 = new ExtractedTableRowAuditEntity();
        extractedTableRowAuditEntity4.setTableId(1)
                .setRowId((short) 2)
                .setColName("header_ids")
                .setPrevValue(String.valueOf(List.of(1, 2, 3)))
                .setNewValue(null)
                .setAuditTime(LocalDateTime.now())
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAction(AuditStatus.DELETED);
        extractedTableRowAuditEntities.add(extractedTableRowAuditEntity4);

        ExtractedTableRowAuditEntity extractedTableRowAuditEntity5 = new ExtractedTableRowAuditEntity();
        extractedTableRowAuditEntity5.setTableId(2)
                .setRowId((short) 1)
                .setColName("nta_table_id")
                .setPrevValue(null)
                .setNewValue(String.valueOf(3))
                .setAuditTime(LocalDateTime.now())
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAction(AuditStatus.CREATED);
        extractedTableRowAuditEntities.add(extractedTableRowAuditEntity5);

        extractedTableRowAuditRepository.persist(extractedTableRowAuditEntities);
        return extractedTableRowAuditEntities;
    }

    @Transactional
    List<LayoutBlockAuditEntity> createLayoutBlockAuditItems() {
        List<LayoutBlockAuditEntity> layoutBlockAuditEntities = new ArrayList<>();

        LayoutBlockAuditEntity layoutBlockAuditEntity1 =
                (LayoutBlockAuditEntity) new LayoutBlockAuditEntity()
                        .setBlockId(1)
                        .setColName("tag")
                        .setPrevValue(null)
                        .setNewValue("tag1")
                        .setAction(AuditStatus.CREATED)
                        .setAuditedBy(USERNAME)
                        .setAuditedBYFullName(NAME)
                        .setAuditTime(LocalDateTime.now());
        layoutBlockAuditEntities.add(layoutBlockAuditEntity1);

        LayoutBlockAuditEntity layoutBlockAuditEntity2 =
                (LayoutBlockAuditEntity) new LayoutBlockAuditEntity()
                        .setBlockId(4)
                        .setColName("tag")
                        .setPrevValue(null)
                        .setNewValue("tag2")
                        .setAction(AuditStatus.CREATED)
                        .setAuditedBy(USERNAME)
                        .setAuditedBYFullName(NAME)
                        .setAuditTime(LocalDateTime.now().plusSeconds(1));
        layoutBlockAuditEntities.add(layoutBlockAuditEntity2);

        LayoutBlockAuditEntity layoutBlockAuditEntity3 =
                (LayoutBlockAuditEntity) new LayoutBlockAuditEntity()
                        .setBlockId(4)
                        .setColName("tag")
                        .setPrevValue("tag2")
                        .setNewValue("Balance Sheet")
                        .setAction(AuditStatus.UPDATED)
                        .setAuditedBy(USERNAME)
                        .setAuditedBYFullName(NAME)
                        .setAuditTime(LocalDateTime.now().plusSeconds(2));
        layoutBlockAuditEntities.add(layoutBlockAuditEntity3);

        layoutBlockAuditRepository.persist(layoutBlockAuditEntities);
        return layoutBlockAuditEntities;
    }

    @Transactional
    List<ExtractedRowCoaDataAuditEntity> createExtractedRowCoaDataAuditItemsForCoaId() {
        List<ExtractedRowCoaDataAuditEntity> extractedRowCoaDataAuditEntities = new ArrayList<>();

        ExtractedRowCoaDataAuditEntity extractedRowCoaDataAuditEntity1 = new ExtractedRowCoaDataAuditEntity();
        extractedRowCoaDataAuditEntity1.setTableId(1)
                .setRowId((short) 1)
                .setColName("coa_id")
                .setPrevValue(String.valueOf(1))
                .setNewValue(String.valueOf(2))
                .setAuditTime(LocalDateTime.now())
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAction(AuditStatus.CREATED);
        extractedRowCoaDataAuditEntities.add(extractedRowCoaDataAuditEntity1);

        ExtractedRowCoaDataAuditEntity extractedRowCoaDataAuditEntity2 = new ExtractedRowCoaDataAuditEntity();
        extractedRowCoaDataAuditEntity2.setTableId(1)
                .setRowId((short) 1)
                .setColName("coa_id")
                .setPrevValue(String.valueOf(2))
                .setNewValue(String.valueOf(7))
                .setAuditTime(LocalDateTime.now().plusSeconds(1))
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAction(AuditStatus.UPDATED);
        extractedRowCoaDataAuditEntities.add(extractedRowCoaDataAuditEntity2);

        extractedRowCoaDataAuditRepository.persist(extractedRowCoaDataAuditEntities);

        return extractedRowCoaDataAuditEntities;
    }

    @Test
    @TestSecurity(user = NAME)
    void testGetAuditTypes() {

        List<String> auditTypes = given().when()
                .get("/types")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(List.of("COA_ID", "HEADER_ID", "TAG"), auditTypes);
    }

    @Test
    @TestSecurity(user = NAME)
    void testGetHistoryForHeaderIds() {
        List<ExtractedTableRowAuditEntity> extractedTableRowAuditEntities =
                createExtractedTableRowAuditItemsForHeaderIds();
        LocalDate auditDate = LocalDate.now();
        int pageSize = 3;
        int pageNumber = 1;

        HistoryDto.Request auditListDto = new HistoryDto.Request(1, AuditListType.HEADER_ID, pageNumber, pageSize,
                USERNAME, auditDate);

        HistoryDto.Response auditHistory = given().when()
                .contentType(ContentType.JSON)
                .body(auditListDto)
                .post()
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(HistoryDto.Response.class);

        List<ExtractedTableRowAuditEntity> audits = extractedTableRowAuditEntities.stream()
                .filter(extractedTableRowAuditEntity -> "header_ids".equals(extractedTableRowAuditEntity.getColName()))
                .sorted(Comparator.comparing(
                        BaseAuditEntity::getAuditTime).reversed()).toList();

        List<ExtractedTableRowAuditEntity> headerIdAudits = audits.stream()
                .skip((pageNumber - 1) * pageSize)
                .limit(pageSize)
                .toList();

        Assertions.assertEquals(pageNumber, auditHistory.pageNumber());
        Assertions.assertEquals(pageSize, auditHistory.pageSize());
        Assertions.assertEquals((int) Math.ceil((double) audits.size() / pageSize),
                auditHistory.totalPages());
        Assertions.assertEquals(headerIdAudits.size(), auditHistory.auditItems().size());

        for (int i = 0; i < auditHistory.auditItems().size(); i++) {
            ExtractedTableRowAuditEntity headerIdAudit = headerIdAudits.get(i);
            HistoryDto.Item auditListItem = auditHistory.auditItems().get(i);
            Assertions.assertEquals(headerIdAudit.getTableId(), auditListItem.blockId());
            Assertions.assertEquals(Integer.valueOf(headerIdAudit.getRowId()), auditListItem.rowId());
            Assertions.assertEquals(headerIdAudit.getPrevValue() == null ? "NA" : headerIdAudit.getPrevValue(),
                    auditListItem.prevValue());
            Assertions.assertEquals(headerIdAudit.getNewValue() == null ? "NA" : headerIdAudit.getNewValue(),
                    auditListItem.newValue());
            Assertions.assertEquals(headerIdAudit.getAction(), auditListItem.action());
            Assertions.assertEquals(USERNAME, auditListItem.auditedBy());
        }
    }

    @Test
    @TestSecurity(user = NAME)
    void testGetHistoryForTag() {
        List<LayoutBlockAuditEntity> layoutBlockAuditEntities = createLayoutBlockAuditItems();
        LocalDate auditDate = LocalDate.now();
        int pageSize = 2;
        int pageNumber = 2;

        HistoryDto.Request auditListDto = new HistoryDto.Request(1, AuditListType.TAG, pageNumber, pageSize,
                USERNAME, auditDate);

        HistoryDto.Response auditHistory = given().when()
                .contentType(ContentType.JSON)
                .body(auditListDto)
                .post()
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(HistoryDto.Response.class);

        List<LayoutBlockAuditEntity> audits = layoutBlockAuditEntities.stream()
                .filter(layoutBlockAuditEntity -> "tag".equals(layoutBlockAuditEntity.getColName()))
                .sorted(Comparator.comparing(
                        LayoutBlockAuditEntity::getAuditTime).reversed())
                .toList();

        List<LayoutBlockAuditEntity> tagAudits = audits.stream()
                .skip((pageNumber - 1) * pageSize)
                .limit(pageSize)
                .toList();

        Assertions.assertEquals(pageNumber, auditHistory.pageNumber());
        Assertions.assertEquals(pageSize, auditHistory.pageSize());
        Assertions.assertEquals((int) Math.ceil((double) audits.size() / pageSize),
                auditHistory.totalPages());
        Assertions.assertEquals(tagAudits.size(), auditHistory.auditItems().size());

        for (int i = 0; i < auditHistory.auditItems().size(); i++) {
            LayoutBlockAuditEntity tagAudit = tagAudits.get(i);
            HistoryDto.Item auditListItem = auditHistory.auditItems().get(i);
            Assertions.assertEquals(tagAudit.getBlockId(), auditListItem.blockId());
            Assertions.assertNull(auditListItem.rowId());
            Assertions.assertEquals(tagAudit.getPrevValue() == null ? "NA" : tagAudit.getPrevValue(),
                    auditListItem.prevValue());
            Assertions.assertEquals(tagAudit.getNewValue() == null ? "NA" : tagAudit.getNewValue(),
                    auditListItem.newValue());
            Assertions.assertEquals(tagAudit.getAction(), auditListItem.action());
            Assertions.assertEquals(USERNAME, auditListItem.auditedBy());
        }
    }

    @Test
    @TestSecurity(user = NAME)
    void testGetHistoryForCoaId() {
        List<ExtractedRowCoaDataAuditEntity> extractedRowCoaDataAuditEntities =
                createExtractedRowCoaDataAuditItemsForCoaId();
        LocalDate auditDate = LocalDate.now();
        int pageSize = 1;
        int pageNumber = 1;

        Mockito.when(exchangeService.getCoas(List.of(2, 7))).thenReturn(List.of(
                new CoaItemDto(2, "1.BS.CA.Cash", "1.BS.CA.Cash", "walnut", 3, "Current Assets", true, true),
                new CoaItemDto(7, "1.BS.CA.Accounts Receivable-Trade", "1.BS.CA.Accounts Receivable-Trade", "walnut",
                        3, "Current Assets", true, true)
        ));

        Map<String, String> coaIdCoaTextMap = Map.of(
                String.valueOf(2), "1.BS.CA.Cash",
                String.valueOf(7), "1.BS.CA.Accounts Receivable-Trade"
        );
        HistoryDto.Request auditListDto = new HistoryDto.Request(1, AuditListType.COA_ID, pageNumber, pageSize,
                USERNAME, auditDate);

        HistoryDto.Response auditHistory = given().when()
                .contentType(ContentType.JSON)
                .body(auditListDto)
                .post()
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(HistoryDto.Response.class);

        List<ExtractedRowCoaDataAuditEntity> audits = extractedRowCoaDataAuditEntities.stream()
                .filter(extractedRowCoaDataAuditEntity -> "coa_id".equals(extractedRowCoaDataAuditEntity.getColName()))
                .sorted(Comparator.comparing(
                        BaseAuditEntity::getAuditTime).reversed()).toList();

        List<ExtractedRowCoaDataAuditEntity> coaIdAudits = audits.stream()
                .skip((pageNumber - 1) * pageSize)
                .limit(pageSize)
                .toList();
        Assertions.assertEquals(pageNumber, auditHistory.pageNumber());
        Assertions.assertEquals(pageSize, auditHistory.pageSize());
        Assertions.assertEquals((int) Math.ceil((double) audits.size() / pageSize),
                auditHistory.totalPages());
        Assertions.assertEquals(coaIdAudits.size(), auditHistory.auditItems().size());

        for (int i = 0; i < auditHistory.auditItems().size(); i++) {
            ExtractedRowCoaDataAuditEntity coaIdAudit = coaIdAudits.get(i);
            HistoryDto.Item auditListItem = auditHistory.auditItems().get(i);
            Assertions.assertEquals(coaIdAudit.getTableId(), auditListItem.blockId());
            Assertions.assertEquals(Integer.valueOf(coaIdAudit.getRowId()), auditListItem.rowId());
            Assertions.assertEquals(
                    coaIdCoaTextMap.get(coaIdAudit.getPrevValue() == null ? "NA" : coaIdAudit.getPrevValue()),
                    auditListItem.prevValue());
            Assertions.assertEquals(
                    coaIdCoaTextMap.get(coaIdAudit.getNewValue() == null ? "NA" : coaIdAudit.getNewValue()),
                    auditListItem.newValue());
            Assertions.assertEquals(coaIdAudit.getAction(), auditListItem.action());
            Assertions.assertEquals(USERNAME, auditListItem.auditedBy());
        }
    }
}
