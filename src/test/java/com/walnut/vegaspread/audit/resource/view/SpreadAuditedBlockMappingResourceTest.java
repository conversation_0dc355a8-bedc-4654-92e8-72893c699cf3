package com.walnut.vegaspread.audit.resource.view;

import com.walnut.vegaspread.audit.entity.extraction.ExtractedTableRowAuditEntity;
import com.walnut.vegaspread.audit.entity.extraction.LayoutBlockAuditEntity;
import com.walnut.vegaspread.audit.repository.extraction.ExtractedTableRowAuditRepository;
import com.walnut.vegaspread.audit.repository.extraction.LayoutBlockAuditRepository;
import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;
import java.util.List;

import static com.walnut.vegaspread.audit.resource.CommonTestUtils.NAME;
import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;

@QuarkusTest
@TestHTTPEndpoint(SpreadAuditedBlockMappingResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class SpreadAuditedBlockMappingResourceTest {

    @Inject
    ExtractedTableRowAuditRepository extractedTableRowAuditRepository;

    @Inject
    LayoutBlockAuditRepository layoutBlockAuditRepository;

    @Inject
    Flyway flyway;

    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @BeforeEach
    void initData() {
        setupData();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Transactional
    void setupData() {
        layoutBlockAuditRepository.persist(
                (LayoutBlockAuditEntity) new LayoutBlockAuditEntity()
                        .setBlockId(4)
                        .setColName("tag")
                        .setPrevValue(null)
                        .setNewValue("Balance Sheet")
                        .setAction(AuditStatus.UPDATED)
                        .setAuditedBy("developer")
                        .setAuditTime(LocalDateTime.now())
        );

        extractedTableRowAuditRepository.persist(
                (ExtractedTableRowAuditEntity) new ExtractedTableRowAuditEntity()
                        .setTableId(4720)
                        .setRowId((short) 1)
                        .setColName("coa_id")
                        .setPrevValue(String.valueOf(1))
                        .setNewValue(String.valueOf(2))
                        .setAction(AuditStatus.UPDATED)
                        .setAuditedBy("maker")
                        .setAuditTime(LocalDateTime.now())

        );

        extractedTableRowAuditRepository.persist(
                (ExtractedTableRowAuditEntity) new ExtractedTableRowAuditEntity()
                        .setTableId(4720)
                        .setRowId((short) 1)
                        .setColName("coa_id")
                        .setPrevValue(String.valueOf(2))
                        .setNewValue(String.valueOf(3))
                        .setAction(AuditStatus.UPDATED)
                        .setAuditedBy("maker")
                        .setAuditTime(LocalDateTime.now())

        );

        extractedTableRowAuditRepository.persist(
                (ExtractedTableRowAuditEntity) new ExtractedTableRowAuditEntity()
                        .setTableId(4720)
                        .setRowId((short) 1)
                        .setColName("coa_id")
                        .setPrevValue(String.valueOf(3))
                        .setNewValue(String.valueOf(1))
                        .setAction(AuditStatus.UPDATED)
                        .setAuditedBy("maker")
                        .setAuditTime(LocalDateTime.now())

        );

        extractedTableRowAuditRepository.persist(
                (ExtractedTableRowAuditEntity) new ExtractedTableRowAuditEntity()
                        .setTableId(4720)
                        .setRowId((short) 1)
                        .setColName("coa_id")
                        .setPrevValue(String.valueOf(1))
                        .setNewValue(String.valueOf(5))
                        .setAction(AuditStatus.UPDATED)
                        .setAuditedBy("developer")
                        .setAuditTime(LocalDateTime.now())

        );
    }

    @Test
    @TestSecurity(user = NAME)
    void testGetAuditedBlocksForSpread() {
        List<Integer> blockIdsForSpread = List.of(4);
        List<Integer> blockIds = given()
                .contentType(ContentType.JSON)
                .pathParam("spreadId", 1)
                .get("/{spreadId}")
                .then().statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<>() {
                });

        Assertions.assertIterableEquals(blockIdsForSpread, blockIds);
    }
}
