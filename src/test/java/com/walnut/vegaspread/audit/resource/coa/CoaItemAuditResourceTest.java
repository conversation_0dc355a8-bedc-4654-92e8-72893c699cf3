package com.walnut.vegaspread.audit.resource.coa;

import com.walnut.vegaspread.audit.entity.coa.CoaItemAuditEntity;
import com.walnut.vegaspread.audit.repository.coa.CoaItemAuditRepository;
import com.walnut.vegaspread.audit.service.common.ExchangeService;
import com.walnut.vegaspread.common.model.audit.coa.CoaItemAuditDto;
import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import com.walnut.vegaspread.common.roles.Roles;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.walnut.vegaspread.audit.resource.CommonTestUtils.NAME;
import static com.walnut.vegaspread.audit.resource.CommonTestUtils.USERNAME;
import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@QuarkusTest
@TestHTTPEndpoint(CoaItemAuditResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CoaItemAuditResourceTest {

    @Inject
    CoaItemAuditRepository coaItemAuditRepository;

    @InjectMock
    ExchangeService exchangeService;

    @Inject
    Flyway flyway;

    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
        reset(exchangeService);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Transactional
    List<CoaItemAuditEntity> createCoaAuditItems() {
        List<CoaItemAuditEntity> coaItemAuditEntities = new ArrayList<>();

        CoaItemAuditEntity coaItemAuditEntity1 = new CoaItemAuditEntity();
        coaItemAuditEntity1.setCoaId(1)
                .setColName("coaText")
                .setPrevValue(null)
                .setNewValue("text1")
                .setAuditTime(LocalDateTime.now())
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAction(AuditStatus.CREATED);
        coaItemAuditEntities.add(coaItemAuditEntity1);

        CoaItemAuditEntity coaItemAuditEntity2 = new CoaItemAuditEntity();
        coaItemAuditEntity2.setCoaId(1)
                .setColName("coaText")
                .setPrevValue("text1")
                .setNewValue("updatedText1")
                .setAuditTime(LocalDateTime.now())
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAction(AuditStatus.UPDATED);
        coaItemAuditEntities.add(coaItemAuditEntity2);

        CoaItemAuditEntity coaItemAuditEntity3 = new CoaItemAuditEntity();
        coaItemAuditEntity3.setCoaId(2)
                .setColName("isActive")
                .setPrevValue(null)
                .setNewValue(Boolean.TRUE.toString())
                .setAuditTime(LocalDateTime.now())
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAction(AuditStatus.CREATED);
        coaItemAuditEntities.add(coaItemAuditEntity3);

        CoaItemAuditEntity coaItemAuditEntity4 = new CoaItemAuditEntity();
        coaItemAuditEntity4.setCoaId(2)
                .setColName("isActive")
                .setPrevValue(Boolean.TRUE.toString())
                .setNewValue(null)
                .setAuditTime(LocalDateTime.now())
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAction(AuditStatus.DELETED);
        coaItemAuditEntities.add(coaItemAuditEntity4);

        CoaItemAuditEntity coaItemAuditEntity5 = new CoaItemAuditEntity();
        coaItemAuditEntity5.setCoaId(5)
                .setColName("coaText")
                .setPrevValue(null)
                .setNewValue("text5")
                .setAuditTime(LocalDateTime.now())
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAction(AuditStatus.CREATED);
        coaItemAuditEntities.add(coaItemAuditEntity5);

        coaItemAuditRepository.persist(coaItemAuditEntities);
        return coaItemAuditEntities;
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.CREATE_NEW_COA)
    void testAuditForCreateForEmptyList() {

        List<CoaItemAuditDto.Response> auditedCreateItems = given().when()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .post("/audit-create")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(auditedCreateItems.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.CREATE_NEW_COA)
    void testAuditForCreate() {
        List<CoaItemAuditDto.Create> auditItems = List.of(
                new CoaItemAuditDto.Create(1, "coaText", "text1"),
                new CoaItemAuditDto.Create(1, "isActive", "false")
        );

        List<CoaItemAuditDto.Response> auditedCreateItems = given().when()
                .contentType(ContentType.JSON)
                .body(auditItems)
                .post("/audit-create")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(auditItems.size(), auditedCreateItems.size());

        for (int i = 0; i < auditItems.size(); i++) {
            CoaItemAuditEntity coaItemAuditEntity = coaItemAuditRepository.findById(auditedCreateItems.get(i).id());
            CoaItemAuditDto.Create auditItem = auditItems.get(i);
            Assertions.assertEquals(auditItem.coaId(), coaItemAuditEntity.getCoaId());
            Assertions.assertEquals(auditItem.colName(), coaItemAuditEntity.getColName());
            Assertions.assertEquals(auditItem.newValue(), coaItemAuditEntity.getNewValue());
            Assertions.assertEquals(AuditStatus.CREATED, coaItemAuditEntity.getAction());
            Assertions.assertNull(coaItemAuditEntity.getPrevValue());
        }
        verify(exchangeService, times(1)).getNameFromUsername(NAME);
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForUpdateForEmptyList() {

        List<CoaItemAuditDto.Response> auditUpdateItems = given().when()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .post("/audit-update")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(auditUpdateItems.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForUpdate() {

        createCoaAuditItems();
        List<CoaItemAuditDto.Update> auditItems = List.of(
                new CoaItemAuditDto.Update(5, "coaText", "coaText5", "updatedCoaText5"));
        List<CoaItemAuditDto.Response> auditUpdateItems = given().when()
                .contentType(ContentType.JSON)
                .body(auditItems)
                .post("/audit-update")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(auditItems.size(), auditUpdateItems.size());

        for (int i = 0; i < auditItems.size(); i++) {
            CoaItemAuditEntity coaItemAuditEntity = coaItemAuditRepository.findById(auditUpdateItems.get(i).id());
            CoaItemAuditDto.Update auditItem = auditItems.get(i);
            Assertions.assertEquals(auditItem.coaId(), coaItemAuditEntity.getCoaId());
            Assertions.assertEquals(auditItem.colName(), coaItemAuditEntity.getColName());
            Assertions.assertEquals(auditItem.newValue(), coaItemAuditEntity.getNewValue());
            Assertions.assertEquals(AuditStatus.UPDATED, coaItemAuditEntity.getAction());
            Assertions.assertEquals(auditItem.prevValue(), coaItemAuditEntity.getPrevValue());
        }
        verify(exchangeService, times(1)).getNameFromUsername(NAME);
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForDeleteForEmptyList() {

        List<CoaItemAuditDto.Response> auditDeleteItems = given().when()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .post("/audit-delete")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(auditDeleteItems.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForDelete() {

        createCoaAuditItems();
        List<CoaItemAuditDto.Delete> auditItems = List.of(
                new CoaItemAuditDto.Delete(1, "coaText", "coaText5"));
        List<CoaItemAuditDto.Response> auditDeleteItems = given().when()
                .contentType(ContentType.JSON)
                .body(auditItems)
                .post("/audit-delete")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(auditItems.size(), auditDeleteItems.size());

        for (int i = 0; i < auditItems.size(); i++) {
            CoaItemAuditEntity coaItemAuditEntity = coaItemAuditRepository.findById(auditDeleteItems.get(i).id());
            CoaItemAuditDto.Delete auditItem = auditItems.get(i);
            Assertions.assertEquals(auditItem.coaId(), coaItemAuditEntity.getCoaId());
            Assertions.assertEquals(auditItem.colName(), coaItemAuditEntity.getColName());
            Assertions.assertNull(coaItemAuditEntity.getNewValue());
            Assertions.assertEquals(AuditStatus.DELETED, coaItemAuditEntity.getAction());
            Assertions.assertEquals(auditItem.prevValue(), coaItemAuditEntity.getPrevValue());
        }
        verify(exchangeService, times(1)).getNameFromUsername(NAME);
    }

    @Test
    @TestSecurity(user = NAME)
    void testList() {

        List<CoaItemAuditEntity> coaItemAuditEntities = createCoaAuditItems();
        List<CoaItemAuditDto.Response> auditListItems = given().when()
                .get("/list")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(coaItemAuditEntities.size(), auditListItems.size());

        for (int i = 0; i < auditListItems.size(); i++) {
            CoaItemAuditEntity coaItemAuditEntity = coaItemAuditEntities.get(i);
            CoaItemAuditDto.Response auditListItem = auditListItems.get(i);
            Assertions.assertEquals(coaItemAuditEntity.getId(), auditListItem.id());
            Assertions.assertEquals(coaItemAuditEntity.getCoaId(), auditListItem.coaId());
            Assertions.assertEquals(coaItemAuditEntity.getColName(), auditListItem.colName());
            Assertions.assertEquals(
                    coaItemAuditEntity.getPrevValue() == null ? "NA" : coaItemAuditEntity.getPrevValue(),
                    auditListItem.prevValue());
            Assertions.assertEquals(coaItemAuditEntity.getNewValue() == null ? "NA" : coaItemAuditEntity.getNewValue(),
                    auditListItem.newValue());
            Assertions.assertEquals(coaItemAuditEntity.getAction(), auditListItem.action());
        }
        verify(exchangeService, times(1)).getNamesFromUsernames(List.of(USERNAME));
    }
}
