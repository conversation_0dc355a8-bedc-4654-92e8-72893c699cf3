package com.walnut.vegaspread.audit.resource.extraction;

import com.walnut.vegaspread.audit.entity.extraction.ExtractedRowCoaDataAuditEntity;
import com.walnut.vegaspread.audit.repository.extraction.ExtractedRowCoaDataAuditRepository;
import com.walnut.vegaspread.audit.service.common.ExchangeService;
import com.walnut.vegaspread.common.model.audit.coa.CoaItemAuditDto;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedRowCoaDataAuditDto;
import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import io.quarkus.liquibase.LiquibaseFactory;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import static com.walnut.vegaspread.audit.resource.CommonTestUtils.NAME;
import static com.walnut.vegaspread.audit.resource.CommonTestUtils.USERNAME;
import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;

@QuarkusTest
@TestHTTPEndpoint(ExtractedRowCoaDataAuditResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ExtractedRowCoaDataAuditResourceTest {

    @Inject
    ExtractedRowCoaDataAuditRepository extractedRowCoaDataAuditRepository;

    @InjectMock
    ExchangeService exchangeService;
    @Inject
    Flyway flyway;

    @Inject
    EntityManager entityManager;

    @Inject
    LiquibaseFactory liquibaseFactory;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Transactional
    List<ExtractedRowCoaDataAuditEntity> createExtractedRowCoaDataAuditItems() {
        List<ExtractedRowCoaDataAuditEntity> extractedRowCoaDataAuditEntities = new ArrayList<>();

        ExtractedRowCoaDataAuditEntity extractedRowCoaDataAuditEntity1 = new ExtractedRowCoaDataAuditEntity();
        extractedRowCoaDataAuditEntity1.setTableId(1)
                .setRowId((short) 1)
                .setColName("coa_id")
                .setPrevValue(null)
                .setNewValue(String.valueOf(1))
                .setAuditTime(LocalDateTime.now())
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAction(AuditStatus.CREATED);
        extractedRowCoaDataAuditEntities.add(extractedRowCoaDataAuditEntity1);

        ExtractedRowCoaDataAuditEntity extractedRowCoaDataAuditEntity2 = new ExtractedRowCoaDataAuditEntity();
        extractedRowCoaDataAuditEntity2.setTableId(1)
                .setRowId((short) 1)
                .setColName("coa_id")
                .setPrevValue(String.valueOf(1))
                .setNewValue(String.valueOf(2))
                .setAuditTime(LocalDateTime.now())
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAction(AuditStatus.UPDATED);
        extractedRowCoaDataAuditEntities.add(extractedRowCoaDataAuditEntity2);

        ExtractedRowCoaDataAuditEntity extractedRowCoaDataAuditEntity3 = new ExtractedRowCoaDataAuditEntity();
        extractedRowCoaDataAuditEntity3.setTableId(1)
                .setRowId((short) 1)
                .setColName("coa_id")
                .setPrevValue(String.valueOf(2))
                .setNewValue(String.valueOf(3))
                .setAuditTime(LocalDateTime.now())
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAction(AuditStatus.UPDATED);
        extractedRowCoaDataAuditEntities.add(extractedRowCoaDataAuditEntity3);

        extractedRowCoaDataAuditRepository.persist(extractedRowCoaDataAuditEntities);
        return extractedRowCoaDataAuditEntities;
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForCreateForEmptyList() {

        List<CoaItemAuditDto.Response> auditedCreateItems = given().when()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .post("/audit-create")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(auditedCreateItems.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForCreate() {
        List<ExtractedRowCoaDataAuditDto.Create> auditItems = List.of(
                new ExtractedRowCoaDataAuditDto.Create(1, 1, "coa_id", String.valueOf(2))
        );

        List<ExtractedRowCoaDataAuditDto.Response> auditedCreateItems = given().when()
                .contentType(ContentType.JSON)
                .body(auditItems)
                .post("/audit-create")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(auditItems.size(), auditedCreateItems.size());

        for (int i = 0; i < auditItems.size(); i++) {
            ExtractedRowCoaDataAuditEntity extractedRowCoaDataAuditEntity = extractedRowCoaDataAuditRepository.findById(
                    auditedCreateItems.get(i).id());
            ExtractedRowCoaDataAuditDto.Create auditItem = auditItems.get(i);
            Assertions.assertEquals(auditItem.tableId(), extractedRowCoaDataAuditEntity.getTableId());
            Assertions.assertEquals(auditItem.rowId().shortValue(), extractedRowCoaDataAuditEntity.getRowId());
            Assertions.assertEquals(auditItem.colName(), extractedRowCoaDataAuditEntity.getColName());
            Assertions.assertEquals(auditItem.newValue(), extractedRowCoaDataAuditEntity.getNewValue());
            Assertions.assertEquals(AuditStatus.CREATED, extractedRowCoaDataAuditEntity.getAction());
            if ("coa_id".equals(extractedRowCoaDataAuditEntity.getColName())) {
                Assertions.assertEquals(String.valueOf(1), extractedRowCoaDataAuditEntity.getPrevValue());
            } else {
                Assertions.assertNull(extractedRowCoaDataAuditEntity.getPrevValue());
            }
        }
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForUpdateForEmptyList() {

        List<ExtractedRowCoaDataAuditDto.Response> auditUpdateItems = given().when()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .post("/audit-update")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(auditUpdateItems.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForUpdate() {

        createExtractedRowCoaDataAuditItems();
        List<ExtractedRowCoaDataAuditDto.Update> auditItems = List.of(
                new ExtractedRowCoaDataAuditDto.Update(2, 1, "coa_id", String.valueOf(3),
                        String.valueOf(6)));
        List<ExtractedRowCoaDataAuditDto.Response> auditUpdateItems = given().when()
                .contentType(ContentType.JSON)
                .body(auditItems)
                .post("/audit-update")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(auditItems.size(), auditUpdateItems.size());

        for (int i = 0; i < auditItems.size(); i++) {
            ExtractedRowCoaDataAuditEntity extractedRowCoaDataAuditEntity = extractedRowCoaDataAuditRepository.findById(
                    auditUpdateItems.get(i).id());
            ExtractedRowCoaDataAuditDto.Update auditItem = auditItems.get(i);
            Assertions.assertEquals(auditItem.tableId(), extractedRowCoaDataAuditEntity.getTableId());
            Assertions.assertEquals(auditItem.rowId().shortValue(), extractedRowCoaDataAuditEntity.getRowId());
            Assertions.assertEquals(auditItem.colName(), extractedRowCoaDataAuditEntity.getColName());
            Assertions.assertEquals(auditItem.newValue(), extractedRowCoaDataAuditEntity.getNewValue());
            Assertions.assertEquals(AuditStatus.UPDATED, extractedRowCoaDataAuditEntity.getAction());
            Assertions.assertEquals(auditItem.prevValue(), extractedRowCoaDataAuditEntity.getPrevValue());
        }
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForDeleteForEmptyList() {

        List<ExtractedRowCoaDataAuditDto.Response> auditDeleteItems = given().when()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .post("/audit-delete")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(auditDeleteItems.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForDelete() {

        createExtractedRowCoaDataAuditItems();
        List<ExtractedRowCoaDataAuditDto.Delete> auditItems = List.of(
                new ExtractedRowCoaDataAuditDto.Delete(2, 1, "coa_id", String.valueOf(3)));
        List<ExtractedRowCoaDataAuditDto.Response> auditDeleteItems = given().when()
                .contentType(ContentType.JSON)
                .body(auditItems)
                .post("/audit-delete")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(auditItems.size(), auditDeleteItems.size());

        for (int i = 0; i < auditItems.size(); i++) {
            ExtractedRowCoaDataAuditEntity extractedRowCoaDataAuditEntity = extractedRowCoaDataAuditRepository.findById(
                    auditDeleteItems.get(i).id());
            ExtractedRowCoaDataAuditDto.Delete auditItem = auditItems.get(i);
            Assertions.assertEquals(auditItem.tableId(), extractedRowCoaDataAuditEntity.getTableId());
            Assertions.assertEquals(auditItem.rowId().shortValue(), extractedRowCoaDataAuditEntity.getRowId());
            Assertions.assertEquals(auditItem.colName(), extractedRowCoaDataAuditEntity.getColName());
            if ("coa_id".equals(extractedRowCoaDataAuditEntity.getColName())) {
                Assertions.assertEquals(String.valueOf(1), extractedRowCoaDataAuditEntity.getNewValue());
            } else {
                Assertions.assertNull(extractedRowCoaDataAuditEntity.getNewValue());
            }
            Assertions.assertEquals(AuditStatus.DELETED, extractedRowCoaDataAuditEntity.getAction());
            Assertions.assertEquals(auditItem.prevValue(), extractedRowCoaDataAuditEntity.getPrevValue());
        }
    }

    @Test
    @TestSecurity(user = NAME)
    void testList() {

        List<ExtractedRowCoaDataAuditEntity> extractedRowCoaDataAuditEntities = createExtractedRowCoaDataAuditItems();
        LocalDate filterDate = LocalDate.now();
        String filterUser = USERNAME;
        int pageSize = 3;
        int pageNumber = 1;
        String colName = "coa_id";
        ExtractedRowCoaDataAuditDto.ListForTables listTablesDto = new ExtractedRowCoaDataAuditDto.ListForTables(
                List.of(1, 2), colName, pageNumber, pageSize, filterUser, filterDate
        );
        ExtractedRowCoaDataAuditDto.ListResponse auditListResponse = given().when()
                .contentType(ContentType.JSON)
                .body(listTablesDto)
                .post("/list")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        List<ExtractedRowCoaDataAuditEntity> filteredDbEntities = extractedRowCoaDataAuditEntities.stream()
                .filter(extractedRowCoaDataAuditEntity ->
                        listTablesDto.tableIds().contains(extractedRowCoaDataAuditEntity.getTableId())
                                && colName.equals(extractedRowCoaDataAuditEntity.getColName())
                                && filterUser.equals(extractedRowCoaDataAuditEntity.getAuditedBy())
                                && filterDate.equals(extractedRowCoaDataAuditEntity.getAuditTime().toLocalDate()))
                .sorted(Comparator.comparing(ExtractedRowCoaDataAuditEntity::getAuditTime).reversed())
                .skip((pageNumber - 1) * pageSize)
                .limit(pageSize).toList();

        Assertions.assertEquals(listTablesDto.pageNumber(), auditListResponse.pageNumber());
        Assertions.assertEquals(listTablesDto.pageSize(), auditListResponse.pageSize());
        Assertions.assertEquals((int) Math.ceil((double) filteredDbEntities.size() / listTablesDto.pageSize()),
                auditListResponse.totalPages());

        List<ExtractedRowCoaDataAuditDto.Response> auditListItems = auditListResponse.extractedRowCoaDataAudits();
        Assertions.assertEquals(filteredDbEntities.size(), auditListItems.size());

        for (int i = 0; i < auditListItems.size(); i++) {
            ExtractedRowCoaDataAuditEntity extractedRowCoaDataAuditEntity = filteredDbEntities.get(i);
            ExtractedRowCoaDataAuditDto.Response auditListItem = auditListItems.get(i);
            Assertions.assertEquals(extractedRowCoaDataAuditEntity.getId(), auditListItem.id());
            Assertions.assertEquals(extractedRowCoaDataAuditEntity.getTableId(), auditListItem.tableId());
            Assertions.assertEquals(extractedRowCoaDataAuditEntity.getRowId().shortValue(), auditListItem.rowId());
            Assertions.assertEquals(extractedRowCoaDataAuditEntity.getColName(), auditListItem.colName());
            Assertions.assertEquals(extractedRowCoaDataAuditEntity.getPrevValue(), auditListItem.prevValue());
            Assertions.assertEquals(extractedRowCoaDataAuditEntity.getNewValue(), auditListItem.newValue());
            Assertions.assertEquals(extractedRowCoaDataAuditEntity.getAction(), auditListItem.action());
        }
    }
}
